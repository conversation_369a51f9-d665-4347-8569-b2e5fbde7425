@import url("https://fonts.googleapis.com/css2?family=Roboto&display=swap");

@font-face {
	font-family: "Gotham";
	src: url("fonts/GothamMedium.ttf") format("truetype"); 
}

html,body {
	margin: 0;
	padding: 0;
	color: #fff;
	cursor: default;
	font-size: 11px;
	overflow: hidden;
	font-family: "Roboto";
	box-sizing: border-box;
	background: transparent;
}

:focus { outline: 0; }
u { text-decoration: none; }
::-webkit-scrollbar { width: 0; }
::-webkit-scrollbar-track { background: transparent; }
::-webkit-scrollbar-thumb { background: transparent; }
::-webkit-scrollbar-thumb:hover { background: transparent; }
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
	-webkit-appearance: none;
	margin: 0;
}

#Spawn {
	width: 100vw;
	height: 100vh;
	z-index: 998;
	position: absolute;	
}

.SpawnCreate, .SpawnSelected, .SpawnLocation {
	margin: 50px;
	width: 300px;
	display: none;
	letter-spacing: 1px;
}

.SpawnSelectedNew {
	float: left;
	width: 49px;
	height: 50px;
	color: #715611;
	font-size: 40px;
	margin-top: 30px;
	text-align: center;
	margin-left: 125px;
	background: #fec026;
	border-radius: 100%;
}

.SpawnSelectedNew:first-child {
	margin-top: 0;
}

.SpawnSelectedCharacter {
	float: left;
	color: #999;
	width: 250px;
	font-size: 13px;
	line-height: 20px;
	padding: 20px 25px;
	position: relative;
	margin-bottom: 20px;
	background: #141414;
}

.SpawnSelectedCharacter b {
	color: #fff;
	font-weight: 300;
}

.SpawnSelectedCharacter i {
	top: 40px;
	right: 30px;
	color: #fec026;
	font-size: 40px;
	font-weight: 700;
	position: absolute;
}

.SpawnMessage {
	right: 50px;
	color: #999;
	bottom: 50px;
	width: 600px;
	padding: 40px;
	display: none;
	font-size: 12px;
	line-height: 22px;
	position: absolute;
	background: #141414;
}

.SpawnMessage span {
	color: #fec026;
	display: block;
	font-size: 30px;
	letter-spacing: 2px;
	margin-bottom: 25px;
	font-family: "Gotham";
	text-transform: uppercase;
}

.SpawnMessage b {
	color: #fff;
	font-weight: 100;
	text-transform: none;
}

.SpawnCreate input {
	border: 0;
	float: left;
	color: #fff;
	width: 300px;
	padding: 20px;
	font-size: 13px;
	margin-bottom: 10px;
	letter-spacing: 2px;
	background: #141414;
}

.SpawnCreate input::placeholder {
	color: #999;
}

.SpawnCreateButton {
	border: 0;
	float: left;
	color: #999;
	width: 145px;
	padding: 20px;
	margin-right: 10px;
	margin-bottom: 10px;
	letter-spacing: 2px;
	background: #141414;
}

.SpawnCreateButtonActive {
	color: #333;
	background: #ccc;
}

.MarginRight {
	margin-right: 0;
}

.SpawnCreateSubmit, .SpawnLocationSubmit {
	float: left;
	width: 260px;
	padding: 20px;
	color: #cbeddb;
	font-size: 16px;
	text-align: center;
	letter-spacing: 5px;
	margin-bottom: 10px;
	background: #2e6e4c;
	text-transform: uppercase;
}

.SpawnCreateCancel {
	float: left;
	width: 260px;
	color: #f9cfde;
	font-size: 16px;
	padding: 10px 20px;
	text-align: center;
	letter-spacing: 5px;
	background: #7c344d;
	text-transform: uppercase;
}

.SpawnCreateTitle {
	float: left;
	width: 300px;
	color: #fec026;
	font-size: 30px;
	letter-spacing: 2px;
	font-family: "Gotham";
	text-transform: uppercase;
	text-shadow: 1px 1px #000;
}

.SpawnCreateSubtitle {
	float: left;
	color: #fff;
	width: 298px;
	font-size: 9px;
	margin-left: 2px;
	margin-bottom: 20px;
	letter-spacing: 2px;
	text-transform: uppercase;
	text-shadow: 1px 1px #000;
}

.SpawnLocationBox {
	color: #999;
	float: left;
	width: 260px;
	padding: 20px;
	font-size: 16px;
	text-align: center;
	letter-spacing: 5px;
	margin-bottom: 10px;
	background: #141414;
	text-transform: uppercase;
}

.SpawnLocationBox:hover {
	color: #fff;
	background: #1e1e1e;
}