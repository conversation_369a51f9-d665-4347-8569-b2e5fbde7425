{"version": 3, "sources": ["webpack:///webpack/bootstrap", "webpack:///./src/App.vue", "webpack:///./src/App.vue?52ba", "webpack:///./src/main.js", "webpack:///./src/App.vue?02b4"], "names": ["webpackJsonpCallback", "data", "moduleId", "chunkId", "chunkIds", "moreModules", "executeModules", "i", "resolves", "length", "Object", "prototype", "hasOwnProperty", "call", "installedChunks", "push", "modules", "parentJsonpFunction", "shift", "deferredModules", "apply", "checkDeferredModules", "result", "deferredModule", "fulfilled", "j", "depId", "splice", "__webpack_require__", "s", "installedModules", "exports", "module", "l", "m", "c", "d", "name", "getter", "o", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "p", "jsonpArray", "window", "oldJsonpFunction", "slice", "id", "src", "class", "voice", "callInfo", "talking", "radioEnabled", "radioChannel", "usingRadio", "voiceModes", "voiceMode", "uiEnabled", "addEventListener", "event", "undefined", "JSON", "parse", "sound", "click", "document", "getElementById", "load", "volume", "play", "catch", "e", "fetch", "GetParentResourceName", "method", "__exports__", "render", "App", "mount"], "mappings": "aACE,SAASA,EAAqBC,GAQ7B,IAPA,IAMIC,EAAUC,EANVC,EAAWH,EAAK,GAChBI,EAAcJ,EAAK,GACnBK,EAAiBL,EAAK,GAIHM,EAAI,EAAGC,EAAW,GACpCD,EAAIH,EAASK,OAAQF,IACzBJ,EAAUC,EAASG,GAChBG,OAAOC,UAAUC,eAAeC,KAAKC,EAAiBX,IAAYW,EAAgBX,IACpFK,EAASO,KAAKD,EAAgBX,GAAS,IAExCW,EAAgBX,GAAW,EAE5B,IAAID,KAAYG,EACZK,OAAOC,UAAUC,eAAeC,KAAKR,EAAaH,KACpDc,EAAQd,GAAYG,EAAYH,IAG/Be,GAAqBA,EAAoBhB,GAE5C,MAAMO,EAASC,OACdD,EAASU,OAATV,GAOD,OAHAW,EAAgBJ,KAAKK,MAAMD,EAAiBb,GAAkB,IAGvDe,IAER,SAASA,IAER,IADA,IAAIC,EACIf,EAAI,EAAGA,EAAIY,EAAgBV,OAAQF,IAAK,CAG/C,IAFA,IAAIgB,EAAiBJ,EAAgBZ,GACjCiB,GAAY,EACRC,EAAI,EAAGA,EAAIF,EAAed,OAAQgB,IAAK,CAC9C,IAAIC,EAAQH,EAAeE,GACG,IAA3BX,EAAgBY,KAAcF,GAAY,GAE3CA,IACFL,EAAgBQ,OAAOpB,IAAK,GAC5Be,EAASM,EAAoBA,EAAoBC,EAAIN,EAAe,KAItE,OAAOD,EAIR,IAAIQ,EAAmB,GAKnBhB,EAAkB,CACrB,IAAO,GAGJK,EAAkB,GAGtB,SAASS,EAAoB1B,GAG5B,GAAG4B,EAAiB5B,GACnB,OAAO4B,EAAiB5B,GAAU6B,QAGnC,IAAIC,EAASF,EAAiB5B,GAAY,CACzCK,EAAGL,EACH+B,GAAG,EACHF,QAAS,IAUV,OANAf,EAAQd,GAAUW,KAAKmB,EAAOD,QAASC,EAAQA,EAAOD,QAASH,GAG/DI,EAAOC,GAAI,EAGJD,EAAOD,QAKfH,EAAoBM,EAAIlB,EAGxBY,EAAoBO,EAAIL,EAGxBF,EAAoBQ,EAAI,SAASL,EAASM,EAAMC,GAC3CV,EAAoBW,EAAER,EAASM,IAClC3B,OAAO8B,eAAeT,EAASM,EAAM,CAAEI,YAAY,EAAMC,IAAKJ,KAKhEV,EAAoBe,EAAI,SAASZ,GACX,qBAAXa,QAA0BA,OAAOC,aAC1CnC,OAAO8B,eAAeT,EAASa,OAAOC,YAAa,CAAEC,MAAO,WAE7DpC,OAAO8B,eAAeT,EAAS,aAAc,CAAEe,OAAO,KAQvDlB,EAAoBmB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQlB,EAAoBkB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,kBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKxC,OAAOyC,OAAO,MAGvB,GAFAvB,EAAoBe,EAAEO,GACtBxC,OAAO8B,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOlB,EAAoBQ,EAAEc,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRtB,EAAoB0B,EAAI,SAAStB,GAChC,IAAIM,EAASN,GAAUA,EAAOiB,WAC7B,WAAwB,OAAOjB,EAAO,YACtC,WAA8B,OAAOA,GAEtC,OADAJ,EAAoBQ,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRV,EAAoBW,EAAI,SAASgB,EAAQC,GAAY,OAAO9C,OAAOC,UAAUC,eAAeC,KAAK0C,EAAQC,IAGzG5B,EAAoB6B,EAAI,GAExB,IAAIC,EAAaC,OAAO,gBAAkBA,OAAO,iBAAmB,GAChEC,EAAmBF,EAAW3C,KAAKsC,KAAKK,GAC5CA,EAAW3C,KAAOf,EAClB0D,EAAaA,EAAWG,QACxB,IAAI,IAAItD,EAAI,EAAGA,EAAImD,EAAWjD,OAAQF,IAAKP,EAAqB0D,EAAWnD,IAC3E,IAAIU,EAAsB2C,EAI1BzC,EAAgBJ,KAAK,CAAC,EAAE,kBAEjBM,K,qICrJP,eAAoD,SAA7CyC,GAAG,WAAWC,IAAI,oB,WACzB,eAAsD,SAA/CD,GAAG,YAAYC,IAAI,qB,kBACEC,MAAM,a,gDAHnC,eAcO,aAbN,EACA,EACW,EAAAC,MAAe,W,iBAA1B,eAUM,MAVN,EAUM,CATuB,IAAnB,EAAAA,MAAMC,U,iBAAf,eAEI,K,MAF4BF,MAAK,wBAAa,EAAAC,MAAME,WAAW,WAEnE,I,sBACS,EAAAF,MAAMG,cAAuC,IAAvB,EAAAH,MAAMI,c,iBAArC,eAEI,K,MAFsDL,MAAK,wBAAa,EAAAC,MAAMK,c,eAC9E,EAAAL,MAAMI,cAAe,gBACzB,I,sBACS,EAAAJ,MAAMM,WAAiB,Q,iBAAhC,eAEI,K,MAF+BP,MAAK,wBAAa,EAAAC,MAAME,W,eACvD,EAAAF,MAAMM,WAAW,EAAAN,MAAMO,WAAW,IAAK,YAC3C,I,iDAOY,OACdnC,KAAM,MACN,QACC,MAAM4B,EAAQ,eAAS,CACtBQ,WAAW,EACXF,WAAY,GACZC,UAAW,EACXH,aAAc,EACdD,cAAc,EACdE,YAAY,EACZJ,SAAU,EACVC,SAAS,IAsDV,OAlDAR,OAAOe,iBAAiB,WAAW,SAASC,GAC3C,MAAM1E,EAAO0E,EAAM1E,KAMnB,QAJuB2E,IAAnB3E,EAAKwE,YACRR,EAAMQ,UAAYxE,EAAKwE,gBAGAG,IAApB3E,EAAKsE,WAA0B,CAClCN,EAAMM,WAAaM,KAAKC,MAAM7E,EAAKsE,YAEnC,IAAIA,EAAa,IAAIN,EAAMM,YAC3BA,EAAWxD,KAAK,CAAC,EAAK,WACtBkD,EAAMM,WAAaA,EA2BpB,QAxBuBK,IAAnB3E,EAAKuE,YACRP,EAAMO,UAAYvE,EAAKuE,gBAGEI,IAAtB3E,EAAKoE,eACRJ,EAAMI,aAAepE,EAAKoE,mBAGDO,IAAtB3E,EAAKmE,eACRH,EAAMG,aAAenE,EAAKmE,mBAGLQ,IAAlB3E,EAAKiE,WACRD,EAAMC,SAAWjE,EAAKiE,eAGCU,IAApB3E,EAAKqE,YAA4BrE,EAAKqE,aAAeL,EAAMK,aAC9DL,EAAMK,WAAarE,EAAKqE,iBAGHM,IAAjB3E,EAAKkE,SAA2BF,EAAMK,aAC1CL,EAAME,QAAUlE,EAAKkE,SAGlBlE,EAAK8E,OAASd,EAAMG,cAAuC,IAAvBH,EAAMI,aAAoB,CACjE,IAAIW,EAAQC,SAASC,eAAejF,EAAK8E,OAEzCC,EAAMG,OACNH,EAAMI,OAASnF,EAAKmF,OACpBJ,EAAMK,OAAOC,MAAOC,WAItBC,MAAM,WAAWC,kCAAmC,CAAEC,OAAQ,SAEvD,CAAEzB,W,iCC9EX,MAAM0B,EAA2B,IAAgB,EAAQ,CAAC,CAAC,SAASC,KAErD,QCNf,eAAUC,GAAKC,MAAM,S,kCCHrB", "file": "js/app.js", "sourcesContent": [" \t// install a JSONP callback for chunk loading\n \tfunction webpackJsonpCallback(data) {\n \t\tvar chunkIds = data[0];\n \t\tvar moreModules = data[1];\n \t\tvar executeModules = data[2];\n\n \t\t// add \"moreModules\" to the modules object,\n \t\t// then flag all \"chunkIds\" as loaded and fire callback\n \t\tvar moduleId, chunkId, i = 0, resolves = [];\n \t\tfor(;i < chunkIds.length; i++) {\n \t\t\tchunkId = chunkIds[i];\n \t\t\tif(Object.prototype.hasOwnProperty.call(installedChunks, chunkId) && installedChunks[chunkId]) {\n \t\t\t\tresolves.push(installedChunks[chunkId][0]);\n \t\t\t}\n \t\t\tinstalledChunks[chunkId] = 0;\n \t\t}\n \t\tfor(moduleId in moreModules) {\n \t\t\tif(Object.prototype.hasOwnProperty.call(moreModules, moduleId)) {\n \t\t\t\tmodules[moduleId] = moreModules[moduleId];\n \t\t\t}\n \t\t}\n \t\tif(parentJsonpFunction) parentJsonpFunction(data);\n\n \t\twhile(resolves.length) {\n \t\t\tresolves.shift()();\n \t\t}\n\n \t\t// add entry modules from loaded chunk to deferred list\n \t\tdeferredModules.push.apply(deferredModules, executeModules || []);\n\n \t\t// run deferred modules when all chunks ready\n \t\treturn checkDeferredModules();\n \t};\n \tfunction checkDeferredModules() {\n \t\tvar result;\n \t\tfor(var i = 0; i < deferredModules.length; i++) {\n \t\t\tvar deferredModule = deferredModules[i];\n \t\t\tvar fulfilled = true;\n \t\t\tfor(var j = 1; j < deferredModule.length; j++) {\n \t\t\t\tvar depId = deferredModule[j];\n \t\t\t\tif(installedChunks[depId] !== 0) fulfilled = false;\n \t\t\t}\n \t\t\tif(fulfilled) {\n \t\t\t\tdeferredModules.splice(i--, 1);\n \t\t\t\tresult = __webpack_require__(__webpack_require__.s = deferredModule[0]);\n \t\t\t}\n \t\t}\n\n \t\treturn result;\n \t}\n\n \t// The module cache\n \tvar installedModules = {};\n\n \t// object to store loaded and loading chunks\n \t// undefined = chunk not loaded, null = chunk preloaded/prefetched\n \t// Promise = chunk loading, 0 = chunk loaded\n \tvar installedChunks = {\n \t\t\"app\": 0\n \t};\n\n \tvar deferredModules = [];\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n \tvar jsonpArray = window[\"webpackJsonp\"] = window[\"webpackJsonp\"] || [];\n \tvar oldJsonpFunction = jsonpArray.push.bind(jsonpArray);\n \tjsonpArray.push = webpackJsonpCallback;\n \tjsonpArray = jsonpArray.slice();\n \tfor(var i = 0; i < jsonpArray.length; i++) webpackJsonpCallback(jsonpArray[i]);\n \tvar parentJsonpFunction = oldJsonpFunction;\n\n\n \t// add entry module to deferred list\n \tdeferredModules.push([0,\"chunk-vendors\"]);\n \t// run deferred modules when ready\n \treturn checkDeferredModules();\n", "<template>\r\n\t<body>\r\n\t\t<audio id=\"audio_on\" src=\"mic_click_on.ogg\"></audio>\r\n\t\t<audio id=\"audio_off\" src=\"mic_click_off.ogg\"></audio>\r\n\t\t<div v-if=\"voice.uiEnabled\" class=\"voiceInfo\">\r\n\t\t\t<p v-if=\"voice.callInfo !== 0\" :class=\"{ talking: voice.talking }\">\r\n\t\t\t\t[Call]\r\n\t\t\t</p>\r\n\t\t\t<p v-if=\"voice.radioEnabled && voice.radioChannel !== 0\" :class=\"{ talking: voice.usingRadio }\">\r\n\t\t\t\t{{ voice.radioChannel }} Mhz [Radio]\r\n\t\t\t</p>\r\n\t\t\t<p v-if=\"voice.voiceModes.length\" :class=\"{ talking: voice.talking }\">\r\n\t\t\t\t{{ voice.voiceModes[voice.voiceMode][1] }} [Range]\r\n\t\t\t</p>\r\n\t\t</div>\r\n\t</body>\r\n</template>\r\n\r\n<script>\r\nimport { reactive } from \"vue\";\r\nexport default {\r\n\tname: \"App\",\r\n\tsetup() {\r\n\t\tconst voice = reactive({\r\n\t\t\tuiEnabled: true,\r\n\t\t\tvoiceModes: [],\r\n\t\t\tvoiceMode: 0,\r\n\t\t\tradioChannel: 0,\r\n\t\t\tradioEnabled: true,\r\n\t\t\tusingRadio: false,\r\n\t\t\tcallInfo: 0,\r\n\t\t\ttalking: false,\r\n\t\t});\r\n\r\n\t\t// stops from toggling voice at the end of talking\r\n\t\twindow.addEventListener(\"message\", function(event) {\r\n\t\t\tconst data = event.data;\r\n\r\n\t\t\tif (data.uiEnabled !== undefined) {\r\n\t\t\t\tvoice.uiEnabled = data.uiEnabled\r\n\t\t\t}\r\n\r\n\t\t\tif (data.voiceModes !== undefined) {\r\n\t\t\t\tvoice.voiceModes = JSON.parse(data.voiceModes);\r\n\t\t\t\t// Push our own custom type for modes that have their range changed\r\n\t\t\t\tlet voiceModes = [...voice.voiceModes]\r\n\t\t\t\tvoiceModes.push([0.0, \"Custom\"])\r\n\t\t\t\tvoice.voiceModes = voiceModes\r\n\t\t\t}\r\n\r\n\t\t\tif (data.voiceMode !== undefined) {\r\n\t\t\t\tvoice.voiceMode = data.voiceMode;\r\n\t\t\t}\r\n\r\n\t\t\tif (data.radioChannel !== undefined) {\r\n\t\t\t\tvoice.radioChannel = data.radioChannel;\r\n\t\t\t}\r\n\r\n\t\t\tif (data.radioEnabled !== undefined) {\r\n\t\t\t\tvoice.radioEnabled = data.radioEnabled;\r\n\t\t\t}\r\n\r\n\t\t\tif (data.callInfo !== undefined) {\r\n\t\t\t\tvoice.callInfo = data.callInfo;\r\n\t\t\t}\r\n\r\n\t\t\tif (data.usingRadio !== undefined && data.usingRadio !== voice.usingRadio) {\r\n\t\t\t\tvoice.usingRadio = data.usingRadio;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif ((data.talking !== undefined) && !voice.usingRadio) {\r\n\t\t\t\tvoice.talking = data.talking;\r\n\t\t\t}\r\n\r\n\t\t\tif (data.sound && voice.radioEnabled && voice.radioChannel !== 0) {\r\n\t\t\t\tlet click = document.getElementById(data.sound);\r\n\t\t\t\t// discard these errors as its usually just a 'uncaught promise' from two clicks happening too fast.\r\n\t\t\t\tclick.load();\r\n\t\t\t\tclick.volume = data.volume;\r\n\t\t\t\tclick.play().catch((e) => {});\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\tfetch(`https://${GetParentResourceName()}/uiReady`, { method: 'POST' });\r\n\r\n\t\treturn { voice };\r\n\t}\r\n};\r\n</script>\r\n\r\n<style>\r\n.voiceInfo {\r\n\tfont-family: Avenir, Helvetica, Arial, sans-serif;\r\n\tposition: fixed;\r\n\ttext-align: right;\r\n\tbottom: 5px;\r\n\tpadding: 0;\r\n\tright: 5px;\r\n\tfont-size: 12px;\r\n\tfont-weight: bold;\r\n\tcolor: rgb(148, 150, 151);\r\n\t/* https://stackoverflow.com/questions/4772906/css-is-it-possible-to-add-a-black-outline-around-each-character-in-text */\r\n\ttext-shadow: 1.25px 0 0 #000, 0 -1.25px 0 #000, 0 1.25px 0 #000,\r\n\t\t-1.25px 0 0 #000;\r\n}\r\n.talking {\r\n\tcolor: rgba(255, 255, 255, 0.822);\r\n}\r\np {\r\n\tmargin: 0;\r\n}\r\n</style>\r\n", "import { render } from \"./App.vue?vue&type=template&id=0cf90768\"\nimport script from \"./App.vue?vue&type=script&lang=js\"\nexport * from \"./App.vue?vue&type=script&lang=js\"\n\nimport \"./App.vue?vue&type=style&index=0&id=0cf90768&lang=css\"\n\nimport exportComponent from \"C:\\\\FXServer\\\\server-data\\\\resources\\\\pma-voice\\\\voice-ui\\\\node_modules\\\\.pnpm\\\\vue-loader@16.8.3_webpack@4.46.0\\\\node_modules\\\\vue-loader\\\\dist\\\\exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render]])\n\nexport default __exports__", "import { createApp } from 'vue'\r\nimport App from './App.vue'\r\n\r\ncreateApp(App).mount('#app')\r\n", "export * from \"-!../node_modules/.pnpm/mini-css-extract-plugin@0.9.0_webpack@4.46.0/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../node_modules/.pnpm/css-loader@3.6.0_webpack@4.46.0/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/.pnpm/vue-loader@16.8.3_webpack@4.46.0/node_modules/vue-loader/dist/stylePostLoader.js!../node_modules/.pnpm/postcss-loader@3.0.0/node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/.pnpm/cache-loader@4.1.0_webpack@4.46.0/node_modules/cache-loader/dist/cjs.js??ref--1-0!../node_modules/.pnpm/vue-loader@16.8.3_webpack@4.46.0/node_modules/vue-loader/dist/index.js??ref--1-1!./App.vue?vue&type=style&index=0&id=0cf90768&lang=css\""], "sourceRoot": ""}