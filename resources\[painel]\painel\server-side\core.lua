-----------------------------------------------------------------------------------------------------------------------------------------
-- VRP
-----------------------------------------------------------------------------------------------------------------------------------------
local Tunnel = module("vrp","lib/Tunnel")
local Proxy = module("vrp","lib/Proxy")
vRP = Proxy.getInterface("vRP")
vRPC = Tunnel.getInterface("vRP")
-----------------------------------------------------------------------------------------------------------------------------------------
-- CONNECTION
-----------------------------------------------------------------------------------------------------------------------------------------
painel = {}
Tunnel.bindInterface("painel", painel)
Proxy.addInterface("painel", painel)
-----------------------------------------------------------------------------------------------------------------------------------------
-- VARIABLES
-----------------------------------------------------------------------------------------------------------------------------------------
local warnings = {}
local spectating = {}
-----------------------------------------------------------------------------------------------------------------------------------------
-- PAINEL:CHECKPERMISSION
-----------------------------------------------------------------------------------------------------------------------------------------
function painel.checkPermission()
    local source = source
    local Passport = vRP.Passport(source)
    
    if vRP.HasPermission(Passport, "Admin") then
        local data = {
            passport = Passport,
            name = vRP.Identity(Passport).name.." "..vRP.Identity(Passport).name2,
            level = vRP.GetUserType(Passport, "Admin") or "Admin"
        }
        TriggerClientEvent("painel:Toggle", source, data)
    else
        TriggerClientEvent("Notify", source, "negado", "Você não tem permissão.", 5000)
    end
end
-----------------------------------------------------------------------------------------------------------------------------------------
-- PAINEL:GETPLAYERS
-----------------------------------------------------------------------------------------------------------------------------------------
function painel.getPlayers()
    local source = source
    local Passport = vRP.Passport(source)
    
    if not vRP.HasPermission(Passport, "Admin") then
        return {}
    end
    
    local players = {}
    local allPlayers = vRP.Players()
    
    for k, v in pairs(allPlayers) do
        local identity = vRP.Identity(k)
        if identity then
            table.insert(players, {
                id = k,
                source = v,
                name = identity.name.." "..identity.name2,
                phone = identity.phone,
                bank = identity.bank,
                coords = GetEntityCoords(GetPlayerPed(v))
            })
        end
    end
    
    return players
end
-----------------------------------------------------------------------------------------------------------------------------------------
-- PAINEL:TELEPORTTO
-----------------------------------------------------------------------------------------------------------------------------------------
function painel.teleportToPlayer(targetId)
    local source = source
    local Passport = vRP.Passport(source)
    
    if not vRP.HasPermission(Passport, "Admin") then
        return
    end
    
    local targetSource = vRP.Source(targetId)
    if targetSource then
        local coords = GetEntityCoords(GetPlayerPed(targetSource))
        vRP.Teleport(source, coords.x, coords.y, coords.z)
        TriggerClientEvent("Notify", source, "sucesso", "Teleportado para o jogador.", 5000)
    else
        TriggerClientEvent("Notify", source, "negado", "Jogador não encontrado.", 5000)
    end
end
-----------------------------------------------------------------------------------------------------------------------------------------
-- PAINEL:TELEPORTPLAYERTOME
-----------------------------------------------------------------------------------------------------------------------------------------
function painel.teleportPlayerToMe(targetId)
    local source = source
    local Passport = vRP.Passport(source)
    
    if not vRP.HasPermission(Passport, "Admin") then
        return
    end
    
    local targetSource = vRP.Source(targetId)
    if targetSource then
        local coords = GetEntityCoords(GetPlayerPed(source))
        vRP.Teleport(targetSource, coords.x, coords.y, coords.z)
        TriggerClientEvent("Notify", source, "sucesso", "Jogador teleportado até você.", 5000)
    else
        TriggerClientEvent("Notify", source, "negado", "Jogador não encontrado.", 5000)
    end
end
-----------------------------------------------------------------------------------------------------------------------------------------
-- PAINEL:KICKPLAYER
-----------------------------------------------------------------------------------------------------------------------------------------
function painel.kickPlayer(targetId, reason)
    local source = source
    local Passport = vRP.Passport(source)
    
    if not vRP.HasPermission(Passport, "Admin") then
        return
    end
    
    local targetSource = vRP.Source(targetId)
    if targetSource then
        vRP.Kick(targetSource, reason or "Expulso por um administrador")
        TriggerClientEvent("Notify", source, "sucesso", "Jogador expulso.", 5000)
        
        -- Log
        TriggerEvent("Discord", "Painel", "**Admin:** "..vRP.Identity(Passport).name.." "..vRP.Identity(Passport).name2.."\n**Ação:** Kick\n**Jogador:** "..vRP.Identity(targetId).name.." "..vRP.Identity(targetId).name2.."\n**Motivo:** "..(reason or "Não informado"), 15158332)
    else
        TriggerClientEvent("Notify", source, "negado", "Jogador não encontrado.", 5000)
    end
end
-----------------------------------------------------------------------------------------------------------------------------------------
-- PAINEL:BANPLAYER
-----------------------------------------------------------------------------------------------------------------------------------------
function painel.banPlayer(targetId, time, reason)
    local source = source
    local Passport = vRP.Passport(source)
    
    if not vRP.HasPermission(Passport, "Admin") then
        return
    end
    
    local targetIdentity = vRP.Identity(targetId)
    if targetIdentity then
        vRP.Query("banneds/InsertBanned", { license = targetIdentity.license, time = parseInt(time) })
        
        local targetSource = vRP.Source(targetId)
        if targetSource then
            vRP.Kick(targetSource, "Banido: "..(reason or "Não informado"))
        end
        
        TriggerClientEvent("Notify", source, "sucesso", "Jogador banido por "..time.." dias.", 5000)
        
        -- Log
        TriggerEvent("Discord", "Painel", "**Admin:** "..vRP.Identity(Passport).name.." "..vRP.Identity(Passport).name2.."\n**Ação:** Ban\n**Jogador:** "..targetIdentity.name.." "..targetIdentity.name2.."\n**Tempo:** "..time.." dias\n**Motivo:** "..(reason or "Não informado"), 15158332)
    else
        TriggerClientEvent("Notify", source, "negado", "Jogador não encontrado.", 5000)
    end
end
-----------------------------------------------------------------------------------------------------------------------------------------
-- PAINEL:GIVEITEM
-----------------------------------------------------------------------------------------------------------------------------------------
function painel.giveItem(targetId, item, amount)
    local source = source
    local Passport = vRP.Passport(source)
    
    if not vRP.HasPermission(Passport, "Admin") then
        return
    end
    
    local targetSource = vRP.Source(targetId)
    if targetSource then
        if vRP.GenerateItem(targetId, item, parseInt(amount), true) then
            TriggerClientEvent("Notify", source, "sucesso", "Item entregue com sucesso.", 5000)
            TriggerClientEvent("Notify", targetSource, "sucesso", "Você recebeu "..amount.."x "..item, 5000)
            
            -- Log
            TriggerEvent("Discord", "Painel", "**Admin:** "..vRP.Identity(Passport).name.." "..vRP.Identity(Passport).name2.."\n**Ação:** Give Item\n**Jogador:** "..vRP.Identity(targetId).name.." "..vRP.Identity(targetId).name2.."\n**Item:** "..item.."\n**Quantidade:** "..amount, 3447003)
        else
            TriggerClientEvent("Notify", source, "negado", "Erro ao entregar o item.", 5000)
        end
    else
        TriggerClientEvent("Notify", source, "negado", "Jogador não encontrado.", 5000)
    end
end
-----------------------------------------------------------------------------------------------------------------------------------------
-- PAINEL:GIVEMONEY
-----------------------------------------------------------------------------------------------------------------------------------------
function painel.giveMoney(targetId, amount)
    local source = source
    local Passport = vRP.Passport(source)
    
    if not vRP.HasPermission(Passport, "Admin") then
        return
    end
    
    local targetSource = vRP.Source(targetId)
    if targetSource then
        vRP.GiveBank(targetId, parseInt(amount))
        TriggerClientEvent("Notify", source, "sucesso", "Dinheiro entregue com sucesso.", 5000)
        TriggerClientEvent("Notify", targetSource, "sucesso", "Você recebeu $"..vRP.Format(amount), 5000)
        
        -- Log
        TriggerEvent("Discord", "Painel", "**Admin:** "..vRP.Identity(Passport).name.." "..vRP.Identity(Passport).name2.."\n**Ação:** Give Money\n**Jogador:** "..vRP.Identity(targetId).name.." "..vRP.Identity(targetId).name2.."\n**Valor:** $"..vRP.Format(amount), 3447003)
    else
        TriggerClientEvent("Notify", source, "negado", "Jogador não encontrado.", 5000)
    end
end
-----------------------------------------------------------------------------------------------------------------------------------------
-- PAINEL:SETGROUP
-----------------------------------------------------------------------------------------------------------------------------------------
function painel.setGroup(targetId, group, level)
    local source = source
    local Passport = vRP.Passport(source)

    if not vRP.HasPermission(Passport, "Admin") then
        return
    end

    local targetSource = vRP.Source(targetId)
    if targetSource then
        vRP.SetPermission(targetId, group, parseInt(level))
        TriggerClientEvent("Notify", source, "sucesso", "Grupo definido com sucesso.", 5000)
        TriggerClientEvent("Notify", targetSource, "sucesso", "Você recebeu o grupo "..group, 5000)

        -- Log
        TriggerEvent("Discord", "Painel", "**Admin:** "..vRP.Identity(Passport).name.." "..vRP.Identity(Passport).name2.."\n**Ação:** Set Group\n**Jogador:** "..vRP.Identity(targetId).name.." "..vRP.Identity(targetId).name2.."\n**Grupo:** "..group.."\n**Nível:** "..level, 3447003)
    else
        TriggerClientEvent("Notify", source, "negado", "Jogador não encontrado.", 5000)
    end
end
-----------------------------------------------------------------------------------------------------------------------------------------
-- PAINEL:REMOVEGROUP
-----------------------------------------------------------------------------------------------------------------------------------------
function painel.removeGroup(targetId, group)
    local source = source
    local Passport = vRP.Passport(source)

    if not vRP.HasPermission(Passport, "Admin") then
        return
    end

    local targetSource = vRP.Source(targetId)
    if targetSource then
        vRP.RemovePermission(targetId, group)
        TriggerClientEvent("Notify", source, "sucesso", "Grupo removido com sucesso.", 5000)
        TriggerClientEvent("Notify", targetSource, "aviso", "Você perdeu o grupo "..group, 5000)

        -- Log
        TriggerEvent("Discord", "Painel", "**Admin:** "..vRP.Identity(Passport).name.." "..vRP.Identity(Passport).name2.."\n**Ação:** Remove Group\n**Jogador:** "..vRP.Identity(targetId).name.." "..vRP.Identity(targetId).name2.."\n**Grupo:** "..group, 15105570)
    else
        TriggerClientEvent("Notify", source, "negado", "Jogador não encontrado.", 5000)
    end
end
-----------------------------------------------------------------------------------------------------------------------------------------
-- PAINEL:REVIVEPLAYER
-----------------------------------------------------------------------------------------------------------------------------------------
function painel.revivePlayer(targetId)
    local source = source
    local Passport = vRP.Passport(source)

    if not vRP.HasPermission(Passport, "Admin") then
        return
    end

    local targetSource = vRP.Source(targetId)
    if targetSource then
        vRP.Revive(targetSource, 200)
        TriggerClientEvent("Notify", source, "sucesso", "Jogador revivido.", 5000)
        TriggerClientEvent("Notify", targetSource, "sucesso", "Você foi revivido por um administrador.", 5000)

        -- Log
        TriggerEvent("Discord", "Painel", "**Admin:** "..vRP.Identity(Passport).name.." "..vRP.Identity(Passport).name2.."\n**Ação:** Revive\n**Jogador:** "..vRP.Identity(targetId).name.." "..vRP.Identity(targetId).name2, 3447003)
    else
        TriggerClientEvent("Notify", source, "negado", "Jogador não encontrado.", 5000)
    end
end
-----------------------------------------------------------------------------------------------------------------------------------------
-- PAINEL:SENDANNOUNCEMENT
-----------------------------------------------------------------------------------------------------------------------------------------
function painel.sendAnnouncement(message, time)
    local source = source
    local Passport = vRP.Passport(source)

    if not vRP.HasPermission(Passport, "Admin") then
        return
    end

    TriggerClientEvent("Notify", -1, "importante", message, parseInt(time) * 1000)
    TriggerClientEvent("Notify", source, "sucesso", "Anúncio enviado.", 5000)

    -- Log
    TriggerEvent("Discord", "Painel", "**Admin:** "..vRP.Identity(Passport).name.." "..vRP.Identity(Passport).name2.."\n**Ação:** Anúncio\n**Mensagem:** "..message, 3447003)
end
-----------------------------------------------------------------------------------------------------------------------------------------
-- PAINEL:SENDWARNING
-----------------------------------------------------------------------------------------------------------------------------------------
function painel.sendWarning(targetId, message)
    local source = source
    local Passport = vRP.Passport(source)

    if not vRP.HasPermission(Passport, "Admin") then
        return
    end

    local targetSource = vRP.Source(targetId)
    if targetSource then
        local warningId = #warnings + 1
        warnings[warningId] = {
            id = warningId,
            admin = vRP.Identity(Passport).name.." "..vRP.Identity(Passport).name2,
            target = vRP.Identity(targetId).name.." "..vRP.Identity(targetId).name2,
            targetId = targetId,
            message = message,
            date = os.date("%d/%m/%Y %H:%M")
        }

        TriggerClientEvent("Notify", targetSource, "negado", "ADVERTÊNCIA: "..message, 10000)
        TriggerClientEvent("Notify", source, "sucesso", "Advertência enviada.", 5000)

        -- Log
        TriggerEvent("Discord", "Painel", "**Admin:** "..vRP.Identity(Passport).name.." "..vRP.Identity(Passport).name2.."\n**Ação:** Advertência\n**Jogador:** "..vRP.Identity(targetId).name.." "..vRP.Identity(targetId).name2.."\n**Mensagem:** "..message, 15105570)
    else
        TriggerClientEvent("Notify", source, "negado", "Jogador não encontrado.", 5000)
    end
end
