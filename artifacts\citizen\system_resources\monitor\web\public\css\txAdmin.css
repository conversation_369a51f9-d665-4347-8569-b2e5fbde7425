/* Footer */
.credit-links {
    font-weight: 600;
    color: #23282c;
}


/* Missing tooltip stuff */
.tooltip[data-popper-placement^=top] .tooltip-arrow, .tooltip[data-popper-placement^=bottom] .tooltip-arrow {
    width: .8rem;
    height: .4rem;
}
.tooltip[data-popper-placement^=bottom] .tooltip-arrow {
    top: 0;
}
.tooltip .tooltip-arrow {
    position: absolute;
    display: block;
}
.tooltip[data-popper-placement^=top], .tooltip[data-popper-placement^=bottom] {
    padding: .4rem 0;
}
.tooltip[data-popper-placement^=bottom] .tooltip-arrow::before {
    bottom: 0;
    border-width: 0 .4rem .4rem;
    border-bottom-color: #000015;
}
.tooltip .tooltip-arrow::before {
    position: absolute;
    content: "";
    border-color: transparent;
    border-style: solid;
}


/* General responsivity */
@media (min-width: 1200px){
    .dashboard-card{
        max-width: 460px !important;
    }
}
@media (min-width: 768px){
    .mw-col8{
        min-width: 660px;
    }
}
@media (min-width: 576px){
    .mw-col6{
        min-width: 460px;
    }
}
@media (max-width: 560px){
    .main .container-fluid {
        padding: 0 5px;
    }
    .main-top-spacer {
        margin-top: 0.5em;
    }
}
.tableActions{
    white-space: nowrap;
    text-align: right !important;
}


/* General tweeks */
.fix-pill-form {
    margin-top: 0.3rem;
    margin-bottom: 0;
}
option:disabled {
    background-color: #f0f3f5;
}
.form-control{
    color: #5e6168;
}
.form-control:focus {
    color: #373a40;
}
.form-control::placeholder {
    color: #848d96;
}
.c-callout{
    background-color: #fff;
}
.c-sidebar .c-sidebar-nav-link.c-active {
    border-left: aquamarine solid 3px;
}
[data-notify="message"] pre{
	white-space: normal;
}
.blur-input:not(:focus):not(:placeholder-shown) {
    color: transparent !important;
    text-shadow: 0 0 5px rgba(0,0,0,0.5) !important;
}
.attentionText {
    /* background-color: #a7d4ff !important; */
}
.permsCheckbox:checked+label {
    font-weight: bold;
}
.btn-inline {
    padding: 0.1rem 0.2rem;
    font-size: .75rem;
    line-height: 1.5;
    border-radius: 0.2rem;
    vertical-align: text-bottom;
}
.btn-inline-sm {
    padding: 0.1rem 0.2rem;
    font-size: .85rem;
    font-weight: 600;
    line-height: 1;
    border-radius: 0.2rem;
    vertical-align: text-bottom;
}

.table td, .table th {
    vertical-align: unset; /* it was top */
}
.table-sm td, .table-sm th {
    padding: 0.3rem 0.6rem; /* it was 0.3rem all sides */
}


/* Theme dependant */
.show-dark-mode {
    display: none;
}
.theme--dark .show-dark-mode {
    display: inline-block;
}
.show-light-mode {
    display: inline-block;
}
.theme--dark .show-light-mode {
    display: none;
}

/* Console/Log marks */
/* https://flatuicolors.com/palette/us */
.consoleMark-cmd {background-color: #ffeaa7;}
.consoleMark-error {background-color: #ffbfa7;}
.consoleMark-info {background-color: #a7d4ff;}
.consoleMark-ok {background-color: #a7ffae;}
mark {background-color: #ffeaa7;}


/* Playerlist */
@media (min-width: 1199.98px) {
    .playerlist{
        /* 56+36 = 92 +4*/
        position: relative;
        height: calc(100vh - 96px);
    }
}
@media (max-width: 1199.98px) {
    .playerlist-sidebar-max{
        margin-right: 0 !important;
    }
    .playerlist-sidebar{
        top: 56px !important;
    }
    .playerlist{
        /* 56+56+36 = 148*/
        position: relative;
        height: calc(100vh - 152px);
    }
}
.playerlist-sidebar{
    z-index: 1029;
    right: 0;
}
.plheader{
    padding-top: 17px;
    height: 56px;
    background-color: #eeeeee !important;
}
.plheader-label{
    font-size: 1.15em;
    color: #8f98a5 !important;
}
.plheader-bignum{
    margin-top: -26px;
    font-size: 3.25em;
    color: #a2a9b3 !important;
    font-weight: 600;
}
.playerlist-search{
    padding: .25rem 1rem;
}
.playerlist>.player {
    cursor: pointer;
    padding: .25rem 1rem;
}
.playerlist>.player:hover {
    background-color: #F2F2F2;
}
.playerlist>.player>.netid {
    font-family: Consolas, monaco, monospace;
}
.playerlist>.player>.pname {
    font-weight: 700;
    padding-left: 5px;
}


/* Scrollable PRE */
.thin-scroll {
    overflow: scroll;
    margin-right: 6px;
}
.thin-scroll::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    margin-left: -10px;
    appearance: none;
}
.thin-scroll::-webkit-scrollbar-track {
    background-color: white;
    border-right: 1px solid #f2f2f2;
    border-left: 1px solid #f2f2f2;
}
.thin-scroll::-webkit-scrollbar-thumb {
    background-color: #f0f0f0;
    background-clip: content-box;
    border-color: transparent;
    border-radius: 6px;
}
.thin-scroll:hover::-webkit-scrollbar-thumb{
    background-color: #cfcfcf;
}


/* Player info modal */
#modPlayer > .modal-dialog {
    max-width: 620px;
}

#modPlayerTitle {
    overflow: hidden;
    text-overflow: ellipsis;
}

#modPlayer .nav-link > i {
    padding-right: 0.75rem;
}
.nav-pills .nav-link.nav-link-disabled{
    color: #cfcfcf;
}
.nav-pills .nav-link.nav-link-red{
    color: #e55353;
}
.nav-pills .nav-link.nav-link-red.active{
    color: #fff;
    background-color: #e55353;
}
#modPlayerMain-notes{
    background-color: #fcf8e3;
}
#modPlayerMain-notes:disabled{
    background-color: #f4f3eb;
}
.player-history-entry{
    padding: .15rem .25rem;
    margin-bottom: 2px !important;
}

/* Player DB actions (players page + modal) */
/* NOTE: this should not be here, but we will refactor the Web UI soon anyways */
.logEntry{
    color: #768192;
    padding: .15rem 0.55rem;
    margin-bottom: 4px !important;
}
.logEntry:hover{
    background-color: #ebedef;
}
.logEntry:hover button{
    color: #fff;
    background-color: #636f83;
}
.logEntry small{
    margin-top: .25em;
}
.logEntryClickable{
    cursor: pointer;
}
.txInlineBtn{
    border-radius: .25rem;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    letter-spacing: 0.1rem;
}
.txActionsBtn{
    font-size: 85%;
    font-weight: 700;
    padding: .25rem !important;
    vertical-align: top;
}


/* HR Separator */
.hrsep {
    display: flex;
    align-items: center;
    text-align: center;
    margin-top: 1rem;
    margin-bottom: 1rem;
    border: 0;
    color: #b1afaf;
}
.hrsep-small {
    margin-top: 0.25rem;
    margin-bottom: 0.25rem;
}
.hrsep::before, .hrsep::after {
    content: '';
    flex: 1;
    border-top: 1px solid #e6e6e6;
}
.hrsep::before {
    margin-right: .25em;
}
.hrsep::after {
    margin-left: .25em;
}


/* Spinner */
.txSpinner,
.txSpinner:before,
.txSpinner:after {
    background: #00c88f;
    -webkit-animation: txSpinnerLoad 1s infinite ease-in-out;
    animation: txSpinnerLoad 1s infinite ease-in-out;
    width: 1em;
    height: 4em;
}

.txSpinner {
    color: #00c88f;
    text-indent: -9999em;
    margin: 88px auto;
    position: relative;
    font-size: 11px;
    -webkit-transform: translateZ(0);
    -ms-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-animation-delay: -0.16s;
    animation-delay: -0.16s;
}

.txSpinner:before,
.txSpinner:after {
    position: absolute;
    top: 0;
    content: '';
}

.txSpinner:before {
    left: -1.5em;
    -webkit-animation-delay: -0.32s;
    animation-delay: -0.32s;
}

.txSpinner:after {
    left: 1.5em;
}

@-webkit-keyframes txSpinnerLoad {

    0%,
    80%,
    100% {
        box-shadow: 0 0;
        height: 4em;
    }

    40% {
        box-shadow: 0 -2em;
        height: 5em;
    }
}

@keyframes txSpinnerLoad {

    0%,
    80%,
    100% {
        box-shadow: 0 0;
        height: 4em;
    }

    40% {
        box-shadow: 0 -2em;
        height: 5em;
    }
}

/* Necessary for the scrollbar  */
.c-body {
    scrollbar-width: thin;
    scrollbar-gutter: stable;
}

.c-main {
    padding-top: unset;
}

.thin-scroll {
    overflow-x: hidden;
    overflow-y: auto;
}
pre.thin-scroll{
    white-space: pre-wrap;
}


/* AutoScroll Toggler */
#autoScrollDiv {
    position: absolute; 
    bottom: calc((24px * 3) + 12px);
    right: calc(1rem + 24px + 18px);
}
#autoScrollDiv a {
    padding-top: 80px;
}
#autoScrollDiv a span {
    position: absolute;
    top: 0;
    left: 50%;
    width: 24px;
    height: 24px;
    margin-left: -12px;
    border-left: 1px solid #fff;
    border-bottom: 1px solid #fff;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
    -webkit-animation: autoScrollBtnAnimation 2s infinite;
    animation: autoScrollBtnAnimation 2s infinite;
    opacity: 0;
    box-sizing: border-box;
}
#autoScrollDiv a span:nth-of-type(1) {
    -webkit-animation-delay: 0s;
    animation-delay: 0s;
}
#autoScrollDiv a span:nth-of-type(2) {
    top: 16px;
    -webkit-animation-delay: .15s;
    animation-delay: .15s;
}
#autoScrollDiv a span:nth-of-type(3) {
    top: 32px;
    -webkit-animation-delay: .3s;
    animation-delay: .3s;
}
@-webkit-keyframes autoScrollBtnAnimation {
    0% {
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}
@keyframes autoScrollBtnAnimation {
    0% {
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}

.switch-lg {
    width: 65px;
}

.switch-lg .c-switch-input:checked~.c-switch-slider::before {
    -webkit-transform:translateX(40px);
    transform:translateX(40px)
}
.switch-lg .c-switch-slider::after {
    width: 65%;
}


/* Shadcn patches for the light mode */
body {
    background-color: #0000 !important;
}

.card {
    --tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1),
        0 1px 2px -1px rgba(0, 0, 0, 0.1);
    --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color),
        0 1px 2px -1px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
