<?xml version="1.0" encoding="UTF-8"?>

<CPedModelInfo__PersonalityDataList>
	<MovementModeUnholsterData>
		<Item>
			<Name>UNHOLSTER_UNARMED</Name>
			<UnholsterClips>
				<Item>
					<Weapons>
						<Item>WEAPON_PARAFAL</Item>
					</Weapons>
					<Clip>unarmed_holster_2h</Clip>
				</Item>
			</UnholsterClips>
		</Item>
		<Item>
			<Name>UNHOLSTER_2H_MELEE</Name>
			<UnholsterClips>
				<Item>
					<Weapons>
						<Item>WEAPON_PARAFAL</Item>
					</Weapons>
					<Clip>2h_melee_holster_2h</Clip>
				</Item>
			</UnholsterClips>
		</Item>
		<Item>
			<Name>UNHOLSTER_1H</Name>
			<UnholsterClips>
				<Item>
					<Weapons>
						<Item>WEAPON_PARAFAL</Item>
					</Weapons>
					<Clip>1h_holster_2h</Clip>
				</Item>
			</UnholsterClips>
		</Item>
		<Item>
			<Name>UNHOLSTER_2H</Name>
			<UnholsterClips>
				<Item>
					<Weapons>
						<Item>WEAPON_PARAFAL</Item>
					</Weapons>
					<Clip>2h_holster_2h</Clip>
				</Item>
			</UnholsterClips>
		</Item>
		<Item>
			<Name>UNHOLSTER_MINIGUN</Name>
			<UnholsterClips>
				<Item>
					<Weapons>
						<Item>WEAPON_PARAFAL</Item>
					</Weapons>
					<Clip>mini_holster_2h</Clip>
				</Item>
			</UnholsterClips>
		</Item>
		<Item>
			<Name>UNHOLSTER_UNARMED_STEALTH</Name>
			<UnholsterClips>
				<Item>
					<Weapons>
						<Item>WEAPON_PARAFAL</Item>
					</Weapons>
					<Clip>unarmed_holster_2h</Clip>
				</Item>
			</UnholsterClips>
		</Item>
		<Item>
			<Name>UNHOLSTER_2H_MELEE_STEALTH</Name>
			<UnholsterClips>
				<Item>
					<Weapons>
						<Item>WEAPON_PARAFAL</Item>
					</Weapons>
					<Clip>unarmed_holster_2h</Clip>
				</Item>
			</UnholsterClips>
		</Item>
		<Item>
			<Name>UNHOLSTER_1H_STEALTH</Name>
			<UnholsterClips>
				<Item>
					<Weapons>
						<Item>WEAPON_PARAFAL</Item>
					</Weapons>
					<Clip>1h_holster_2h</Clip>
				</Item>
			</UnholsterClips>
		</Item>
		<Item>
			<Name>UNHOLSTER_2H_STEALTH</Name>
			<UnholsterClips>
				<Item>
					<Weapons>
						<Item>WEAPON_PARAFAL</Item>
					</Weapons>
					<Clip>2h_holster_2h</Clip>
				</Item>
			</UnholsterClips>
		</Item>
	</MovementModeUnholsterData>
	<MovementModes>
		<Item>
			<Name>DEFAULT_ACTION</Name>
			<MovementModes>
				<Item>
					<Item>
						<Weapons>
							<Item>WEAPON_PARAFAL</Item>
						</Weapons>
						<ClipSets>
							<Item>
								<MovementClipSetId>move_action@p_m_zero@armed@core</MovementClipSetId>
								<WeaponClipSetId>move_action@p_m_zero@armed@2H@upper</WeaponClipSetId>
								<WeaponClipFilterId>UpperbodyAndIk_filter</WeaponClipFilterId>
								<UpperBodyShadowExpressionEnabled value="true" />
								<UpperBodyFeatheredLeanEnabled value="true" />
								<UseWeaponAnimsForGrip value="false" />
								<UseLeftHandIk value="true" />
								<IdleTransitions>
									<Item>MOVE_ACTION@GENERIC@TRANS@2H</Item>
								</IdleTransitions>
								<IdleTransitionBlendOutTime value="0.500000"/>
								<UnholsterClipSetId>MOVE_ACTION@P_M_ZERO@HOLSTER</UnholsterClipSetId>
								<UnholsterClipData ref="UNHOLSTER_2H" />
							</Item>
						</ClipSets>
					</Item>
				</Item>
				<Item>
					<Item>
						<Weapons>
							<Item>WEAPON_PARAFAL</Item>
						</Weapons>
						<ClipSets>
							<Item>
								<MovementClipSetId>move_stealth@p_m_zero@unarmed@core</MovementClipSetId>
								<WeaponClipSetId>move_stealth@p_m_zero@2h@upper</WeaponClipSetId>
								<WeaponClipFilterId>UpperbodyAndIk_filter</WeaponClipFilterId>
								<UpperBodyShadowExpressionEnabled value="true" />
								<UpperBodyFeatheredLeanEnabled value="true" />
								<UseWeaponAnimsForGrip value="false" />
								<UseLeftHandIk value="true" />
								<IdleTransitions>
									<Item>move_stealth@generic@trans@2h</Item>
								</IdleTransitions>
								<IdleTransitionBlendOutTime value="0.500000"/>
								<UnholsterClipSetId>move_stealth@p_m_zero@holster</UnholsterClipSetId>
								<UnholsterClipData ref="UNHOLSTER_2H_STEALTH" />
							</Item>
						</ClipSets>
					</Item>
				</Item>
			</MovementModes>
		</Item>
		<Item>
			<Name>MP_FEMALE_ACTION</Name>
			<MovementModes>
				<Item>
					<Item>
						<Weapons>
							<Item>WEAPON_PARAFAL</Item>
						</Weapons>
						<ClipSets>
							<Item>
								<MovementClipSetId>move_action@p_m_zero@armed@core</MovementClipSetId>
								<WeaponClipSetId>move_action@mp_female@armed@2H@upper</WeaponClipSetId>
								<WeaponClipFilterId>UpperbodyAndIk_filter</WeaponClipFilterId>
								<UpperBodyShadowExpressionEnabled value="true" />
								<UpperBodyFeatheredLeanEnabled value="true" />
								<UseWeaponAnimsForGrip value="false" />
								<UseLeftHandIk value="true" />
								<IdleTransitions>
									<Item>MOVE_ACTION@MP_FEMALE@ARMED@2H@TRANS</Item>
								</IdleTransitions>
								<IdleTransitionBlendOutTime value="0.500000"/>
								<UnholsterClipSetId>MOVE_ACTION@P_M_ZERO@HOLSTER</UnholsterClipSetId>
								<UnholsterClipData ref="UNHOLSTER_2H" />
							</Item>
						</ClipSets>
					</Item>
				</Item>
				<Item>
					<Item>
						<Weapons>
							<Item>WEAPON_PARAFAL</Item>
						</Weapons>
						<ClipSets>
							<Item>
								<MovementClipSetId>move_stealth@p_m_zero@unarmed@core</MovementClipSetId>
								<WeaponClipSetId>move_stealth@p_m_zero@2h@upper</WeaponClipSetId>
								<WeaponClipFilterId>UpperbodyAndIk_filter</WeaponClipFilterId>
								<UpperBodyShadowExpressionEnabled value="true" />
								<UpperBodyFeatheredLeanEnabled value="true" />
								<UseWeaponAnimsForGrip value="false" />
								<UseLeftHandIk value="true" />
								<IdleTransitions>
									<Item>MOVE_STEALTH@MP_FEMALE@2H@TRANS</Item>
								</IdleTransitions>
								<IdleTransitionBlendOutTime value="0.500000"/>
								<UnholsterClipSetId>move_stealth@p_m_zero@holster</UnholsterClipSetId>
								<UnholsterClipData ref="UNHOLSTER_2H_STEALTH" />
							</Item>
						</ClipSets>
					</Item>
				</Item>
			</MovementModes>
		</Item>
		<Item>
			<Name>MICHAEL_ACTION</Name>
			<MovementModes>
				<Item>
					<Item>
						<Weapons>
							<Item>WEAPON_PARAFAL</Item>
						</Weapons>
						<ClipSets>
							<Item>
								<MovementClipSetId>move_action@p_m_zero@armed@core</MovementClipSetId>
								<WeaponClipSetId>move_action@p_m_zero@armed@2H@upper</WeaponClipSetId>
								<WeaponClipFilterId>UpperbodyAndIk_filter</WeaponClipFilterId>
								<UpperBodyShadowExpressionEnabled value="true" />
								<UpperBodyFeatheredLeanEnabled value="true" />
								<UseWeaponAnimsForGrip value="false" />
								<UseLeftHandIk value="true" />
								<IdleTransitions>
									<Item>MOVE_ACTION@P_M_ZERO@ARMED@2H@TRANS@A</Item>
								</IdleTransitions>
								<IdleTransitionBlendOutTime value="0.500000"/>
								<UnholsterClipSetId>MOVE_ACTION@P_M_ZERO@HOLSTER</UnholsterClipSetId>
								<UnholsterClipData ref="UNHOLSTER_2H" />
							</Item>
						</ClipSets>
					</Item>
				</Item>
				<Item>
					<Item>
						<Weapons>
							<Item>WEAPON_PARAFAL</Item>
						</Weapons>
						<ClipSets>
							<Item>
								<MovementClipSetId>move_stealth@p_m_zero@unarmed@core</MovementClipSetId>
								<WeaponClipSetId>move_stealth@p_m_zero@2h@upper</WeaponClipSetId>
								<WeaponClipFilterId>UpperbodyAndIk_filter</WeaponClipFilterId>
								<UpperBodyShadowExpressionEnabled value="true" />
								<UpperBodyFeatheredLeanEnabled value="true" />
								<UseWeaponAnimsForGrip value="false" />
								<UseLeftHandIk value="true" />
								<IdleTransitions>
									<Item>move_stealth@p_m_zero@2h@trans@a</Item>
								</IdleTransitions>
								<IdleTransitionBlendOutTime value="0.500000"/>
								<UnholsterClipSetId>move_stealth@p_m_zero@holster</UnholsterClipSetId>
								<UnholsterClipData ref="UNHOLSTER_2H_STEALTH" />
							</Item>
						</ClipSets>
					</Item>
				</Item>
			</MovementModes>
		</Item>
		<Item>
			<Name>FRANKLIN_ACTION</Name>
			<MovementModes>
				<Item>
					<Item>
						<Weapons>
							<Item>WEAPON_PARAFAL</Item>
						</Weapons>
						<ClipSets>
							<Item>
								<MovementClipSetId>move_action@p_m_one@armed@core</MovementClipSetId>
								<WeaponClipSetId>move_action@P_M_ONE@armed@2H@upper</WeaponClipSetId>
								<WeaponClipFilterId>UpperbodyAndIk_filter</WeaponClipFilterId>
								<UpperBodyShadowExpressionEnabled value="true" />
								<UpperBodyFeatheredLeanEnabled value="true" />
								<UseWeaponAnimsForGrip value="false" />
								<UseLeftHandIk value="true" />
								<IdleTransitions>
									<Item>MOVE_ACTION@P_M_ONE@ARMED@2H@TRANS@A</Item>
								</IdleTransitions>
								<IdleTransitionBlendOutTime value="0.500000"/>
								<UnholsterClipSetId>MOVE_ACTION@P_M_ONE@HOLSTER</UnholsterClipSetId>
								<UnholsterClipData ref="UNHOLSTER_2H" />
							</Item>
						</ClipSets>
					</Item>
				</Item>
				<Item>
					<Item>
						<Weapons>
							<Item>WEAPON_PARAFAL</Item>
						</Weapons>
						<ClipSets>
							<Item>
								<MovementClipSetId>move_stealth@p_m_one@unarmed@core</MovementClipSetId>
								<WeaponClipSetId>move_stealth@p_m_one@2h@upper</WeaponClipSetId>
								<WeaponClipFilterId>UpperbodyAndIk_filter</WeaponClipFilterId>
								<UpperBodyShadowExpressionEnabled value="true" />
								<UpperBodyFeatheredLeanEnabled value="true" />
								<UseWeaponAnimsForGrip value="false" />
								<UseLeftHandIk value="true" />
								<IdleTransitions>
									<Item>move_stealth@p_m_one@2h@trans@a</Item>
								</IdleTransitions>
								<IdleTransitionBlendOutTime value="0.500000"/>
								<UnholsterClipSetId>MOVE_STEALTH@P_M_ONE@HOLSTER</UnholsterClipSetId>
								<UnholsterClipData ref="UNHOLSTER_2H_STEALTH" />
							</Item>
						</ClipSets>
					</Item>
				</Item>
			</MovementModes>
		</Item>
		<Item>
			<Name>TREVOR_ACTION</Name>
			<MovementModes>
				<Item>
					<Item>
						<Weapons>
							<Item>WEAPON_PARAFAL</Item>
						</Weapons>
						<ClipSets>
							<Item>
								<MovementClipSetId>move_action@p_m_two@armed@core</MovementClipSetId>
								<WeaponClipSetId>move_action@p_m_two@armed@2H@upper</WeaponClipSetId>
								<WeaponClipFilterId>UpperbodyAndIk_filter</WeaponClipFilterId>
								<UpperBodyShadowExpressionEnabled value="true" />
								<UpperBodyFeatheredLeanEnabled value="true" />
								<UseWeaponAnimsForGrip value="false" />
								<UseLeftHandIk value="true" />
								<IdleTransitions>
									<Item>MOVE_ACTION@p_m_two@ARMED@2H@TRANS@A</Item>
								</IdleTransitions>
								<IdleTransitionBlendOutTime value="0.500000"/>
								<UnholsterClipSetId>MOVE_ACTION@p_m_two@HOLSTER</UnholsterClipSetId>
								<UnholsterClipData ref="UNHOLSTER_2H" />
							</Item>
						</ClipSets>
					</Item>
				</Item>
				<Item>
					<Item>
						<Weapons>
							<Item>WEAPON_PARAFAL</Item>
						</Weapons>
						<ClipSets>
							<Item>
								<MovementClipSetId>move_stealth@p_m_two@unarmed@core</MovementClipSetId>
								<WeaponClipSetId>move_stealth@p_m_two@2h@upper</WeaponClipSetId>
								<WeaponClipFilterId>UpperbodyAndIk_filter</WeaponClipFilterId>
								<UpperBodyShadowExpressionEnabled value="true" />
								<UpperBodyFeatheredLeanEnabled value="true" />
								<UseWeaponAnimsForGrip value="false" />
								<UseLeftHandIk value="true" />
								<IdleTransitions>
									<Item>move_stealth@p_m_two@2h@trans@a</Item>
								</IdleTransitions>
								<IdleTransitionBlendOutTime value="0.500000"/>
								<UnholsterClipSetId>MOVE_STEALTH@P_M_TWO@HOLSTER</UnholsterClipSetId>
								<UnholsterClipData ref="UNHOLSTER_2H_STEALTH" />
							</Item>
						</ClipSets>
					</Item>
				</Item>
			</MovementModes>
		</Item>
	</MovementModes>
</CPedModelInfo__PersonalityDataList>