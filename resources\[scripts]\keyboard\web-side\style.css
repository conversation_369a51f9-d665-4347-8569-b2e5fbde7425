::-webkit-scrollbar { width: 0; }
::-webkit-scrollbar-track { background: transparent; }
::-webkit-scrollbar-thumb { background: transparent; }
::-webkit-scrollbar-thumb:hover { background: transparent; }
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button { -webkit-appearance: none; }
:focus { outline: 0; }

body {
	margin: 0;
	padding: 0;
	color: #fff;
	font-family: "Roboto";
}

* {
	overflow: hidden;
	user-select: none;
	box-sizing: border-box;
}

.Keyboard {
	width: 100vw;
	height: 100vh;
	display: none;
	overflow: hidden;
	align-items: center;
	justify-content: center;
}

.main-wrapper {
	width: 300px;
	padding: 20px 40px 40px;
	background: rgba(15,15,15,.75);
}

.body {
	width: 100%;
	height: 100%;
	margin: auto;
	display: flex;
	font-size: 14px;
	flex-direction: column;
}

.body label {
	color: #ccc;
	margin-top: 20px;
	margin-bottom: 15px;
	letter-spacing: 1px;
}

.body input {
	color: #333;
	border: none;
	padding: 10px;
	background: #ccc;
}

.body textarea {
	color: #333;
	border: none;
	resize: none;
	padding: 10px;
	height: 100px;
	background: #fff;
}

.footer {
	display: flex;
	padding-top: 20px;
	position: relative;
	align-items: center;
	justify-content: center;
}

.btn {
	border: 0;
	color: #715611;
	width: 230px;
	padding: 20px 0;
	margin-top: 10px;
	letter-spacing: 1px;
	background: #fec026;
}