<%- await include('parts/header.ejs', locals) %>

<div class="row justify-content-md-center">
    <div class="col-lg-10 col-xl-8">
        <div class="alert alert-info text-center" role="alert">
            <span class="font-weight-bold">
                This is a page exclusively for advanced users. <br>
                Do not expect any support in our Discord if you mess with something on this page. 
            </span>
            <br>
            This is also an undocumented feature for a reason: nothing here is expected to work properly and things might be added or removed for any reason.
        </div>
    </div>
</div>


<div class="row justify-content-md-center">
    
    <div class="col-lg-8 col-xl-6 mw-col6">
        <div class="card card-accent-danger" style="min-height: 160px;">
            <div class="card-header font-weight-bold">Random buttons, knobs and data:</div>
            <div class="card-body text-center">
                <!-- Verbosity -->
                <span class="text-muted">
                    With verbosity enabled, you will see more detailed information on the terminal. <br>
                    Good to help getting information on errors. <br>
                </span>
                <% if (verbosityEnabled) { %>
                    <button class="btn btn-md btn-danger mt-2" onclick="doChangeVerbosity(false)">
                        Disable Verbosity
                    </button>
                <% } else { %>
                    <button class="btn btn-md btn-success mt-2" onclick="doChangeVerbosity(true)">
                        Enable Verbosity
                    </button>
                <% } %>
                <hr>

                <!-- Profile Monitor -->
                <span class="text-muted">
                    This will execute the profiler in the Monitor for 5 seconds. <br>
                    Required the Server to be started for showing the profiler URL. <br>
                </span>
                <button class="btn btn-md btn-outline-dark mt-2" onclick="doProfileMonitor()">Profile Monitor</button>
            </div>
        </div>
    </div>

    <div class="col-lg-8 col-xl-6 mw-col6 text-center">
        <div class="form-group row justify-content-center">
            <div class="col-sm-9">
                <input class="form-control" type="text" id="magicActionInput" value="perform_magic">
            </div>
            <div class="col-sm-3">
                <button class="btn btn-md btn-outline-dark w-100" onclick="doMagicTrick()">Magic Button</button>
            </div>
        </div>
        <pre class="alert alert-secondary mt-2 text-left" id="magicPre">What will happen when its pressed?!</pre>
    </div>
</div>


<div class="row justify-content-md-center">
    <marquee behavior="" direction="">
        <img src="img/txSnaily2anim_320.png" alt="Not A Meme" class="img-fluid">
    </marquee>
</div>

<%- await include('parts/footer.ejs', locals) %>

<script>
    //============================================== Magic
    function doMagicTrick(){
        let data = {
            action: document.getElementById('magicActionInput').value.trim(),
            parameter: false
        }
        const magicPre = document.getElementById("magicPre");
        magicPre.innerText = 'doing magic';
        const notify = $.notify({ message: '<p class="text-center">Performing Magic...</p>' }, {});
        txAdminAPI({
            type: "POST",
            url: '/advanced',
            timeout: REQ_TIMEOUT_LONG,
            data: data,
            success: function (data) {
                if(data.type === 'success'){
                    notify.update('progress', 0);
                    notify.update('type', 'success');
                    notify.update('message', 'Check the magic box below...');
                    magicPre.innerText = data.message;
                }else{
                    notify.update('progress', 0);
                    notify.update('type', data.type);
                    notify.update('message', data.message);
                }
            },
            error: function (xmlhttprequest, textstatus, message) {
                notify.update('progress', 0);
                notify.update('type', 'danger');
                notify.update('message', message);
            }
        });
    }
        

    //============================================== Verbosity
    function doChangeVerbosity(desiredState) {
        let data = {
            action: 'change_verbosity',
            parameter: desiredState
        }
        const notify = $.notify({ message: '<p class="text-center">Executing Command...</p>' }, {});
        txAdminAPI({
            type: "POST",
            url: '/advanced',
            data: data,
            success: function (data) {
                if (checkApiLogoutRefresh(data)) return;
                notify.update('progress', 0);
                notify.update('type', data.type);
                notify.update('message', data.message);
            },
            error: function (xmlhttprequest, textstatus, message) {
                notify.update('progress', 0);
                notify.update('type', 'danger');
                notify.update('message', message);
            }
        });
    }


    //============================================== Profiler
    function doProfileMonitor() {
        const notify = $.notify({ message: '<p class="text-center">Executing Command...</p>' }, {});
        txAdminAPI({
            type: "POST",
            url: '/fxserver/commands',
            data: {
                action: 'profile_monitor',
                parameter: false
            },
            success: function (data) {
                notify.update('progress', 0);
                notify.update('type', data.type);
                notify.update('message', data.message);
            },
            error: function (xmlhttprequest, textstatus, message) {
                notify.update('progress', 0);
                notify.update('type', 'danger');
                notify.update('message', message);
            }
        });
    }
</script>
