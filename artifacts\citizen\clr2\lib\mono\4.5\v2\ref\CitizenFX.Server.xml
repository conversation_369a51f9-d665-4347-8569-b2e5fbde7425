<?xml version="1.0"?>
<doc>
    <assembly>
        <name>CitizenFX.Server</name>
    </assembly>
    <members>
        <member name="M:CitizenFX.Server.Game.GenerateHashASCII(System.String)">
            <inheritdoc cref="!:GenerateHash"/>
            <remarks>Using a non-ASCII string has undefined behavior.</remarks>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.AddBlipForArea(System.Single,System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Adds a rectangular blip for the specified coordinates/area.
            
            It is recommended to use [SET_BLIP_ROTATION](#\_0xF87683CDF73C3F6E) and [SET_BLIP_COLOUR](#\_0x03D7FB09E75D6B7E) to make the blip not rotate along with the camera.
            
            By default, the blip will show as a *regular* blip with the specified color/sprite if it is outside of the minimap view.
            
            Example image:
            ![minimap](https://i.imgur.com/qLbXWcQ.png)
            ![big map](https://i.imgur.com/0j7O7Rh.png)
            
            (Native name is *likely* to actually be ADD_BLIP_FOR_AREA, but due to the usual reasons this can't be confirmed)
            </summary>
            <param name="x">
            The X coordinate of the center of the blip.
            </param>
            <param name="y">
            The Y coordinate of the center of the blip.
            </param>
            <param name="z">
            The Z coordinate of the center of the blip.
            </param>
            <param name="width">
            The width of the blip.
            </param>
            <param name="height">
            The height of the blip.
            </param>
            <returns>
            A handle to the blip.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.N_0xce5d0e5e315db238(System.Single,System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Adds a rectangular blip for the specified coordinates/area.
            
            It is recommended to use [SET_BLIP_ROTATION](#\_0xF87683CDF73C3F6E) and [SET_BLIP_COLOUR](#\_0x03D7FB09E75D6B7E) to make the blip not rotate along with the camera.
            
            By default, the blip will show as a *regular* blip with the specified color/sprite if it is outside of the minimap view.
            
            Example image:
            ![minimap](https://i.imgur.com/qLbXWcQ.png)
            ![big map](https://i.imgur.com/0j7O7Rh.png)
            
            (Native name is *likely* to actually be ADD_BLIP_FOR_AREA, but due to the usual reasons this can't be confirmed)
            </summary>
            <param name="x">
            The X coordinate of the center of the blip.
            </param>
            <param name="y">
            The Y coordinate of the center of the blip.
            </param>
            <param name="z">
            The Z coordinate of the center of the blip.
            </param>
            <param name="width">
            The width of the blip.
            </param>
            <param name="height">
            The height of the blip.
            </param>
            <returns>
            A handle to the blip.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.AddBlipForCoord(System.Single,System.Single,System.Single)">
            <summary>
            Creates a blip for the specified coordinates. You can use `SET_BLIP_` natives to change the blip.
            </summary>
            <param name="x">
            The X coordinate to create the blip on.
            </param>
            <param name="y">
            The Y coordinate.
            </param>
            <param name="z">
            The Z coordinate.
            </param>
            <returns>
            A blip handle.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.AddBlipForEntity(System.Int32)">
            <summary>
            Create a blip that by default is red (enemy), you can use [SET_BLIP_AS_FRIENDLY](#\_0xC6F43D0E) to make it blue (friend).\
            Can be used for objects, vehicles and peds.
            
            Example of enemy:
            ![enemy](https://i.imgur.com/fl78svv.png)
            Example of friend:
            ![friend](https://i.imgur.com/Q16ho5d.png)
            </summary>
            <param name="entity">
            The entity handle to create the blip.
            </param>
            <returns>
            A blip handle.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.AddBlipForRadius(System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Create a blip with a radius for the specified coordinates (it doesnt create the blip sprite, so you need to use [AddBlipCoords](#\_0xC6F43D0E))
            
            Example image:
            ![example](https://i.imgur.com/9hQl3DB.png)
            </summary>
            <param name="posX">
            The x position of the blip (you can also send a vector3 instead of the bulk coordinates)
            </param>
            <param name="posY">
            The y position of the blip (you can also send a vector3 instead of the bulk coordinates)
            </param>
            <param name="posZ">
            The z position of the blip (you can also send a vector3 instead of the bulk coordinates)
            </param>
            <param name="radius">
            The number that defines the radius of the blip circle
            </param>
            <returns>
            The blip handle that can be manipulated with every `SetBlip` natives
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.AddConvarChangeListener(CitizenFX.Core.CString,System.Delegate)">
            <summary>
            Adds a listener for Console Variable changes.
            
            The function called expects to match the following signature:
            
            ```ts
            function ConVarChangeListener(conVarName: string, reserved: any);
            ```
            
            *   **conVarName**: The ConVar that changed.
            *   **reserved**: Currently unused.
            </summary>
            <param name="conVarFilter">
            The Console Variable to listen for, this can be a pattern like "test:\*", or null for any
            </param>
            <param name="handler">
            The handler function.
            </param>
            <returns>
            A cookie to remove the change handler.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.AddPedDecorationFromHashes(System.Int32,System.UInt32,System.UInt32)">
            <summary>
            Applies an Item from a PedDecorationCollection to a ped. These include tattoos and shirt decals.
            collection - PedDecorationCollection filename hash
            overlay - Item name hash
            Example:
            Entry inside "mpbeach_overlays.xml" -
            &lt;Item&gt;
              &lt;uvPos x="0.500000" y="0.500000" /&gt;
              &lt;scale x="0.600000" y="0.500000" /&gt;
              &lt;rotation value="0.000000" /&gt;
              &lt;nameHash&gt;FM_Hair_Fuzz&lt;/nameHash&gt;
              &lt;txdHash&gt;mp_hair_fuzz&lt;/txdHash&gt;
              &lt;txtHash&gt;mp_hair_fuzz&lt;/txtHash&gt;
              &lt;zone&gt;ZONE_HEAD&lt;/zone&gt;
              &lt;type&gt;TYPE_TATTOO&lt;/type&gt;
              &lt;faction&gt;FM&lt;/faction&gt;
              &lt;garment&gt;All&lt;/garment&gt;
              &lt;gender&gt;GENDER_DONTCARE&lt;/gender&gt;
              &lt;award /&gt;
              &lt;awardLevel /&gt;
            &lt;/Item&gt;
            Code:
            PED::_0x5F5D1665E352A839(PLAYER::PLAYER_PED_ID(), MISC::GET_HASH_KEY("mpbeach_overlays"), MISC::GET_HASH_KEY("fm_hair_fuzz"))
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.ApplyPedOverlay(System.Int32,System.UInt32,System.UInt32)">
            <summary>
            Applies an Item from a PedDecorationCollection to a ped. These include tattoos and shirt decals.
            collection - PedDecorationCollection filename hash
            overlay - Item name hash
            Example:
            Entry inside "mpbeach_overlays.xml" -
            &lt;Item&gt;
              &lt;uvPos x="0.500000" y="0.500000" /&gt;
              &lt;scale x="0.600000" y="0.500000" /&gt;
              &lt;rotation value="0.000000" /&gt;
              &lt;nameHash&gt;FM_Hair_Fuzz&lt;/nameHash&gt;
              &lt;txdHash&gt;mp_hair_fuzz&lt;/txdHash&gt;
              &lt;txtHash&gt;mp_hair_fuzz&lt;/txtHash&gt;
              &lt;zone&gt;ZONE_HEAD&lt;/zone&gt;
              &lt;type&gt;TYPE_TATTOO&lt;/type&gt;
              &lt;faction&gt;FM&lt;/faction&gt;
              &lt;garment&gt;All&lt;/garment&gt;
              &lt;gender&gt;GENDER_DONTCARE&lt;/gender&gt;
              &lt;award /&gt;
              &lt;awardLevel /&gt;
            &lt;/Item&gt;
            Code:
            PED::_0x5F5D1665E352A839(PLAYER::PLAYER_PED_ID(), MISC::GET_HASH_KEY("mpbeach_overlays"), MISC::GET_HASH_KEY("fm_hair_fuzz"))
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedDecoration(System.Int32,System.UInt32,System.UInt32)">
            <summary>
            Applies an Item from a PedDecorationCollection to a ped. These include tattoos and shirt decals.
            collection - PedDecorationCollection filename hash
            overlay - Item name hash
            Example:
            Entry inside "mpbeach_overlays.xml" -
            &lt;Item&gt;
              &lt;uvPos x="0.500000" y="0.500000" /&gt;
              &lt;scale x="0.600000" y="0.500000" /&gt;
              &lt;rotation value="0.000000" /&gt;
              &lt;nameHash&gt;FM_Hair_Fuzz&lt;/nameHash&gt;
              &lt;txdHash&gt;mp_hair_fuzz&lt;/txdHash&gt;
              &lt;txtHash&gt;mp_hair_fuzz&lt;/txtHash&gt;
              &lt;zone&gt;ZONE_HEAD&lt;/zone&gt;
              &lt;type&gt;TYPE_TATTOO&lt;/type&gt;
              &lt;faction&gt;FM&lt;/faction&gt;
              &lt;garment&gt;All&lt;/garment&gt;
              &lt;gender&gt;GENDER_DONTCARE&lt;/gender&gt;
              &lt;award /&gt;
              &lt;awardLevel /&gt;
            &lt;/Item&gt;
            Code:
            PED::_0x5F5D1665E352A839(PLAYER::PLAYER_PED_ID(), MISC::GET_HASH_KEY("mpbeach_overlays"), MISC::GET_HASH_KEY("fm_hair_fuzz"))
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.AddStateBagChangeHandler(CitizenFX.Core.CString,CitizenFX.Core.CString,System.Delegate)">
            <summary>
            Adds a handler for changes to a state bag.
            
            The function called expects to match the following signature:
            
            ```ts
            function StateBagChangeHandler(bagName: string, key: string, value: any, reserved: number, replicated: boolean);
            ```
            
            *   **bagName**: The internal bag ID for the state bag which changed. This is usually `player:Source`, `entity:NetID`
                or `localEntity:Handle`.
            *   **key**: The changed key.
            *   **value**: The new value stored at key. The old value is still stored in the state bag at the time this callback executes.
            *   **reserved**: Currently unused.
            *   **replicated**: Whether the set is meant to be replicated.
            
            At this time, the change handler can't opt to reject changes.
            
            If bagName refers to an entity, use [GET_ENTITY_FROM_STATE_BAG_NAME](#\_0x4BDF1867) to get the entity handle
            If bagName refers to a player, use [GET_PLAYER_FROM_STATE_BAG_NAME](#\_0xA56135E0) to get the player handle
            </summary>
            <param name="keyFilter">
            The key to check for, or null for no filter.
            </param>
            <param name="bagFilter">
            The bag ID to check for such as `entity:65535`, or null for no filter.
            </param>
            <param name="handler">
            The handler function.
            </param>
            <returns>
            A cookie to remove the change handler.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.ApplyForceToEntity(System.Int32,System.Int32,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Int32,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            cpp
            enum eApplyForceTypes {
                APPLY_TYPE_FORCE = 0,
                APPLY_TYPE_IMPULSE = 1,
                APPLY_TYPE_EXTERNAL_FORCE = 2,
                APPLY_TYPE_EXTERNAL_IMPULSE = 3,
                APPLY_TYPE_TORQUE = 4,
                APPLY_TYPE_ANGULAR_IMPULSE = 5
            }
            </summary>
            <param name="entity">
            The entity handle
            </param>
            <param name="forceType">
            The force type
            </param>
            <param name="x">
            The x component of the force to apply
            </param>
            <param name="y">
            The y component of the force to apply
            </param>
            <param name="z">
            The z component of the force to apply
            </param>
            <param name="offX">
            Offset from center of entity (X)
            </param>
            <param name="offY">
            Offset from center of entity (Y)
            </param>
            <param name="offZ">
            Offset from center of entity (Z)
            </param>
            <param name="nComponent">
            Component of the entity to apply the force too. Only matters for breakable or articulated (ragdoll) physics. 0 means the root or parent component
            </param>
            <param name="bLocalForce">
            Specifies whether the force vector passed in is in local or world coordinates. `true` means the force will get automatically transformed into world space before being applied
            </param>
            <param name="bLocalOffset">
            Specifies whether the offset passed in is in local or world coordinates
            </param>
            <param name="bScaleByMass">
            Specifies whether to scale the force by mass
            </param>
            <param name="bPlayAudio">
            Specifies whether to play audio events related to the force being applied. The audio will depend on the entity type. Currently vehicles are the only entity types supported, and will play a suspension squeal depending on the magnitude of the force
            </param>
            <param name="bScaleByTimeWarp">
            Specifies whether to scale the force by time warp. Default is `true`
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.CanPlayerStartCommerceSession(CitizenFX.Core.CString)">
            <summary>
            Returns whether or not the specified player has enough information to start a commerce session for.
            </summary>
            <param name="playerSrc">
            The player handle
            </param>
            <returns>
            True or false.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.CancelEvent">
            <summary>
            Cancels the currently executing event.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.ClearPedProp(System.Int32,System.Int32)">
            <summary>
            CLEAR_PED_PROP
            </summary>
            <param name="ped">
            The ped handle.
            </param>
            <param name="propId">
            The prop id you want to clear from the ped. Refer to [SET_PED_PROP_INDEX](#\_0x93376B65A266EB5F).
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.ClearPedSecondaryTask(System.Int32)">
            <summary>
            CLEAR_PED_SECONDARY_TASK
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.ClearPedTasks(System.Int32)">
            <summary>
            Clear a ped's tasks. Stop animations and other tasks created by scripts.
            </summary>
            <param name="ped">
            Ped id. Use PlayerPedId() for your own character.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.ClearPedTasksImmediately(System.Int32)">
            <summary>
            Immediately stops the pedestrian from whatever it's doing. The difference between this and [CLEAR_PED_TASKS](#\_0xE1EF3C1216AFF2CD) is that this one teleports the ped but does not change the position of the ped.
            </summary>
            <param name="ped">
            Ped id.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.ClearPlayerWantedLevel(CitizenFX.Core.CString)">
            <summary>
            This executes at the same as speed as PLAYER::SET_PLAYER_WANTED_LEVEL(player, 0, false);  
            PLAYER::GET_PLAYER_WANTED_LEVEL(player); executes in less than half the time. Which means that it's worth first checking if the wanted level needs to be cleared before clearing. However, this is mostly about good code practice and can important in other situations. The difference in time in this example is negligible.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.CreateObject(System.UInt32,System.Single,System.Single,System.Single,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Creates an object (prop) with the specified model at the specified position, offset on the Z axis by the radius of the object's model.
            This object will initially be owned by the creating script as a mission entity, and the model should be loaded already (e.g. using REQUEST_MODEL).
            </summary>
            <param name="modelHash">
            The model to spawn.
            </param>
            <param name="x">
            Spawn coordinate X component.
            </param>
            <param name="y">
            Spawn coordinate Y component.
            </param>
            <param name="z">
            Spawn coordinate Z component, 'ground level'.
            </param>
            <param name="isNetwork">
            Whether to create a network object for the object. If false, the object exists only locally.
            </param>
            <param name="netMissionEntity">
            Whether to register the object as pinned to the script host in the R\* network model.
            </param>
            <param name="doorFlag">
            False to create a door archetype (archetype flag bit 26 set) as a door. Required to be set to true to create door models in network mode.
            </param>
            <returns>
            A script handle (fwScriptGuid index) for the object, or `0` if the object failed to be created.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.CreateObjectNoOffset(System.UInt32,System.Single,System.Single,System.Single,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Creates an object (prop) with the specified model centered at the specified position.
            This object will initially be owned by the creating script as a mission entity, and the model should be loaded already (e.g. using REQUEST_MODEL).
            </summary>
            <param name="modelHash">
            The model to spawn.
            </param>
            <param name="x">
            Spawn coordinate X component.
            </param>
            <param name="y">
            Spawn coordinate Y component.
            </param>
            <param name="z">
            Spawn coordinate Z component.
            </param>
            <param name="isNetwork">
            Whether to create a network object for the object. If false, the object exists only locally.
            </param>
            <param name="netMissionEntity">
            Whether to register the object as pinned to the script host in the R\* network model.
            </param>
            <param name="doorFlag">
            False to create a door archetype (archetype flag bit 26 set) as a door. Required to be set to true to create door models in network mode.
            </param>
            <returns>
            A script handle (fwScriptGuid index) for the object, or `0` if the object failed to be created.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.CreatePed(System.Int32,System.UInt32,System.Single,System.Single,System.Single,System.Single,System.Boolean,System.Boolean)">
            <summary>
            Creates a ped (biped character, pedestrian, actor) with the specified model at the specified position and heading.
            This ped will initially be owned by the creating script as a mission entity, and the model should be loaded already
            (e.g. using REQUEST_MODEL).
            </summary>
            <param name="pedType">
            Unused. Peds get set to CIVMALE/CIVFEMALE/etc. no matter the value specified.
            </param>
            <param name="modelHash">
            The model of ped to spawn.
            </param>
            <param name="x">
            Spawn coordinate X component.
            </param>
            <param name="y">
            Spawn coordinate Y component.
            </param>
            <param name="z">
            Spawn coordinate Z component.
            </param>
            <param name="heading">
            Heading to face towards, in degrees.
            </param>
            <param name="isNetwork">
            Whether to create a network object for the ped. If false, the ped exists only locally.
            </param>
            <param name="bScriptHostPed">
            Whether to register the ped as pinned to the script host in the R\* network model.
            </param>
            <returns>
            A script handle (fwScriptGuid index) for the ped, or `0` if the ped failed to be created.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.CreatePedInsideVehicle(System.Int32,System.Int32,System.UInt32,System.Int32,System.Boolean,System.Boolean)">
            <summary>
            CREATE_PED_INSIDE_VEHICLE
            </summary>
            <param name="pedType">
            See [`CREATE_PED`](#\_0xD49F9B0955C367DE)
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.CreateVehicle(System.UInt32,System.Single,System.Single,System.Single,System.Single,System.Boolean,System.Boolean)">
            <summary>
            Creates a vehicle with the specified model at the specified position. This vehicle will initially be owned by the creating
            script as a mission entity, and the model should be loaded already (e.g. using REQUEST_MODEL).
            
            ```
            NativeDB Added Parameter 8: BOOL p7
            ```
            </summary>
            <param name="modelHash">
            The model of vehicle to spawn.
            </param>
            <param name="x">
            Spawn coordinate X component.
            </param>
            <param name="y">
            Spawn coordinate Y component.
            </param>
            <param name="z">
            Spawn coordinate Z component.
            </param>
            <param name="heading">
            Heading to face towards, in degrees.
            </param>
            <param name="isNetwork">
            Whether to create a network object for the vehicle. If false, the vehicle exists only locally.
            </param>
            <param name="netMissionEntity">
            Whether to register the vehicle as pinned to the script host in the R\* network model.
            </param>
            <returns>
            A script handle (fwScriptGuid index) for the vehicle, or `0` if the vehicle failed to be created.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.CreateVehicleServerSetter(System.UInt32,CitizenFX.Core.CString,System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Equivalent to CREATE_VEHICLE, but it uses 'server setter' logic (like the former CREATE_AUTOMOBILE) as a workaround for
            reliability concerns regarding entity creation RPC.
            
            Unlike CREATE_AUTOMOBILE, this supports other vehicle types as well.
            </summary>
            <param name="modelHash">
            The model of vehicle to spawn.
            </param>
            <param name="type">
            The appropriate vehicle type for the model info. Can be one of `automobile`, `bike`, `boat`, `heli`, `plane`, `submarine`, `trailer`, and (potentially), `train`. This should be the same type as the `type` field in `vehicles.meta`.
            </param>
            <param name="x">
            Spawn coordinate X component.
            </param>
            <param name="y">
            Spawn coordinate Y component.
            </param>
            <param name="z">
            Spawn coordinate Z component.
            </param>
            <param name="heading">
            Heading to face towards, in degrees.
            </param>
            <returns>
            A script handle for the vehicle, or 0 if the vehicle failed to be created.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.DeleteEntity(System.Int32)">
            <summary>
            Deletes the specified entity.
            
            **NOTE**: For trains this will only work if called on the train engine, it will not work on its carriages.
            </summary>
            <param name="entity">
            The entity to delete.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.DeleteFunctionReference(CitizenFX.Core.CString)">
            <summary>
            DELETE_FUNCTION_REFERENCE
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.DeleteResourceKvp(CitizenFX.Core.CString)">
            <summary>
            DELETE_RESOURCE_KVP
            </summary>
            <param name="key">
            The key to delete
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.DeleteResourceKvpNoSync(CitizenFX.Core.CString)">
            <summary>
            Nonsynchronous [DELETE_RESOURCE_KVP](#\_0x7389B5DF) operation; see [FLUSH_RESOURCE_KVP](#\_0x5240DA5A).
            </summary>
            <param name="key">
            The key to delete
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.DeleteTrain(System.Int32)">
            <summary>
            Deletes the specified `entity` and any carriage its attached to, or that is attached to it.
            </summary>
            <param name="entity">
            The carriage to delete.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.DoesBoatSinkWhenWrecked(System.Int32)">
            <summary>
            DOES_BOAT_SINK_WHEN_WRECKED
            </summary>
            <param name="vehicle">
            The target vehicle.
            </param>
            <returns>
            Returns whether or not the boat sinks when wrecked.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.DoesEntityExist(System.Int32)">
            <summary>
            DOES_ENTITY_EXIST
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.DoesPlayerExist(CitizenFX.Core.CString)">
            <summary>
            Returns whether or not the player exists
            </summary>
            <returns>
            True if the player exists, false otherwise
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.DoesPlayerOwnSku(CitizenFX.Core.CString,System.Int32)">
            <summary>
            Requests whether or not the player owns the specified SKU.
            </summary>
            <param name="playerSrc">
            The player handle
            </param>
            <param name="skuId">
            The ID of the SKU.
            </param>
            <returns>
            A boolean.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.DoesPlayerOwnSkuExt(CitizenFX.Core.CString,System.Int32)">
            <summary>
            Requests whether or not the player owns the specified package.
            </summary>
            <param name="playerSrc">
            The player handle
            </param>
            <param name="skuId">
            The package ID on Tebex.
            </param>
            <returns>
            A boolean.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.DoesTrainStopAtStations(System.Int32)">
            <summary>
            DOES_TRAIN_STOP_AT_STATIONS
            </summary>
            <param name="train">
            The train handle
            </param>
            <returns>
            True if the train stops at stations. False otherwise
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.DropPlayer(CitizenFX.Core.CString,CitizenFX.Core.CString)">
            <summary>
            DROP_PLAYER
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.DuplicateFunctionReference(CitizenFX.Core.CString)">
            <summary>
            DUPLICATE_FUNCTION_REFERENCE
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.EnableEnhancedHostSupport(System.Boolean)">
            <summary>
            ENABLE_ENHANCED_HOST_SUPPORT
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.EndFindKvp(System.Int32)">
            <summary>
            END_FIND_KVP
            </summary>
            <param name="handle">
            The KVP find handle returned from [START_FIND_KVP](#\_0xDD379006)
            </param>
            <returns>
            None.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.EnsureEntityStateBag(System.Int32)">
            <summary>
            Internal function for ensuring an entity has a state bag.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.ExecuteCommand(CitizenFX.Core.CString)">
            <summary>
            Depending on your use case you may need to use `add_acl resource.&lt;your_resource_name&gt; command.&lt;command_name&gt; allow` to use this native in your resource.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.FindKvp(System.Int32)">
            <summary>
            FIND_KVP
            </summary>
            <param name="handle">
            The KVP find handle returned from [START_FIND_KVP](#\_0xDD379006)
            </param>
            <returns>
            None.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.FlagServerAsPrivate(System.Boolean)">
            <summary>
            FLAG_SERVER_AS_PRIVATE
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.FlushResourceKvp">
            <summary>
            Nonsynchronous operations will not wait for a disk/filesystem flush before returning from a write or delete call. They will be much faster than their synchronous counterparts (e.g., bulk operations), however, a system crash may lose the data to some recent operations.
            
            This native ensures all `_NO_SYNC` operations are synchronized with the disk/filesystem.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.FormatStackTrace(System.Object)">
            <summary>
            An internal function for converting a stack trace object to a string.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.FreezeEntityPosition(System.Int32,System.Boolean)">
            <summary>
            Freezes or unfreezes an entity preventing its coordinates to change by the player if set to `true`. You can still change the entity position using [`SET_ENTITY_COORDS`](#\_0x06843DA7060A026B).
            </summary>
            <param name="entity">
            The entity to freeze/unfreeze.
            </param>
            <param name="toggle">
            Freeze or unfreeze entity.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetAirDragMultiplierForPlayersVehicle(CitizenFX.Core.CString)">
            <summary>
            GET_AIR_DRAG_MULTIPLIER_FOR_PLAYERS_VEHICLE
            </summary>
            <param name="playerSrc">
            The player handle
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetAllObjects">
            <summary>
            Returns all object handles known to the server.
            The data returned adheres to the following layout:
            
            ```
            [127, 42, 13, 37]
            ```
            </summary>
            <returns>
            An object containing a list of object handles.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetAllPeds">
            <summary>
            Returns all peds handles known to the server.
            The data returned adheres to the following layout:
            
            ```
            [127, 42, 13, 37]
            ```
            </summary>
            <returns>
            An object containing a list of peds handles.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetAllVehicles">
            <summary>
            Returns all vehicle handles known to the server.
            The data returned adheres to the following layout:
            
            ```
            [127, 42, 13, 37]
            ```
            </summary>
            <returns>
            An object containing a list of vehicle handles.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetConsoleBuffer">
            <summary>
            Returns the current console output buffer.
            </summary>
            <returns>
            The most recent game console output, as a string.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetConvar(CitizenFX.Core.CString,CitizenFX.Core.CString)">
            <summary>
            Can be used to get a console variable of type `char*`, for example a string.
            </summary>
            <param name="varName">
            The console variable to look up.
            </param>
            <param name="default_">
            The default value to set if none is found.
            </param>
            <returns>
            Returns the convar value if it can be found, otherwise it returns the assigned `default`.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetConvarBool(CitizenFX.Core.CString,System.Boolean)">
            <summary>
            Can be used to get a console variable casted back to `bool`.
            </summary>
            <param name="varName">
            The console variable to look up.
            </param>
            <param name="defaultValue">
            The default value to set if none is found.
            </param>
            <returns>
            Returns the convar value if it can be found, otherwise it returns the assigned `default`.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetConvarFloat(CitizenFX.Core.CString,System.Single)">
            <summary>
            This will have floating point inaccuracy.
            </summary>
            <param name="varName">
            The console variable to get
            </param>
            <param name="defaultValue">
            The default value to set, if none are found.
            </param>
            <returns>
            Returns the value set in varName, or `default` if none are specified
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetConvarInt(CitizenFX.Core.CString,System.Int32)">
            <summary>
            Can be used to get a console variable casted back to `int` (an integer value).
            </summary>
            <param name="varName">
            The console variable to look up.
            </param>
            <param name="default_">
            The default value to set if none is found (variable not set using [SET_CONVAR](#\_0x341B16D2), or not accessible).
            </param>
            <returns>
            Returns the convar value if it can be found, otherwise it returns the assigned `default`.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetCurrentPedWeapon(System.Int32)">
            <summary>
            Returns the hash of weapon the Ped is currently using.
            </summary>
            <param name="ped">
            The target ped.
            </param>
            <returns>
            The weapon hash.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetCurrentResourceName">
            <summary>
            Returns the name of the currently executing resource.
            </summary>
            <returns>
            The name of the resource.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetEntitiesInRadius(System.Single,System.Single,System.Single,System.Single,System.Int32,System.Boolean,System.Object)">
            <summary>
            ### Supported types
            
            *   \[1] : Peds (including animals) and players.
            *   \[2] : Vehicles.
            *   \[3] : Objects (props), doors, and projectiles.
            
            ### Coordinates need to be send unpacked (x,y,z)
            
            ```lua
            
            -- Define the allowed model hashes
            local allowedModelHashes = { GetHashKey("p_crate03x"), GetHashKey("p_crate22x") }
            
            -- Get the player's current coordinates
            local playerCoords = GetEntityCoords(PlayerPedId())
            
            -- Retrieve all entities of type Object (type 3) within a radius of 10.0 units
            -- that match the allowed model hashes
            -- and sort output entities by distance
            local entities = GetEntitiesInRadius(playerCoords.x, playerCoords.y, playerCoords.z, 10.0, 3, true, allowedModelHashes)
            
            -- Iterate through the list of entities and print their ids
            for i = 1, #entities do
                local entity = entities[i]
                print(entity)
            end
            
            ```
            </summary>
            <param name="x">
            The X coordinate.
            </param>
            <param name="y">
            The Y coordinate.
            </param>
            <param name="z">
            The Z coordinate.
            </param>
            <param name="radius">
            Max distance from coordinate to entity
            </param>
            <param name="entityType">
            Entity types see list below
            </param>
            <param name="sortByDistance">
            Sort output entites by distance from nearest to farthest
            </param>
            <param name="models">
            List of allowed models its also optional
            </param>
            <returns>
            An array containing entity handles for each entity.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetEntityAttachedTo(System.Int32)">
            <summary>
            Gets the entity that this entity is attached to.
            </summary>
            <param name="entity">
            The entity to check.
            </param>
            <returns>
            The attached entity handle. 0 returned if the entity is not attached.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetEntityCollisionDisabled(System.Int32)">
            <summary>
            GET_ENTITY_COLLISION_DISABLED
            </summary>
            <param name="entity">
            The target entity.
            </param>
            <returns>
            Returns whether or not entity collisions are disabled.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetEntityCoords(System.Int32)">
            <summary>
            Gets the current coordinates for a specified entity. This native is used server side when using OneSync.
            
            See [GET_ENTITY_COORDS](#\_0x3FEF770D40960D5A) for client side.
            </summary>
            <param name="entity">
            The entity to get the coordinates from.
            </param>
            <returns>
            The current entity coordinates.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetEntityFromStateBagName(CitizenFX.Core.CString)">
            <summary>
            Returns the entity handle for the specified state bag name. For use with [ADD_STATE_BAG_CHANGE_HANDLER](#\_0x5BA35AAF).
            </summary>
            <param name="bagName">
            An internal state bag ID from the argument to a state bag change handler.
            </param>
            <returns>
            The entity handle or 0 if the state bag name did not refer to an entity, or the entity does not exist.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetEntityHeading(System.Int32)">
            <summary>
            GET_ENTITY_HEADING
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetEntityHealth(System.Int32)">
            <summary>
            Only works for vehicle and peds
            </summary>
            <param name="entity">
            The entity to check the health of
            </param>
            <returns>
            If the entity is a vehicle it will return 0-1000
            If the entity is a ped it will return 0-200
            If the entity is an object it will return 0
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetEntityMaxHealth(System.Int32)">
            <summary>
            Currently it only works with peds.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetEntityModel(System.Int32)">
            <summary>
            GET_ENTITY_MODEL
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetEntityOrphanMode(System.Int32)">
            <summary>
            GET_ENTITY_ORPHAN_MODE
            </summary>
            <param name="entity">
            The entity to get the orphan mode of
            </param>
            <returns>
            Returns the entities current orphan mode, refer to enum in [SET_ENTITY_ORPHAN_MODE](#\_0x489E9162)
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetEntityPopulationType(System.Int32)">
            <summary>
            This native gets an entity's population type.
            </summary>
            <param name="entity">
            the entity to obtain the population type from
            </param>
            <returns>
            Returns the population type ID, defined by the below enumeration:```cpp
            enum ePopulationType
            {
            	POPTYPE_UNKNOWN = 0,
            	POPTYPE_RANDOM_PERMANENT,
            	POPTYPE_RANDOM_PARKED,
            	POPTYPE_RANDOM_PATROL,
            	POPTYPE_RANDOM_SCENARIO,
            	POPTYPE_RANDOM_AMBIENT,
            	POPTYPE_PERMANENT,
            	POPTYPE_MISSION,
            	POPTYPE_REPLAY,
            	POPTYPE_CACHE,
            	POPTYPE_TOOL
            };
            ```
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetEntityRemoteSyncedScenesAllowed(System.Int32)">
            <summary>
            GET_ENTITY_REMOTE_SYNCED_SCENES_ALLOWED
            </summary>
            <param name="entity">
            The entity to get the flag for.
            </param>
            <returns>
            Returns if the entity is allowed to participate in network-synchronized scenes initiated by clients that do not own the entity.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetEntityRotation(System.Int32)">
            <summary>
            GET_ENTITY_ROTATION
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetEntityRotationVelocity(System.Int32)">
            <summary>
            GET_ENTITY_ROTATION_VELOCITY
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetEntityRoutingBucket(System.Int32)">
            <summary>
            Gets the routing bucket for the specified entity.
            
            Routing buckets are also known as 'dimensions' or 'virtual worlds' in past echoes, however they are population-aware.
            </summary>
            <param name="entity">
            The entity to get the routing bucket for.
            </param>
            <returns>
            The routing bucket ID.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetEntityScript(System.Int32)">
            <summary>
            GET_ENTITY_SCRIPT
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetEntitySpeed(System.Int32)">
            <summary>
            Gets the current speed of the entity in meters per second.
            
            ```
            To convert to MPH: speed * 2.236936
            To convert to KPH: speed * 3.6
            ```
            </summary>
            <param name="entity">
            The entity to get the speed of
            </param>
            <returns>
            The speed of the entity in meters per second
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetEntityType(System.Int32)">
            <summary>
            Gets the entity type (as an integer), which can be one of the following defined down below:
            
            **The following entities will return type `1`:**
            
            *   Ped
            *   Player
            *   Animal (Red Dead Redemption 2)
            *   Horse (Red Dead Redemption 2)
            
            **The following entities will return type `2`:**
            
            *   Automobile
            *   Bike
            *   Boat
            *   Heli
            *   Plane
            *   Submarine
            *   Trailer
            *   Train
            *   DraftVeh (Red Dead Redemption 2)
            
            **The following entities will return type `3`:**
            
            *   Object
            *   Door
            *   Pickup
            
            Otherwise, a value of `0` will be returned.
            </summary>
            <param name="entity">
            The entity to get the type of.
            </param>
            <returns>
            The entity type returned as an integer value.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetEntityVelocity(System.Int32)">
            <summary>
            GET_ENTITY_VELOCITY
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetGameBuildNumber">
            <summary>
            Returns the internal build number of the current game being executed.
            
            Possible values:
            
            *   FiveM
                *   1604
                *   2060
                *   2189
                *   2372
                *   2545
                *   2612
                *   2699
                *   2802
                *   2944
                *   3095
                *   3258
                *   3323
                *   3407
                *   3570
            *   RedM
                *   1311
                *   1355
                *   1436
                *   1491
            *   LibertyM
                *   43
            *   FXServer
                *   0
            </summary>
            <returns>
            The build number, or **0** if no build number is known.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetGameName">
            <summary>
            Returns the current game being executed.
            
            Possible values:
            
            | Return value | Meaning                        |
            | ------------ | ------------------------------ |
            | `fxserver`   | Server-side code ('Duplicity') |
            | `fivem`      | FiveM for GTA V                |
            | `libertym`   | LibertyM for GTA IV            |
            | `redm`       | RedM for Red Dead Redemption 2 |
            </summary>
            <returns>
            The game the script environment is running in.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetGamePool(CitizenFX.Core.CString)">
            <summary>
            Returns a list of entity handles (script GUID) for all entities in the specified pool - the data returned is an array as
            follows:
            
            ```json
            [ 770, 1026, 1282, 1538, 1794, 2050, 2306, 2562, 2818, 3074, 3330, 3586, 3842, 4098, 4354, 4610, ...]
            ```
            
            ### Supported pools
            
            *   `CPed`: Peds (including animals) and players.
            *   `CObject`: Objects (props), doors, and projectiles.
            *   `CNetObject`: Networked objects
            *   `CVehicle`: Vehicles.
            *   `CPickup`: Pickups.
            </summary>
            <param name="poolName">
            The pool name to get a list of entities from.
            </param>
            <returns>
            An array containing entity handles for each entity in the named pool.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetGameTimer">
            <summary>
            Gets the current game timer in milliseconds.
            </summary>
            <returns>
            The game time.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetHashKey(CitizenFX.Core.CString)">
            <summary>
            This native converts the passed string to a hash.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetHeliBodyHealth(System.Int32)">
            <summary>
            **Note** This native will always return `1000.0` unless [SET_VEHICLE_BODY_HEALTH](#\_0xB77D05AC8C78AADB), [SET_VEHICLE_ENGINE_HEALTH](#\_0x45F6D8EEF34ABEF1), or [SET_VEHICLE_PETROL_TANK_HEALTH](#\_0x70DB57649FA8D0D8) have been called with a value greater than `1000.0`.
            </summary>
            <param name="heli">
            The helicopter to check
            </param>
            <returns>
            Returns the current health of the helicopter's body.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetHeliDisableExplodeFromBodyDamage(System.Int32)">
            <summary>
            This is a getter for [SET_DISABLE_HELI_EXPLODE_FROM_BODY_DAMAGE](#\_0xEDBC8405B3895CC9)
            </summary>
            <param name="heli">
            The helicopter to check
            </param>
            <returns>
            Returns `true` if the helicopter is set to be protected from exploding due to minor body damage, `false` otherwise.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetHeliEngineHealth(System.Int32)">
            <summary>
            **Note** This native will always return `1000.0` unless [SET_VEHICLE_BODY_HEALTH](#\_0xB77D05AC8C78AADB), [SET_VEHICLE_ENGINE_HEALTH](#\_0x45F6D8EEF34ABEF1), or [SET_VEHICLE_PETROL_TANK_HEALTH](#\_0x70DB57649FA8D0D8) have been called with a value greater than `1000.0`.
            </summary>
            <param name="heli">
            The helicopter to check
            </param>
            <returns>
            Returns the current health of the helicopter's engine.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetHeliGasTankHealth(System.Int32)">
            <summary>
            **Note** This native will always return `1000.0` unless [SET_VEHICLE_BODY_HEALTH](#\_0xB77D05AC8C78AADB), [SET_VEHICLE_ENGINE_HEALTH](#\_0x45F6D8EEF34ABEF1), or [SET_VEHICLE_PETROL_TANK_HEALTH](#\_0x70DB57649FA8D0D8) have been called with a value greater than `1000.0`.
            </summary>
            <param name="heli">
            The helicopter to check
            </param>
            <returns>
            Returns the current health of the helicopter's gas tank.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetHeliMainRotorDamageScale(System.Int32)">
            <summary>
            GET_HELI_MAIN_ROTOR_DAMAGE_SCALE
            </summary>
            <param name="heli">
            The helicopter to check
            </param>
            <returns>
            Returns a value representing the damage scaling factor applied to the helicopter's main rotor. The value ranges from `0.0` (no damage scaling) to`  1.0 ` (full damage scaling).
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetHeliMainRotorHealth(System.Int32)">
            <summary>
            GET_HELI_MAIN_ROTOR_HEALTH
            </summary>
            <param name="vehicle">
            The target vehicle.
            </param>
            <returns>
            See the client-side [GET_HELI_MAIN_ROTOR_HEALTH](#\_0xE4CB7541F413D2C5) for the return value.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetHeliPitchControl(System.Int32)">
            <summary>
            GET_HELI_PITCH_CONTROL
            </summary>
            <param name="heli">
            The helicopter to check.
            </param>
            <returns>
            Returns a value representing the pitch control of the helicopter. The values range from `-1.0` (nose down) to `1.0` (nose up), with `0.0` indicating no pitch input.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetHeliRearRotorDamageScale(System.Int32)">
            <summary>
            GET_HELI_REAR_ROTOR_DAMAGE_SCALE
            </summary>
            <param name="heli">
            The helicopter to check
            </param>
            <returns>
            Returns a value representing the damage scaling factor applied to the helicopter's rear rotor. The value ranges from `0.0` (no damage scaling) to`  1.0 ` (full damage scaling).
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetHeliRearRotorHealth(System.Int32)">
            <summary>
            This native is a getter for [SET_HELI_TAIL_ROTOR_HEALTH](#\_0xFE205F38AAA58E5B)
            </summary>
            <param name="vehicle">
            The target vehicle.
            </param>
            <returns>
            Returns the health of the helicopter's rear rotor. The maximum health value is `1000`.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetHeliRollControl(System.Int32)">
            <summary>
            GET_HELI_ROLL_CONTROL
            </summary>
            <param name="heli">
            The helicopter to check.
            </param>
            <returns>
            Returns a value representing the roll control of the helicopter. The values range from `-1.0` (roll left) to `1.0` (roll right), with `0.0` indicating no roll input.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetHeliTailRotorDamageScale(System.Int32)">
            <summary>
            GET_HELI_TAIL_ROTOR_DAMAGE_SCALE
            </summary>
            <param name="heli">
            The helicopter to check
            </param>
            <returns>
            Returns a value representing the damage scaling factor applied to the helicopter's tail rotor. The value ranges from `0.0` (no damage scaling) to`  1.0 ` (full damage scaling).
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetHeliTailRotorHealth(System.Int32)">
            <summary>
            **Note**: This native is deprecated, please use [`GET_HELI_REAR_ROTOR_HEALTH`](#\_0x33EE6E2B) instead.
            </summary>
            <param name="vehicle">
            The target vehicle.
            </param>
            <returns>
            Return the health of the rear rotor of the helicopter, not the tail rotor.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetHeliThrottleControl(System.Int32)">
            <summary>
            GET_HELI_THROTTLE_CONTROL
            </summary>
            <param name="heli">
            The helicopter to check.
            </param>
            <returns>
            Returns a value representing the throttle control of the helicopter. The value ranges from `0.0` (no throttle) to `2.0` (full throttle).
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetHeliYawControl(System.Int32)">
            <summary>
            GET_HELI_YAW_CONTROL
            </summary>
            <param name="heli">
            The helicopter to check.
            </param>
            <returns>
            Returns a value the yaw control of the helicopter. The value ranges from `-1.0` (yaw left) to `1.0` (yaw right), with `0.0` meaning no yaw input.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetHostId">
            <summary>
            GET_HOST_ID
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetInstanceId">
            <summary>
            GET_INSTANCE_ID
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetInvokingResource">
            <summary>
            GET_INVOKING_RESOURCE
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetIsHeliEngineRunning(System.Int32)">
            <summary>
            GET_IS_HELI_ENGINE_RUNNING
            </summary>
            <param name="heli">
            The helicopter to check
            </param>
            <returns>
            Returns `true` if the helicopter's engine is running, `false` if it is not.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetIsVehicleEngineRunning(System.Int32)">
            <summary>
            GET_IS_VEHICLE_ENGINE_RUNNING
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetIsVehiclePrimaryColourCustom(System.Int32)">
            <summary>
            GET_IS_VEHICLE_PRIMARY_COLOUR_CUSTOM
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetIsVehicleSecondaryColourCustom(System.Int32)">
            <summary>
            GET_IS_VEHICLE_SECONDARY_COLOUR_CUSTOM
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetLandingGearState(System.Int32)">
            <summary>
            See the client-side [GET_LANDING_GEAR_STATE](#\_0x9B0F3DCA3DB0F4CD) native for a description of landing gear states.
            </summary>
            <param name="vehicle">
            The vehicle to check.
            </param>
            <returns>
            The current state of the vehicles landing gear.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetLastPedInVehicleSeat(System.Int32,System.Int32)">
            <summary>
            GET_LAST_PED_IN_VEHICLE_SEAT
            </summary>
            <param name="vehicle">
            The target vehicle.
            </param>
            <param name="seatIndex">
            See eSeatPosition declared in [`IS_VEHICLE_SEAT_FREE`](#\_0x22AC59A870E6A669).
            </param>
            <returns>
            The last ped in the specified seat of the passed vehicle. Returns 0 if the specified seat was never occupied or the last ped does not exist anymore.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetNetTypeFromEntity(System.Int32)">
            <summary>
            Gets the specific entity type (as an integer), which can be one of the following defined down below:
            
            #### FiveM:
            
            ```cpp
            enum eNetObjEntityType
            {
                Automobile = 0,
                Bike = 1,
                Boat = 2,
                Door = 3,
                Heli = 4,
                Object = 5,
                Ped = 6,
                Pickup = 7,
                PickupPlacement = 8,
                Plane = 9,
                Submarine = 10,
                Player = 11,
                Trailer = 12,
                Train = 13
            };
            ```
            
            #### RedM:
            
            ```cpp
            enum eNetObjEntityType
            {
                Animal = 0,
                Automobile = 1,
                Bike = 2,
                Boat = 3,
                Door = 4,
                Heli = 5,
                Object = 6,
                Ped = 7,
                Pickup = 8,
                PickupPlacement = 9,
                Plane = 10,
                Submarine = 11,
                Player = 12,
                Trailer = 13,
                Train = 14,
                DraftVeh = 15,
                StatsTracker = 16,
                PropSet = 17,
                AnimScene = 18,
                GroupScenario = 19,
                Herd = 20,
                Horse = 21,
                WorldState = 22,
                WorldProjectile = 23,
                Incident = 24,
                Guardzone = 25,
                PedGroup = 26,
                CombatDirector = 27,
                PedSharedTargeting = 28,
                Persistent = 29
            };
            ```
            </summary>
            <param name="entity">
            The entity to get the specific type of.
            </param>
            <returns>
            The specific entity type returned as an integer value or -1 if the entity is invalid.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetNumPlayerIdentifiers(CitizenFX.Core.CString)">
            <summary>
            GET_NUM_PLAYER_IDENTIFIERS
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetNumPlayerIndices">
            <summary>
            GET_NUM_PLAYER_INDICES
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetNumPlayerTokens(CitizenFX.Core.CString)">
            <summary>
            GET_NUM_PLAYER_TOKENS
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetNumResourceMetadata(CitizenFX.Core.CString,CitizenFX.Core.CString)">
            <summary>
            Gets the amount of metadata values with the specified key existing in the specified resource's manifest.
            See also: [Resource manifest](https://docs.fivem.net/docs/scripting-reference/resource-manifest/resource-manifest/)
            </summary>
            <param name="resourceName">
            The resource name.
            </param>
            <param name="metadataKey">
            The key to look up in the resource manifest.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetNumResources">
            <summary>
            GET_NUM_RESOURCES
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPasswordHash(CitizenFX.Core.CString)">
            <summary>
            GET_PASSWORD_HASH
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPedArmour(System.Int32)">
            <summary>
            GET_PED_ARMOUR
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPedCauseOfDeath(System.Int32)">
            <summary>
            GET_PED_CAUSE_OF_DEATH
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPedDesiredHeading(System.Int32)">
            <summary>
            GET_PED_DESIRED_HEADING
            </summary>
            <param name="ped">
            The target ped
            </param>
            <returns>
            Returns ped's desired heading.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPedInVehicleSeat(System.Int32,System.Int32)">
            <summary>
            GET_PED_IN_VEHICLE_SEAT
            </summary>
            <param name="vehicle">
            The target vehicle.
            </param>
            <param name="seatIndex">
            See eSeatPosition declared in [`IS_VEHICLE_SEAT_FREE`](#\_0x22AC59A870E6A669).
            </param>
            <returns>
            The ped in the specified seat of the passed vehicle. Returns 0 if the specified seat is not occupied.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPedMaxHealth(System.Int32)">
            <summary>
            GET_PED_MAX_HEALTH
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPedRelationshipGroupHash(System.Int32)">
            <summary>
            Gets the current relationship group hash of a ped.
            </summary>
            <param name="ped">
            The target ped
            </param>
            <returns>
            The relationship group hash.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPedScriptTaskCommand(System.Int32)">
            <summary>
            Gets the script task command currently assigned to the ped.
            </summary>
            <param name="ped">
            The target ped.
            </param>
            <returns>
            The script task command currently assigned to the ped. A value of 0x811E343C denotes no script task is assigned.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPedScriptTaskStage(System.Int32)">
            <summary>
            Gets the stage of the peds scripted task.
            </summary>
            <param name="ped">
            The target ped.
            </param>
            <returns>
            The stage of the ped's scripted task. A value of 3 denotes no script task is assigned.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPedSourceOfDamage(System.Int32)">
            <summary>
            Get the last entity that damaged the ped. This native is used server side when using OneSync.
            </summary>
            <param name="ped">
            The target ped
            </param>
            <returns>
            The entity id. Returns 0 if the ped has not been damaged recently.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPedSourceOfDeath(System.Int32)">
            <summary>
            Get the entity that killed the ped. This native is used server side when using OneSync.
            </summary>
            <param name="ped">
            The target ped
            </param>
            <returns>
            The entity id. Returns 0 if the ped has no killer.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPedSpecificTaskType(System.Int32,System.Int32)">
            <summary>
            Gets the type of a ped's specific task given an index of the CPedTaskSpecificDataNode nodes.
            A ped will typically have a task at index 0, if a ped has multiple tasks at once they will be in the order 0, 1, 2, etc.
            </summary>
            <param name="ped">
            The target ped.
            </param>
            <param name="index">
            A zero-based index with a maximum value of 7.
            </param>
            <returns>
            The type of the specific task.
            1604: A value of 530 denotes no script task is assigned or an invalid input was given.
            2060+: A value of 531 denotes no script task is assigned or an invalid input was given.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPedStealthMovement(System.Int32)">
            <summary>
            GET_PED_STEALTH_MOVEMENT
            </summary>
            <param name="ped">
            The target ped.
            </param>
            <returns>
            Whether or not the ped is stealthy.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerCameraRotation(CitizenFX.Core.CString)">
            <summary>
            Gets the current camera rotation for a specified player. This native is used server side when using OneSync.
            </summary>
            <param name="playerSrc">
            The player handle.
            </param>
            <returns>
            The player's camera rotation. Values are in radians.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerEndpoint(CitizenFX.Core.CString)">
            <summary>
            GET_PLAYER_ENDPOINT
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerFakeWantedLevel(CitizenFX.Core.CString)">
            <summary>
            Gets the current fake wanted level for a specified player. This native is used server side when using OneSync.
            </summary>
            <param name="playerSrc">
            The target player
            </param>
            <returns>
            The fake wanted level
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerFocusPos(CitizenFX.Core.CString)">
            <summary>
            Gets the focus position (i.e. the position of the active camera in the game world) of a player.
            </summary>
            <param name="playerSrc">
            The player to get the focus position of
            </param>
            <returns>
            Returns a `Vector3` containing the focus position of the player.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerFromIndex(System.Int32)">
            <summary>
            GET_PLAYER_FROM_INDEX
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerFromStateBagName(CitizenFX.Core.CString)">
            <summary>
            On the server this will return the players source, on the client it will return the player handle.
            </summary>
            <param name="bagName">
            An internal state bag ID from the argument to a state bag change handler.
            </param>
            <returns>
            The player handle or 0 if the state bag name did not refer to a player, or the player does not exist.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerGuid(CitizenFX.Core.CString)">
            <summary>
            GET_PLAYER_GUID
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerIdentifier(CitizenFX.Core.CString,System.Int32)">
            <summary>
            To get the number of identifiers, use [GET_NUM_PLAYER_IDENTIFIERS](#\_0xFF7F66AB)
            
            To get a specific type of identifier, use [GET_PLAYER_IDENTIFIER_BY_TYPE](#\_0xA61C8FC6)
            </summary>
            <returns>
            Returns the identifier at the specific index, if out of bounds returns `null`
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerIdentifierByType(CitizenFX.Core.CString,CitizenFX.Core.CString)">
            <summary>
            Get an identifier from a player by the type of the identifier.
            Known [Identifiers](https://docs.fivem.net/docs/scripting-reference/runtimes/lua/functions/GetPlayerIdentifiers/#identifier-types)
            </summary>
            <param name="playerSrc">
            The player to get the identifier for
            </param>
            <param name="identifierType">
            The string to match in an identifier, this can be `"license"` for example.
            </param>
            <returns>
            The identifier that matches the string provided
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerInvincible(CitizenFX.Core.CString)">
            <summary>
            GET_PLAYER_INVINCIBLE
            </summary>
            <param name="playerSrc">
            The player handle
            </param>
            <returns>
            A boolean to tell if the player is invincible.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerLastMsg(CitizenFX.Core.CString)">
            <summary>
            GET_PLAYER_LAST_MSG
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerMaxArmour(CitizenFX.Core.CString)">
            <summary>
            GET_PLAYER_MAX_ARMOUR
            </summary>
            <param name="playerSrc">
            The player handle
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerMaxHealth(CitizenFX.Core.CString)">
            <summary>
            GET_PLAYER_MAX_HEALTH
            </summary>
            <param name="playerSrc">
            The player handle
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerMeleeWeaponDamageModifier(CitizenFX.Core.CString)">
            <summary>
            A getter for [SET_PLAYER_MELEE_WEAPON_DAMAGE_MODIFIER](#\_0x4A3DC7ECCC321032).
            </summary>
            <param name="playerId">
            The player index.
            </param>
            <returns>
            Returns player melee weapon damage modifier value.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerName(CitizenFX.Core.CString)">
            <summary>
            GET_PLAYER_NAME
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerPed(CitizenFX.Core.CString)">
            <summary>
            Used to get the player's Ped Entity ID when a valid `playerSrc` is passed.
            </summary>
            <param name="playerSrc">
            The player source, passed as a string.
            </param>
            <returns>
            Returns a valid Ped Entity ID if the passed `playerSrc` is valid, `0` if not.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerPeerStatistics(CitizenFX.Core.CString,System.Int32)">
            <summary>
            ```cpp
            const int ENET_PACKET_LOSS_SCALE = 65536;
            
            enum PeerStatistics
            {
            	// PacketLoss will only update once every 10 seconds, use PacketLossEpoch if you want the time
            	// since the last time the packet loss was updated.
            
            	// the amount of packet loss the player has, needs to be scaled with PACKET_LOSS_SCALE
            	PacketLoss = 0,
            	// The variance in the packet loss
            	PacketLossVariance = 1,
            	// The time since the last packet update in ms, relative to the peers connection time
            	PacketLossEpoch = 2,
            	// The mean amount of time it takes for a packet to get to the client (ping)
            	RoundTripTime = 3,
            	// The variance in the round trip time
            	RoundTripTimeVariance = 4,
            	// Despite their name, these are only updated once every 5 seconds, you can get the last time this was updated with PacketThrottleEpoch
            	// The last recorded round trip time of a packet
            	LastRoundTripTime = 5,
            	// The last round trip time variance
            	LastRoundTripTimeVariance = 6,
            	// The time since the last packet throttle update, relative to the peers connection time
            	PacketThrottleEpoch = 7,
            };
            ```
            
            These statistics only update once every 10 seconds.
            </summary>
            <param name="playerSrc">
            The player to get the stats of
            </param>
            <param name="peerStatistic">
            The statistic to get, this will error if its out of range
            </param>
            <returns>
            See `ENetStatisticType` for what this will return.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerPing(CitizenFX.Core.CString)">
            <summary>
            See [GET_PLAYER_PEER_STATISTICS](#\_0x9A928294) if you want more detailed information, like packet loss, and packet/rtt variance
            </summary>
            <returns>
            Returns the mean amount of time a packet takes to get to the client
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerRoutingBucket(CitizenFX.Core.CString)">
            <summary>
            Gets the routing bucket for the specified player.
            
            Routing buckets are also known as 'dimensions' or 'virtual worlds' in past echoes, however they are population-aware.
            </summary>
            <param name="playerSrc">
            The player to get the routing bucket for.
            </param>
            <returns>
            The routing bucket ID.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerTeam(CitizenFX.Core.CString)">
            <summary>
            GET_PLAYER_TEAM
            </summary>
            <param name="playerSrc">
            The player handle
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerTimeInPursuit(CitizenFX.Core.CString,System.Boolean)">
            <summary>
            Gets the amount of time player has spent evading the cops.
            Counter starts and increments only when cops are chasing the player.
            If the player is evading, the timer will pause.
            </summary>
            <param name="playerSrc">
            The target player
            </param>
            <param name="lastPursuit">
            False = CurrentPursuit, True = LastPursuit
            </param>
            <returns>
            Returns -1, if the player is not wanted or wasn't in pursuit before, depending on the lastPursuit parameter
            Returns 0, if lastPursuit == False and the player has a wanted level, but the pursuit has not started yet
            Otherwise, will return the milliseconds of the pursuit.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerTimeOnline(CitizenFX.Core.CString)">
            <summary>
            Gets the current time online for a specified player.
            </summary>
            <param name="playerSrc">
            A player.
            </param>
            <returns>
            The current time online in seconds.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerToken(CitizenFX.Core.CString,System.Int32)">
            <summary>
            Gets a player's token. Tokens can be used to enhance banning logic, however are specific to a server.
            </summary>
            <param name="playerSrc">
            A player.
            </param>
            <param name="index">
            Index between 0 and GET_NUM_PLAYER_TOKENS.
            </param>
            <returns>
            A token value.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerWantedCentrePosition(CitizenFX.Core.CString)">
            <summary>
            Gets the current known coordinates for the specified player from cops perspective. This native is used server side when using OneSync.
            </summary>
            <param name="playerSrc">
            The target player
            </param>
            <returns>
            The player's position known by police. Vector zero if the player has no wanted level.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerWantedLevel(CitizenFX.Core.CString)">
            <summary>
            Returns given players wanted level server-side.
            </summary>
            <param name="playerSrc">
            The target player
            </param>
            <returns>
            The wanted level
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerWeaponDamageModifier(CitizenFX.Core.CString)">
            <summary>
            A getter for [SET_PLAYER_WEAPON_DAMAGE_MODIFIER](#\_0xCE07B9F7817AADA3).
            </summary>
            <param name="playerId">
            The player index.
            </param>
            <returns>
            The value of player weapon damage modifier.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerWeaponDefenseModifier(CitizenFX.Core.CString)">
            <summary>
            A getter for [SET_PLAYER_WEAPON_DEFENSE_MODIFIER](#\_0x2D83BC011CA14A3C).
            </summary>
            <param name="playerId">
            The player index.
            </param>
            <returns>
            The value of player weapon defense modifier.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerWeaponDefenseModifier_2(CitizenFX.Core.CString)">
            <summary>
            A getter for [\_SET_PLAYER_WEAPON_DEFENSE_MODIFIER\_2](#\_0xBCFDE9EDE4CF27DC).
            </summary>
            <param name="playerId">
            The player index.
            </param>
            <returns>
            The value of player weapon defense modifier 2.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetRegisteredCommands">
            <summary>
            Returns all commands that are registered in the command system.
            The data returned adheres to the following layout:
            
            ```
            [
            {
            "name": "cmdlist",
            "resource": "resource",
            "arity" = -1,
            },
            {
            "name": "command1"
            "resource": "resource_2",
            "arity" = -1,
            }
            ]
            ```
            </summary>
            <returns>
            An object containing registered commands.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetResourceByFindIndex(System.Int32)">
            <summary>
            GET_RESOURCE_BY_FIND_INDEX
            </summary>
            <param name="findIndex">
            The index of the resource (starting at 0)
            </param>
            <returns>
            The resource name as a `string`
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetResourceCommands(CitizenFX.Core.CString)">
            <summary>
            Returns all commands registered by the specified resource.
            The data returned adheres to the following layout:
            
            ```
            [
            {
            "name": "cmdlist",
            "resource": "example_resource",
            "arity" = -1,
            },
            {
            "name": "command1"
            "resource": "example_resource2",
            "arity" = -1,
            }
            ]
            ```
            </summary>
            <returns>
            An object containing registered commands.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetResourceKvpFloat(CitizenFX.Core.CString)">
            <summary>
            A getter for [SET_RESOURCE_KVP_FLOAT](#\_0x9ADD2938).
            </summary>
            <param name="key">
            The key to fetch
            </param>
            <returns>
            The floating-point value stored under the specified key, or 0.0 if not found.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetResourceKvpInt(CitizenFX.Core.CString)">
            <summary>
            A getter for [SET_RESOURCE_KVP_INT](#\_0x6A2B1E8).
            </summary>
            <param name="key">
            The key to fetch
            </param>
            <returns>
            The integer value stored under the specified key, or 0 if not found.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetResourceKvpString(CitizenFX.Core.CString)">
            <summary>
            A getter for [SET_RESOURCE_KVP](#\_0x21C7A35B).
            </summary>
            <param name="key">
            The key to fetch
            </param>
            <returns>
            The string value stored under the specified key, or nil/null if not found.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetResourceMetadata(CitizenFX.Core.CString,CitizenFX.Core.CString,System.Int32)">
            <summary>
            Gets the metadata value at a specified key/index from a resource's manifest.
            See also: [Resource manifest](https://docs.fivem.net/docs/scripting-reference/resource-manifest/resource-manifest/)
            </summary>
            <param name="resourceName">
            The resource name.
            </param>
            <param name="metadataKey">
            The key in the resource manifest.
            </param>
            <param name="index">
            The value index, in a range from \[0..GET_NUM_RESOURCE_METDATA-1].
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetResourcePath(CitizenFX.Core.CString)">
            <summary>
            Returns the physical on-disk path of the specified resource.
            </summary>
            <param name="resourceName">
            The name of the resource.
            </param>
            <returns>
            The resource directory name, possibly without trailing slash.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetResourceState(CitizenFX.Core.CString)">
            <summary>
            Returns the current state of the specified resource.
            </summary>
            <param name="resourceName">
            The name of the resource.
            </param>
            <returns>
            The resource state. One of `"missing", "started", "starting", "stopped", "stopping", "uninitialized" or "unknown"`.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetSelectedPedWeapon(System.Int32)">
            <summary>
            An alias of [GET_CURRENT_PED_WEAPON](#\_0xB0237302).
            
            Note, the client-side [GET_SELECTED_PED_WEAPON](#\_0x0A6DB4965674D243) native returns the weapon selected via the HUD (weapon wheel). This data is not available to FXServer.
            </summary>
            <param name="ped">
            The target ped.
            </param>
            <returns>
            The weapon hash.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetStateBagKeys(CitizenFX.Core.CString)">
            <summary>
            GET_STATE_BAG_KEYS
            </summary>
            <param name="bagName">
            The name of the bag.
            </param>
            <returns>
            Returns an array containing all keys for which the state bag has associated values.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetStateBagValue(CitizenFX.Core.CString,CitizenFX.Core.CString)">
            <summary>
            Returns the value of a state bag key.
            </summary>
            <returns>
            Value.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetThrusterSideRcsThrottle(System.Int32)">
            <summary>
            GET_THRUSTER_SIDE_RCS_THROTTLE
            </summary>
            <param name="jetpack">
            The jetpack to check.
            </param>
            <returns>
            Returns a value representing the side RCS (Reaction Control System) throttle of the jetpack. The values range from `0.0` (no throttle) to `1.0` (full throttle).
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetThrusterThrottle(System.Int32)">
            <summary>
            GET_THRUSTER_THROTTLE
            </summary>
            <param name="jetpack">
            The jetpack to check.
            </param>
            <returns>
            Returns a value representing the main throttle of the jetpack. The values range from `0.0` (no throttle) to `1.0` (full throttle)
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetTrainBackwardCarriage(System.Int32)">
            <summary>
            GET_TRAIN_BACKWARD_CARRIAGE
            </summary>
            <param name="train">
            The train handle
            </param>
            <returns>
            The handle of the carriage behind this train in the chain. Otherwise returns 0 if the train is the caboose of the chain.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetTrainCarriageEngine(System.Int32)">
            <summary>
            GET_TRAIN_CARRIAGE_ENGINE
            </summary>
            <param name="train">
            The entity handle.
            </param>
            <returns>
            The train engine carriage.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetTrainCarriageIndex(System.Int32)">
            <summary>
            GET_TRAIN_CARRIAGE_INDEX
            </summary>
            <param name="train">
            The entity handle.
            </param>
            <returns>
            The carriage index. -1 returned if invalid result.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetTrainCruiseSpeed(System.Int32)">
            <summary>
            Gets the trains desired speed.
            </summary>
            <param name="train">
            The train handle
            </param>
            <returns>
            The desired cruise speed of the train. Not the speed the train is currently traveling at
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetTrainDirection(System.Int32)">
            <summary>
            Gets the direction the train is facing
            </summary>
            <param name="train">
            The train handle
            </param>
            <returns>
            True if the train is moving forward on the track, False otherwise
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetTrainForwardCarriage(System.Int32)">
            <summary>
            GET_TRAIN_FORWARD_CARRIAGE
            </summary>
            <param name="train">
            The train handle
            </param>
            <returns>
            The handle of the carriage in front of this train in the chain. Otherwise returns 0 if the train has no carriage in front of it
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetTrainState(System.Int32)">
            <summary>
            GET_TRAIN_STATE
            </summary>
            <param name="train">
            The train handle
            </param>
            <returns>
            The trains current state```cpp
            enum eTrainState
            {
                MOVING = 0,
                ENTERING_STATION,
                OPENING_DOORS,
                STOPPED,
                CLOSING_DOORS,
                LEAVING_STATION,
            }
            ```
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetTrainTrackIndex(System.Int32)">
            <summary>
            GET_TRAIN_TRACK_INDEX
            </summary>
            <param name="train">
            The train handle
            </param>
            <returns>
            The track index the train is currently on.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleBodyHealth(System.Int32)">
            <summary>
            GET_VEHICLE_BODY_HEALTH
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleColours(System.Int32,System.Int32@,System.Int32@)">
            <summary>
            GET_VEHICLE_COLOURS
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleCustomPrimaryColour(System.Int32,System.Int32@,System.Int32@,System.Int32@)">
            <summary>
            GET_VEHICLE_CUSTOM_PRIMARY_COLOUR
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleCustomSecondaryColour(System.Int32,System.Int32@,System.Int32@,System.Int32@)">
            <summary>
            GET_VEHICLE_CUSTOM_SECONDARY_COLOUR
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleDashboardColour(System.Int32,System.Int32@)">
            <summary>
            GET_VEHICLE_DASHBOARD_COLOUR
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleDirtLevel(System.Int32)">
            <summary>
            GET_VEHICLE_DIRT_LEVEL
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleDoorLockStatus(System.Int32)">
            <summary>
            ```lua
            enum_VehicleLockStatus = {
                None = 0,
                Locked = 2,
                LockedForPlayer = 3,
                StickPlayerInside = 4, -- Doesn't allow players to exit the vehicle with the exit vehicle key.
                CanBeBrokenInto = 7, -- Can be broken into the car. If the glass is broken, the value will be set to 1
                CanBeBrokenIntoPersist = 8, -- Can be broken into persist
                CannotBeTriedToEnter = 10, -- Cannot be tried to enter (Nothing happens when you press the vehicle enter key).
            }
            ```
            
            It should be [noted](https://forum.cfx.re/t/4863241) that while the [client-side command](#\_0x25BC98A59C2EA962) and its
            setter distinguish between states 0 (unset) and 1 (unlocked), the game will synchronize both as state 0, so the server-side
            command will return only '0' if unlocked.
            </summary>
            <param name="vehicle">
            A vehicle handle.
            </param>
            <returns>
            The door lock status for the specified vehicle.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleDoorStatus(System.Int32,System.Int32)">
            <summary>
            Returns the open position of the specified door on the target vehicle.
            </summary>
            <param name="vehicle">
            The target vehicle.
            </param>
            <param name="doorIndex">
            Index of door to check (0-6).
            </param>
            <returns>
            A number from 0 to 7.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleDoorsLockedForPlayer(System.Int32)">
            <summary>
            Currently it only works when set to "all players".
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleEngineHealth(System.Int32)">
            <summary>
            GET_VEHICLE_ENGINE_HEALTH
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleExtraColours(System.Int32,System.Int32@,System.Int32@)">
            <summary>
            GET_VEHICLE_EXTRA_COLOURS
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleFlightNozzlePosition(System.Int32)">
            <summary>
            Gets the flight nozzel position for the specified vehicle. See the client-side [\_GET_VEHICLE_FLIGHT_NOZZLE_POSITION](#\_0xDA62027C8BDB326E) native for usage examples.
            </summary>
            <param name="vehicle">
            The vehicle to check.
            </param>
            <returns>
            The flight nozzel position between 0.0 (flying normally) and 1.0 (VTOL mode)
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleHandbrake(System.Int32)">
            <summary>
            GET_VEHICLE_HANDBRAKE
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleHeadlightsColour(System.Int32)">
            <summary>
            GET_VEHICLE_HEADLIGHTS_COLOUR
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleHomingLockonState(System.Int32)">
            <summary>
            Gets the lock on state for the specified vehicle. See the client-side [GET_VEHICLE_HOMING_LOCKON_STATE](#\_0xE6B0E8CFC3633BF0) native for a description of lock on states.
            </summary>
            <param name="vehicle">
            The vehicle to check.
            </param>
            <returns>
            The lock on state.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleHornType(System.Int32)">
            <summary>
            This is a getter for the client-side native [`START_VEHICLE_HORN`](#\_0x9C8C6504B5B63D2C), which allows you to return the horn type of the vehicle.
            
            **Note**: This native only gets the hash value set with `START_VEHICLE_HORN`. If a wrong hash is passed into `START_VEHICLE_HORN`, it will return this wrong hash.
            
            ```cpp
            enum eHornTypes
            {
                NORMAL = 1330140148,
                HELDDOWN = -2087385909,
                AGGRESSIVE = -92810745
            }
            ```
            </summary>
            <param name="vehicle">
            The vehicle to check the horn type.
            </param>
            <returns>
            Returns the vehicle horn type hash, or `0` if one is not set.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleInteriorColour(System.Int32,System.Int32@)">
            <summary>
            GET_VEHICLE_INTERIOR_COLOUR
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleLightsState(System.Int32,System.Boolean@,System.Boolean@)">
            <summary>
            GET_VEHICLE_LIGHTS_STATE
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleLivery(System.Int32)">
            <summary>
            GET_VEHICLE_LIVERY
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleLockOnTarget(System.Int32)">
            <summary>
            Gets the vehicle that is locked on to for the specified vehicle.
            </summary>
            <param name="vehicle">
            The vehicle to check.
            </param>
            <returns>
            The vehicle that is locked on. 0 returned if no vehicle is locked on.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleNeonColour(System.Int32,System.Int32@,System.Int32@,System.Int32@)">
            <summary>
            Getter to check the neon colour of a vehicle. This native is the server side getter of [GET_VEHICLE_NEON_LIGHTS_COLOUR](#\_0x7619EEE8C886757F).
            </summary>
            <param name="vehicle">
            The vehicle to check.
            </param>
            <param name="red">
            Pointer to an integer where the red component of the neon color will be stored.
            </param>
            <param name="green">
            Pointer to an integer where the green component of the neon color will be stored.
            </param>
            <param name="blue">
            Pointer to an integer where the blue component of the neon color will be stored.
            </param>
            <returns>
            None. The neon color values are retrieved and stored in the `red`, `green`, and `blue` pointers. Make sure to store the returned values in variables for further use.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleNeonEnabled(System.Int32,System.Int32)">
            <summary>
            Getter to check if one of the neon lights of a vehicle is enabled. This native is the server side getter of [IS_VEHICLE_NEON_LIGHT_ENABLED](#\_0x8C4B92553E4766A5).
            
            ```cpp
            enum neonIndex
            {
                NEON_BACK = 0,   // Back neon
                NEON_RIGHT = 1,  // Right neon
                NEON_LEFT = 2,   // Left neon
                NEON_FRONT = 3   // Front neon
            };
            ```
            </summary>
            <param name="vehicle">
            The vehicle to check.
            </param>
            <param name="neonIndex">
            A value from the neonIndex enum representing the neon light to check.
            </param>
            <returns>
            Returns `true` if the specified neon light is enabled, `false` otherwise.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleNumberPlateText(System.Int32)">
            <summary>
            GET_VEHICLE_NUMBER_PLATE_TEXT
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleNumberPlateTextIndex(System.Int32)">
            <summary>
            GET_VEHICLE_NUMBER_PLATE_TEXT_INDEX
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehiclePedIsIn(System.Int32,System.Boolean)">
            <summary>
            Gets the vehicle the specified Ped is/was in depending on bool value. This native is used server side when using OneSync.
            </summary>
            <param name="ped">
            The target ped
            </param>
            <param name="lastVehicle">
            False = CurrentVehicle, True = LastVehicle
            </param>
            <returns>
            The vehicle id. Returns 0 if the ped is/was not in a vehicle.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehiclePetrolTankHealth(System.Int32)">
            <summary>
            GET_VEHICLE_PETROL_TANK_HEALTH
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleRadioStationIndex(System.Int32)">
            <summary>
            GET_VEHICLE_RADIO_STATION_INDEX
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleRoofLivery(System.Int32)">
            <summary>
            GET_VEHICLE_ROOF_LIVERY
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleSteeringAngle(System.Int32)">
            <summary>
            GET_VEHICLE_STEERING_ANGLE
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleTotalRepairs(System.Int32)">
            <summary>
            GET_VEHICLE_TOTAL_REPAIRS
            </summary>
            <returns>
            Returns the total amount of repairs. Each repair will increase the count to make it possible to detect client repairs.
            The value has a range from 0 to 15. Next value after 15 is 0.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleType(System.Int32)">
            <summary>
            Returns the type of the passed vehicle.
            
            For client scripts, reference the more detailed [GET_VEHICLE_TYPE_RAW](#\_0xDE73BC10) native.
            
            ### Vehicle types
            
            *   automobile
            *   bike
            *   boat
            *   heli
            *   plane
            *   submarine
            *   trailer
            *   train
            </summary>
            <param name="vehicle">
            The vehicle's entity handle.
            </param>
            <returns>
            If the entity is a vehicle, the vehicle type. If it is not a vehicle, the return value will be null.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleTyreSmokeColor(System.Int32,System.Int32@,System.Int32@,System.Int32@)">
            <summary>
            GET_VEHICLE_TYRE_SMOKE_COLOR
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleWheelType(System.Int32)">
            <summary>
            GET_VEHICLE_WHEEL_TYPE
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleWindowTint(System.Int32)">
            <summary>
            GET_VEHICLE_WINDOW_TINT
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GiveWeaponComponentToPed(System.Int32,System.UInt32,System.UInt32)">
            <summary>
            GIVE_WEAPON_COMPONENT_TO_PED
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GiveWeaponToPed(System.Int32,System.UInt32,System.Int32,System.Boolean,System.Boolean)">
            <summary>
            GIVE_WEAPON_TO_PED
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.HasEntityBeenMarkedAsNoLongerNeeded(System.Int32)">
            <summary>
            HAS_ENTITY_BEEN_MARKED_AS_NO_LONGER_NEEDED
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.HasVehicleBeenDamagedByBullets(System.Int32)">
            <summary>
            HAS_VEHICLE_BEEN_DAMAGED_BY_BULLETS
            </summary>
            <param name="vehicle">
            The target vehicle.
            </param>
            <returns>
            Returns whether or not the target vehicle has been damaged by bullets.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.HasVehicleBeenOwnedByPlayer(System.Int32)">
            <summary>
            HAS_VEHICLE_BEEN_OWNED_BY_PLAYER
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsAceAllowed(CitizenFX.Core.CString)">
            <summary>
            IS_ACE_ALLOWED
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsBoatAnchoredAndFrozen(System.Int32)">
            <summary>
            IS_BOAT_ANCHORED_AND_FROZEN
            </summary>
            <param name="vehicle">
            The target vehicle.
            </param>
            <returns>
            Returns whether or not the boat is anchored and frozen.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsBoatWrecked(System.Int32)">
            <summary>
            IS_BOAT_WRECKED
            </summary>
            <param name="vehicle">
            The target vehicle.
            </param>
            <returns>
            Returns whether or not the boat is wrecked.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsDuplicityVersion">
            <summary>
            Gets whether or not this is the CitizenFX server.
            </summary>
            <returns>
            A boolean value.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsEntityPositionFrozen(System.Int32)">
            <summary>
            A getter for [FREEZE_ENTITY_POSITION](#\_0x428CA6DBD1094446).
            </summary>
            <param name="entity">
            The entity to check for
            </param>
            <returns>
            Boolean stating if it is frozen or not.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsEntityVisible(System.Int32)">
            <summary>
            This native checks if the given entity is visible.
            </summary>
            <returns>
            Returns `true` if the entity is visible, `false` otherwise.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsFlashLightOn(System.Int32)">
            <summary>
            IS_FLASH_LIGHT_ON
            </summary>
            <param name="ped">
            The target ped.
            </param>
            <returns>
            Whether or not the ped's flash light is on.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsHeliTailBoomBreakable(System.Int32)">
            <summary>
            This is a getter for [SET_HELI_TAIL_EXPLODE_THROW_DASHBOARD](#\_0x3EC8BF18AA453FE9)
            </summary>
            <param name="heli">
            The helicopter to check
            </param>
            <returns>
            Returns `true` if the helicopter's tail boom can break, `false` if it cannot.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsHeliTailBoomBroken(System.Int32)">
            <summary>
            IS_HELI_TAIL_BOOM_BROKEN
            </summary>
            <param name="heli">
            The helicopter to check
            </param>
            <returns>
            Returns `true` if the helicopter's tail boom is broken, `false` if it is intact.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsPedAPlayer(System.Int32)">
            <summary>
            This native checks if the given ped is a player.
            </summary>
            <returns>
            Returns `true` if the ped is a player, `false` otherwise.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsPedHandcuffed(System.Int32)">
            <summary>
            IS_PED_HANDCUFFED
            </summary>
            <param name="ped">
            The target ped.
            </param>
            <returns>
            Whether or not the ped is handcuffed.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsPedRagdoll(System.Int32)">
            <summary>
            IS_PED_RAGDOLL
            </summary>
            <param name="ped">
            The target ped.
            </param>
            <returns>
            Whether or not the ped is ragdolling.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsPedStrafing(System.Int32)">
            <summary>
            IS_PED_STRAFING
            </summary>
            <param name="ped">
            The target ped.
            </param>
            <returns>
            Whether or not the ped is strafing.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsPedUsingActionMode(System.Int32)">
            <summary>
            IS_PED_USING_ACTION_MODE
            </summary>
            <param name="ped">
            The target ped.
            </param>
            <returns>
            Whether or not the ped is using action mode.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsPlayerAceAllowed(CitizenFX.Core.CString,CitizenFX.Core.CString)">
            <summary>
            IS_PLAYER_ACE_ALLOWED
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsPlayerCommerceInfoLoaded(CitizenFX.Core.CString)">
            <summary>
            Requests whether or not the commerce data for the specified player has loaded.
            </summary>
            <param name="playerSrc">
            The player handle
            </param>
            <returns>
            A boolean.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsPlayerCommerceInfoLoadedExt(CitizenFX.Core.CString)">
            <summary>
            Requests whether or not the commerce data for the specified player has loaded from Tebex.
            </summary>
            <param name="playerSrc">
            The player handle
            </param>
            <returns>
            A boolean.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsPlayerEvadingWantedLevel(CitizenFX.Core.CString)">
            <summary>
            This will return true if the player is evading wanted level, meaning that the wanted level stars are blink.
            Otherwise will return false.
            
            If the player is not wanted, it simply returns false.
            </summary>
            <param name="playerSrc">
            The target player
            </param>
            <returns>
            boolean value, depending if the player is evading his wanted level or not.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsPlayerInFreeCamMode(CitizenFX.Core.CString)">
            <summary>
            IS_PLAYER_IN_FREE_CAM_MODE
            </summary>
            <param name="playerSrc">
            The player to get the free camera mode status of
            </param>
            <returns>
            Returns if the player is in free camera mode.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsPlayerUsingSuperJump(CitizenFX.Core.CString)">
            <summary>
            IS_PLAYER_USING_SUPER_JUMP
            </summary>
            <param name="playerSrc">
            The player handle
            </param>
            <returns>
            A boolean.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsPrincipalAceAllowed(CitizenFX.Core.CString,CitizenFX.Core.CString)">
            <summary>
            IS_PRINCIPAL_ACE_ALLOWED
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsTrainCaboose(System.Int32)">
            <summary>
            IS_TRAIN_CABOOSE
            </summary>
            <param name="train">
            The train handle
            </param>
            <returns>
            Returns true if the train is the caboose of the chain.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsVehicleEngineStarting(System.Int32)">
            <summary>
            IS_VEHICLE_ENGINE_STARTING
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsVehicleExtraTurnedOn(System.Int32,System.Int32)">
            <summary>
            IS_VEHICLE_EXTRA_TURNED_ON
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsVehicleSirenOn(System.Int32)">
            <summary>
            IS_VEHICLE_SIREN_ON
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsVehicleTyreBurst(System.Int32,System.Int32,System.Boolean)">
            <summary>
            IS_VEHICLE_TYRE_BURST
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsVehicleWindowIntact(System.Int32,System.Int32)">
            <summary>
            See the client-side [IS_VEHICLE_WINDOW_INTACT](#\_0x46E571A0E20D01F1) for a window indexes list.
            </summary>
            <param name="vehicle">
            The target vehicle.
            </param>
            <param name="windowIndex">
            The window index.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.LoadPlayerCommerceData(CitizenFX.Core.CString)">
            <summary>
            Requests the commerce data for the specified player, including the owned SKUs. Use `IS_PLAYER_COMMERCE_INFO_LOADED` to check if it has loaded.
            </summary>
            <param name="playerSrc">
            The player handle
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.LoadPlayerCommerceDataExt(CitizenFX.Core.CString)">
            <summary>
            Requests the commerce data from Tebex for the specified player, including the owned SKUs.
            
            Use [`IS_PLAYER_COMMERCE_INFO_LOADED_EXT`](#\_0x1D14F4FE) to check if it has loaded.
            
            This will not automatically update whenever a client purchases a package, if you want to fetch new purchases you will need to call this native again.
            
            This native will temporarily cache the players commerce data for 10 seconds, a call to this native after 10 seconds will re-fetch the players commerce data.
            </summary>
            <param name="playerSrc">
            The player handle
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.LoadResourceFile(CitizenFX.Core.CString,CitizenFX.Core.CString)">
            <summary>
            Reads the contents of a text file in a specified resource.
            If executed on the client, this file has to be included in `files` in the resource manifest.
            Example: `local data = LoadResourceFile("devtools", "data.json")`
            </summary>
            <param name="resourceName">
            The resource name.
            </param>
            <param name="fileName">
            The file in the resource.
            </param>
            <returns>
            The file contents
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.MumbleCreateChannel(System.Int32)">
            <summary>
            Create a permanent voice channel.
            </summary>
            <param name="id">
            ID of the channel.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.MumbleIsPlayerMuted(System.Int32)">
            <summary>
            Checks if the player is currently muted
            </summary>
            <param name="playerSrc">
            The player to get the mute state for
            </param>
            <returns>
            Whether or not the player is muted
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.MumbleSetPlayerMuted(System.Int32,System.Boolean)">
            <summary>
            Mutes or unmutes the specified player
            </summary>
            <param name="playerSrc">
            The player to set the mute state of
            </param>
            <param name="toggle">
            Whether to mute or unmute the player
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.NetworkGetEntityFromNetworkId(System.Int32)">
            <summary>
            NETWORK_GET_ENTITY_FROM_NETWORK_ID
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.NetworkGetEntityOwner(System.Int32)">
            <summary>
            Returns the owner ID of the specified entity.
            </summary>
            <param name="entity">
            The entity to get the owner for.
            </param>
            <returns>
            On the server, the server ID of the entity owner. On the client, returns the player/slot ID of the entity owner.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.NetworkGetFirstEntityOwner(System.Int32)">
            <summary>
            Returns the first owner ID of the specified entity.
            </summary>
            <param name="entity">
            The entity to get the first owner for.
            </param>
            <returns>
            The server ID of the first entity owner.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.NetworkGetNetworkIdFromEntity(System.Int32)">
            <summary>
            NETWORK_GET_NETWORK_ID_FROM_ENTITY
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.NetworkGetVoiceProximityOverrideForPlayer(CitizenFX.Core.CString)">
            <summary>
            NETWORK_GET_VOICE_PROXIMITY_OVERRIDE_FOR_PLAYER
            </summary>
            <param name="playerSrc">
            The player handle
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.PerformHttpRequestInternal(CitizenFX.Core.CString,System.Int32)">
            <summary>
            PERFORM_HTTP_REQUEST_INTERNAL
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.PerformHttpRequestInternalEx(System.Object)">
            <summary>
            PERFORM_HTTP_REQUEST_INTERNAL_EX
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.PrintStructuredTrace(CitizenFX.Core.CString)">
            <summary>
            Prints 'structured trace' data to the server `file descriptor 3` channel. This is not generally useful outside of
            server monitoring utilities.
            </summary>
            <param name="jsonString">
            JSON data to submit as `payload` in the `script_structured_trace` event.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.ProfilerEnterScope(CitizenFX.Core.CString)">
            <summary>
            Scope entry for profiler.
            </summary>
            <param name="scopeName">
            Scope name.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.ProfilerExitScope">
            <summary>
            Scope exit for profiler.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.ProfilerIsRecording">
            <summary>
            Returns true if the profiler is active.
            </summary>
            <returns>
            True or false.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.RegisterCommand(CitizenFX.Core.CString,System.Delegate,System.Boolean)">
            <summary>
            Registered commands can be executed by entering them in the client console (this works for client side and server side registered commands). Or by entering them in the server console/through an RCON client (only works for server side registered commands). Or if you use a supported chat resource, like the default one provided in the cfx-server-data repository, then you can enter the command in chat by prefixing it with a `/`.
            
            Commands registered using this function can also be executed by resources, using the [`ExecuteCommand` native](#\_0x561C060B).
            
            The restricted bool is not used on the client side. Permissions can only be checked on the server side, so if you want to limit your command with an ace permission automatically, make it a server command (by registering it in a server script).
            
            **Example result**:
            
            ![](https://i.imgur.com/TaCnG09.png)
            </summary>
            <param name="commandName">
            The command you want to register.
            </param>
            <param name="handler">
            A handler function that gets called whenever the command is executed.
            </param>
            <param name="restricted">
            If this is a server command and you set this to true, then players will need the command.yourCommandName ace permission to execute this command.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.RegisterConsoleListener(System.Delegate)">
            <summary>
            Registers a listener for console output messages.
            </summary>
            <param name="listener">
            A function of `(channel: string, message: string) =&gt; void`. The message might contain `\n`.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.RegisterResourceAsEventHandler(CitizenFX.Core.CString)">
            <summary>
            An internal function which allows the current resource's HLL script runtimes to receive state for the specified event.
            </summary>
            <param name="eventName">
            An event name, or "\*" to disable HLL event filtering for this resource.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.RegisterResourceAsset(CitizenFX.Core.CString,CitizenFX.Core.CString)">
            <summary>
            **Experimental**: This native may be altered or removed in future versions of CitizenFX without warning.
            
            Registers a cached resource asset with the resource system, similar to the automatic scanning of the `stream/` folder.
            </summary>
            <param name="resourceName">
            The resource to add the asset to.
            </param>
            <param name="fileName">
            A file name in the resource.
            </param>
            <returns>
            A cache string to pass to `REGISTER_STREAMING_FILE_FROM_CACHE` on the client.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.RegisterResourceBuildTaskFactory(CitizenFX.Core.CString,System.Delegate)">
            <summary>
            Registers a build task factory for resources.
            The function should return an object (msgpack map) with the following fields:
            
            ```
            {
            // returns whether the specific resource should be built
            shouldBuild = func(resourceName: string): bool,
            
            // asynchronously start building the specific resource.
            // call cb when completed
            build = func(resourceName: string, cb: func(success: bool, status: string): void): void
            }
            ```
            </summary>
            <param name="factoryId">
            The identifier for the build task.
            </param>
            <param name="factoryFn">
            The factory function.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.RemoveAllPedWeapons(System.Int32,System.Boolean)">
            <summary>
            Parameter `p1` does not seem to be used or referenced in game binaries.\
            **Note:** When called for networked entities, a `CRemoveAllWeaponsEvent` will be created per request.
            </summary>
            <param name="ped">
            The ped entity
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.RemoveBlip(System.Int32@)">
            <summary>
            Removes the blip from your map.
            
            **Note:** This function only works on the script that created the blip, if you wish to remove blips created by other scripts, see [`SET_THIS_SCRIPT_CAN_REMOVE_BLIPS_CREATED_BY_ANY_SCRIPT`](#\_0x86A652570E5F25DD).
            </summary>
            <param name="blip">
            Blip handle to remove.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.RemoveConvarChangeListener(System.Int32)">
            <summary>
            REMOVE_CONVAR_CHANGE_LISTENER
            </summary>
            <param name="cookie">
            The cookie returned from [ADD_CONVAR_CHANGE_LISTENER](#\_0xAB7F7241)
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.RemoveStateBagChangeHandler(System.Int32)">
            <summary>
            **Experimental**: This native may be altered or removed in future versions of CitizenFX without warning.
            
            Removes a handler for changes to a state bag.
            </summary>
            <param name="cookie">
            The cookie.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.RemoveWeaponComponentFromPed(System.Int32,System.UInt32,System.UInt32)">
            <summary>
            REMOVE_WEAPON_COMPONENT_FROM_PED
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.RemoveWeaponFromPed(System.Int32,System.UInt32)">
            <summary>
            This native removes a specified weapon from your selected ped.  
            Weapon Hashes: pastebin.com/0wwDZgkF  
            Example:  
            C#:  
            Function.Call(Hash.REMOVE_WEAPON_FROM_PED, Game.Player.Character, 0x99B507EA);  
            C++:  
            WEAPON::REMOVE_WEAPON_FROM_PED(PLAYER::PLAYER_PED_ID(), 0x99B507EA);  
            The code above removes the knife from the player.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.RequestPlayerCommerceSession(CitizenFX.Core.CString,System.Int32)">
            <summary>
            Requests the specified player to buy the passed SKU. This'll pop up a prompt on the client, which upon acceptance
            will open the browser prompting further purchase details.
            </summary>
            <param name="playerSrc">
            The player handle
            </param>
            <param name="skuId">
            The ID of the SKU.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SaveResourceFile(CitizenFX.Core.CString,CitizenFX.Core.CString,CitizenFX.Core.CString,System.Int32)">
            <summary>
            Writes the specified data to a file in the specified resource.
            Using a length of `-1` will automatically detect the length assuming the data is a C string.
            </summary>
            <param name="resourceName">
            The name of the resource.
            </param>
            <param name="fileName">
            The name of the file.
            </param>
            <param name="data">
            The data to write to the file.
            </param>
            <param name="dataLength">
            The length of the written data.
            </param>
            <returns>
            A value indicating if the write succeeded.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.ScanResourceRoot(CitizenFX.Core.CString,System.Delegate)">
            <summary>
            Scans the resources in the specified resource root. This function is only available in the 'monitor mode' process and is
            not available for user resources.
            </summary>
            <param name="rootPath">
            The resource directory to scan.
            </param>
            <param name="callback">
            A callback that will receive an object with results.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.ScheduleResourceTick(CitizenFX.Core.CString)">
            <summary>
            Schedules the specified resource to run a tick as soon as possible, bypassing the server's fixed tick rate.
            </summary>
            <param name="resourceName">
            The resource to tick.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetBlipSprite(System.Int32,System.Int32)">
            <summary>
            Sets the displayed sprite for a specific blip.
            
            There's a [list of sprites](https://docs.fivem.net/game-references/blips/) on the FiveM documentation site.
            </summary>
            <param name="blip">
            The blip to change.
            </param>
            <param name="spriteId">
            The sprite ID to set.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetConvar(CitizenFX.Core.CString,CitizenFX.Core.CString)">
            <summary>
            SET_CONVAR
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetConvarReplicated(CitizenFX.Core.CString,CitizenFX.Core.CString)">
            <summary>
            Used to replicate a server variable onto clients.
            </summary>
            <param name="varName">
            The console variable name.
            </param>
            <param name="_value">
            The value to set for the given console variable.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetConvarServerInfo(CitizenFX.Core.CString,CitizenFX.Core.CString)">
            <summary>
            SET_CONVAR_SERVER_INFO
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetCurrentPedWeapon(System.Int32,System.UInt32,System.Boolean)">
            <summary>
            SET_CURRENT_PED_WEAPON
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetEntityCoords(System.Int32,System.Single,System.Single,System.Single,System.Boolean,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Sets the coordinates (world position) for a specified entity, offset by the radius of the entity on the Z axis.
            </summary>
            <param name="entity">
            The entity to change coordinates for.
            </param>
            <param name="xPos">
            The X coordinate.
            </param>
            <param name="yPos">
            The Y coordinate.
            </param>
            <param name="zPos">
            The Z coordinate, ground level.
            </param>
            <param name="alive">
            Unused by the game, potentially used by debug builds of GTA in order to assert whether or not an entity was alive.
            </param>
            <param name="deadFlag">
            Whether to disable physics for dead peds, too, and not just living peds.
            </param>
            <param name="ragdollFlag">
            A special flag used for ragdolling peds.
            </param>
            <param name="clearArea">
            Whether to clear any entities in the target area.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetEntityDistanceCullingRadius(System.Int32,System.Single)">
            <summary>
            It overrides the default distance culling radius of an entity. Set to `0.0` to reset.
            If you want to interact with an entity outside of your players' scopes set the radius to a huge number.
            
            **WARNING**: Culling natives are deprecated and have known, [unfixable issues](https://forum.cfx.re/t/issue-with-culling-radius-and-server-side-entities/4900677/4)
            </summary>
            <param name="entity">
            The entity handle to override the distance culling radius.
            </param>
            <param name="radius">
            The new distance culling radius.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetEntityHeading(System.Int32,System.Single)">
            <summary>
            Set the heading of an entity in degrees also known as "Yaw".
            </summary>
            <param name="entity">
            The entity to set the heading for.
            </param>
            <param name="heading">
            The heading in degrees.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetEntityIgnoreRequestControlFilter(System.Int32,System.Boolean)">
            <summary>
            It allows to flag an entity to ignore the request control filter policy.
            </summary>
            <param name="entity">
            The entity handle to ignore the request control filter.
            </param>
            <param name="ignore">
            Define if the entity ignores the request control filter policy.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetEntityOrphanMode(System.Int32,System.Int32)">
            <summary>
            ```cpp
            enum EntityOrphanMode {
                // Default, this will delete the entity when it isn't relevant to any players
                // NOTE: this *doesn't* mean when they're no longer in scope
                DeleteWhenNotRelevant = 0,
                // The entity will be deleted whenever its original owner disconnects
                // NOTE: if this is set when the entities original owner has already left it will be
                // marked for deletion (similar to just calling DELETE_ENTITY)
                DeleteOnOwnerDisconnect = 1,
                // The entity will never be deleted by the server when it does relevancy checks
                // you should only use this on entities that need to be relatively persistent
                KeepEntity = 2
            }
            ```
            
            Sets what the server will do when the entity no longer has its original owner. By default the server will cleanup entities that it considers "no longer relevant".
            
            When used on trains, this native will recursively call onto all attached carriages.
            
            **NOTE**: When used with `KeepEntity` (2) this native only guarantees that the ***server*** will not delete the entity, client requests to delete the entity will still work perfectly fine.
            </summary>
            <param name="entity">
            The entity to set the orphan mode of
            </param>
            <param name="orphanMode">
            The mode that the server should use for determining if an entity should be removed.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetEntityRemoteSyncedScenesAllowed(System.Int32,System.Boolean)">
            <summary>
            Enables or disables the owner check for the specified entity in network-synchronized scenes. When set to `false`, the entity cannot participate in synced scenes initiated by clients that do not own the entity.
            
            By default, this is `false` for all entities, meaning only the entity's owner can include it in networked synchronized scenes.
            </summary>
            <param name="entity">
            The entity to set the flag for.
            </param>
            <param name="allow">
            Whether to allow remote synced scenes for the entity.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetEntityRotation(System.Int32,System.Single,System.Single,System.Single,System.Int32,System.Boolean)">
            <summary>
            Sets the rotation of a specified entity in the game world.
            
            ```
            NativeDB Introduced: v323
            ```
            </summary>
            <param name="entity">
            The entity to rotate.
            </param>
            <param name="pitch">
            The pitch (X-axis) rotation in degrees.
            </param>
            <param name="roll">
            The roll (Y-axis) rotation in degrees.
            </param>
            <param name="yaw">
            The yaw (Z-axis) rotation in degrees.
            </param>
            <param name="rotationOrder">
            Specifies the order in which yaw, pitch, and roll are applied, see [`GET_ENTITY_ROTATION`](#\_0xAFBD61CC738D9EB9) for the available rotation orders.
            </param>
            <param name="bDeadCheck">
            Usually set to `true`. Determines whether to check if the entity is dead before applying the rotation.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetEntityRoutingBucket(System.Int32,System.Int32)">
            <summary>
            Sets the routing bucket for the specified entity.
            
            Routing buckets are also known as 'dimensions' or 'virtual worlds' in past echoes, however they are population-aware.
            </summary>
            <param name="entity">
            The entity to set the routing bucket for.
            </param>
            <param name="bucket">
            The bucket ID.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetEntityVelocity(System.Int32,System.Single,System.Single,System.Single)">
            <summary>
            Note that the third parameter(denoted as z) is "up and down" with positive numbers encouraging upwards movement.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetGameType(CitizenFX.Core.CString)">
            <summary>
            SET_GAME_TYPE
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetHttpHandler(System.Delegate)">
            <summary>
            Sets the handler for HTTP requests made to the executing resource.
            
            Example request URL: `http://localhost:30120/http-test/ping` - this request will be sent to the `http-test` resource with the `/ping` path.
            
            The handler function assumes the following signature:
            
            ```ts
            function HttpHandler(
              request: {
                address: string;
                headers: Record&lt;string, string&gt;;
                method: string;
                path: string;
                setDataHandler(handler: (data: string) =&gt; void): void;
                setDataHandler(handler: (data: ArrayBuffer) =&gt; void, binary: 'binary'): void;
                setCancelHandler(handler: () =&gt; void): void;
              },
              response: {
                writeHead(code: number, headers?: Record&lt;string, string | string[]&gt;): void;
                write(data: string): void;
                send(data?: string): void;
              }
            ): void;
            ```
            
            *   **request**: The request object.
                *   **address**: The IP address of the request sender.
                *   **path**: The path to where the request was sent.
                *   **headers**: The headers sent with the request.
                *   **method**: The request method.
                *   **setDataHandler**: Sets the handler for when a data body is passed with the request. Additionally you can pass the `'binary'` argument to receive a `BufferArray` in JavaScript or `System.Byte[]` in C# (has no effect in Lua).
                *   **setCancelHandler**: Sets the handler for when the request is cancelled.
            *   **response**: An object to control the response.
                *   **writeHead**: Sets the status code &amp; headers of the response. Can be only called once and won't work if called after running other response functions.
                *   **write**: Writes to the response body without sending it. Can be called multiple times.
                *   **send**: Writes to the response body and then sends it along with the status code &amp; headers, finishing the request.
            </summary>
            <param name="handler">
            The handler function.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetMapName(CitizenFX.Core.CString)">
            <summary>
            SET_MAP_NAME
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedAmmo(System.Int32,System.UInt32,System.Int32)">
            <summary>
            NativeDB Added Parameter 4: BOOL p3
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedArmour(System.Int32,System.Int32)">
            <summary>
            Sets the armor of the specified ped.  
            ped: The Ped to set the armor of.  
            amount: A value between 0 and 100 indicating the value to set the Ped's armor to.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedCanRagdoll(System.Int32,System.Boolean)">
            <summary>
            SET_PED_CAN_RAGDOLL
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedComponentVariation(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            This native is used to set component variation on a ped. Components, drawables and textures IDs are related to the ped model.
            
            ### MP Freemode list of components
            
            **0**: Face
            **1**: Mask
            **2**: Hair
            **3**: Torso
            **4**: Leg
            **5**: Parachute / bag
            **6**: Shoes
            **7**: Accessory
            **8**: Undershirt
            **9**: Kevlar
            **10**: Badge
            **11**: Torso 2
            
            List of Component IDs
            
            ```cpp
            // Components
            enum ePedVarComp
            {
                PV_COMP_INVALID = 0xFFFFFFFF,
                PV_COMP_HEAD = 0, // "HEAD"
                PV_COMP_BERD = 1, // "BEARD"
                PV_COMP_HAIR = 2, // "HAIR"
                PV_COMP_UPPR = 3, // "UPPER"
                PV_COMP_LOWR = 4, // "LOWER"
                PV_COMP_HAND = 5, // "HAND"
                PV_COMP_FEET = 6, // "FEET"
                PV_COMP_TEEF = 7, // "TEETH"
                PV_COMP_ACCS = 8, // "ACCESSORIES"
                PV_COMP_TASK = 9, // "TASK"
                PV_COMP_DECL = 10, // "DECL"
                PV_COMP_JBIB = 11, // "JBIB"
                PV_COMP_MAX = 12,
            };
            ```
            </summary>
            <param name="ped">
            The ped handle.
            </param>
            <param name="componentId">
            The component that you want to set.
            </param>
            <param name="drawableId">
            The drawable id that is going to be set. Refer to [GET_NUMBER_OF_PED_DRAWABLE_VARIATIONS](#\_0x27561561732A7842).
            </param>
            <param name="textureId">
            The texture id of the drawable. Refer to [GET_NUMBER_OF_PED_TEXTURE_VARIATIONS](#\_0x8F7156A3142A6BAD).
            </param>
            <param name="paletteId">
            0 to 3.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedConfigFlag(System.Int32,System.Int32,System.Boolean)">
            <summary>
            cpp
            // Potential names and hash collisions included as comments
            enum ePedConfigFlags {
            	CPED_CONFIG_FLAG_CreatedByFactory = 0,
            	CPED_CONFIG_FLAG_CanBeShotInVehicle = 1,
            	CPED_CONFIG_FLAG_NoCriticalHits = 2,
            	CPED_CONFIG_FLAG_DrownsInWater = 3,
            	CPED_CONFIG_FLAG_DrownsInSinkingVehicle = 4,
            	CPED_CONFIG_FLAG_DiesInstantlyWhenSwimming = 5,
            	CPED_CONFIG_FLAG_HasBulletProofVest = 6,
            	CPED_CONFIG_FLAG_UpperBodyDamageAnimsOnly = 7,
            	CPED_CONFIG_FLAG_NeverFallOffSkis = 8,
            	CPED_CONFIG_FLAG_NeverEverTargetThisPed = 9,
            	CPED_CONFIG_FLAG_ThisPedIsATargetPriority = 10,
            	CPED_CONFIG_FLAG_TargettableWithNoLos = 11,
            	CPED_CONFIG_FLAG_DoesntListenToPlayerGroupCommands = 12,
            	CPED_CONFIG_FLAG_NeverLeavesGroup = 13,
            	CPED_CONFIG_FLAG_DoesntDropWeaponsWhenDead = 14,
            	CPED_CONFIG_FLAG_SetDelayedWeaponAsCurrent = 15,
            	CPED_CONFIG_FLAG_KeepTasksAfterCleanUp = 16,
            	CPED_CONFIG_FLAG_BlockNonTemporaryEvents = 17,
            	CPED_CONFIG_FLAG_HasAScriptBrain = 18,
            	CPED_CONFIG_FLAG_WaitingForScriptBrainToLoad = 19,
            	CPED_CONFIG_FLAG_AllowMedicsToReviveMe = 20,
            	CPED_CONFIG_FLAG_MoneyHasBeenGivenByScript = 21,
            	CPED_CONFIG_FLAG_NotAllowedToCrouch = 22,
            	CPED_CONFIG_FLAG_DeathPickupsPersist = 23,
            	CPED_CONFIG_FLAG_IgnoreSeenMelee = 24,
            	CPED_CONFIG_FLAG_ForceDieIfInjured = 25,
            	CPED_CONFIG_FLAG_DontDragMeOutCar = 26,
            	CPED_CONFIG_FLAG_StayInCarOnJack = 27,
            	CPED_CONFIG_FLAG_ForceDieInCar = 28,
            	CPED_CONFIG_FLAG_GetOutUndriveableVehicle = 29,
            	CPED_CONFIG_FLAG_WillRemainOnBoatAfterMissionEnds = 30,
            	CPED_CONFIG_FLAG_DontStoreAsPersistent = 31,
            	CPED_CONFIG_FLAG_WillFlyThroughWindscreen = 32,
            	CPED_CONFIG_FLAG_DieWhenRagdoll = 33,
            	CPED_CONFIG_FLAG_HasHelmet = 34,
            	CPED_CONFIG_FLAG_UseHelmet = 35,
            	CPED_CONFIG_FLAG_DontTakeOffHelmet = 36,
            	CPED_CONFIG_FLAG_HideInCutscene = 37,
            	CPED_CONFIG_FLAG_PedIsEnemyToPlayer = 38,
            	CPED_CONFIG_FLAG_DisableEvasiveDives = 39,
            	CPED_CONFIG_FLAG_PedGeneratesDeadBodyEvents = 40,
            	CPED_CONFIG_FLAG_DontAttackPlayerWithoutWantedLevel = 41,
            	CPED_CONFIG_FLAG_DontInfluenceWantedLevel = 42,
            	CPED_CONFIG_FLAG_DisablePlayerLockon = 43,
            	CPED_CONFIG_FLAG_DisableLockonToRandomPeds = 44,
            	CPED_CONFIG_FLAG_AllowLockonToFriendlyPlayers = 45,
            	_0xDB115BFA = 46,
            	CPED_CONFIG_FLAG_PedBeingDeleted = 47,
            	CPED_CONFIG_FLAG_BlockWeaponSwitching = 48,
            	CPED_CONFIG_FLAG_BlockGroupPedAimedAtResponse = 49,
            	CPED_CONFIG_FLAG_WillFollowLeaderAnyMeans = 50,
            	CPED_CONFIG_FLAG_BlippedByScript = 51,
            	CPED_CONFIG_FLAG_DrawRadarVisualField = 52,
            	CPED_CONFIG_FLAG_StopWeaponFiringOnImpact = 53,
            	CPED_CONFIG_FLAG_DissableAutoFallOffTests = 54,
            	CPED_CONFIG_FLAG_SteerAroundDeadBodies = 55,
            	CPED_CONFIG_FLAG_ConstrainToNavMesh = 56,
            	CPED_CONFIG_FLAG_SyncingAnimatedProps = 57,
            	CPED_CONFIG_FLAG_IsFiring = 58,
            	CPED_CONFIG_FLAG_WasFiring = 59,
            	CPED_CONFIG_FLAG_IsStanding = 60,
            	CPED_CONFIG_FLAG_WasStanding = 61,
            	CPED_CONFIG_FLAG_InVehicle = 62,
            	CPED_CONFIG_FLAG_OnMount = 63,
            	CPED_CONFIG_FLAG_AttachedToVehicle = 64,
            	CPED_CONFIG_FLAG_IsSwimming = 65,
            	CPED_CONFIG_FLAG_WasSwimming = 66,
            	CPED_CONFIG_FLAG_IsSkiing = 67,
            	CPED_CONFIG_FLAG_IsSitting = 68,
            	CPED_CONFIG_FLAG_KilledByStealth = 69,
            	CPED_CONFIG_FLAG_KilledByTakedown = 70,
            	CPED_CONFIG_FLAG_Knockedout = 71,
            	CPED_CONFIG_FLAG_ClearRadarBlipOnDeath = 72,
            	CPED_CONFIG_FLAG_JustGotOffTrain = 73,
            	CPED_CONFIG_FLAG_JustGotOnTrain = 74,
            	CPED_CONFIG_FLAG_UsingCoverPoint = 75,
            	CPED_CONFIG_FLAG_IsInTheAir = 76,
            	CPED_CONFIG_FLAG_KnockedUpIntoAir = 77,
            	CPED_CONFIG_FLAG_IsAimingGun = 78,
            	CPED_CONFIG_FLAG_HasJustLeftCar = 79,
            	CPED_CONFIG_FLAG_TargetWhenInjuredAllowed = 80,
            	CPED_CONFIG_FLAG_CurrLeftFootCollNM = 81,
            	CPED_CONFIG_FLAG_PrevLeftFootCollNM = 82,
            	CPED_CONFIG_FLAG_CurrRightFootCollNM = 83,
            	CPED_CONFIG_FLAG_PrevRightFootCollNM = 84,
            	CPED_CONFIG_FLAG_HasBeenBumpedInCar = 85,
            	CPED_CONFIG_FLAG_InWaterTaskQuitToClimbLadder = 86,
            	CPED_CONFIG_FLAG_NMTwoHandedWeaponBothHandsConstrained = 87,
            	CPED_CONFIG_FLAG_CreatedBloodPoolTimer = 88,
            	CPED_CONFIG_FLAG_DontActivateRagdollFromAnyPedImpact = 89,
            	CPED_CONFIG_FLAG_GroupPedFailedToEnterCover = 90,
            	CPED_CONFIG_FLAG_AlreadyChattedOnPhone = 91,
            	CPED_CONFIG_FLAG_AlreadyReactedToPedOnRoof = 92,
            	CPED_CONFIG_FLAG_ForcePedLoadCover = 93,
            	CPED_CONFIG_FLAG_BlockCoweringInCover = 94,
            	CPED_CONFIG_FLAG_BlockPeekingInCover = 95,
            	CPED_CONFIG_FLAG_JustLeftCarNotCheckedForDoors = 96,
            	CPED_CONFIG_FLAG_VaultFromCover = 97,
            	CPED_CONFIG_FLAG_AutoConversationLookAts = 98,
            	CPED_CONFIG_FLAG_UsingCrouchedPedCapsule = 99,
            	CPED_CONFIG_FLAG_HasDeadPedBeenReported = 100,
            	CPED_CONFIG_FLAG_ForcedAim = 101,
            	CPED_CONFIG_FLAG_SteersAroundPeds = 102,
            	CPED_CONFIG_FLAG_SteersAroundObjects = 103,
            	CPED_CONFIG_FLAG_OpenDoorArmIK = 104,
            	CPED_CONFIG_FLAG_ForceReload = 105,
            	CPED_CONFIG_FLAG_DontActivateRagdollFromVehicleImpact = 106,
            	CPED_CONFIG_FLAG_DontActivateRagdollFromBulletImpact = 107,
            	CPED_CONFIG_FLAG_DontActivateRagdollFromExplosions = 108,
            	CPED_CONFIG_FLAG_DontActivateRagdollFromFire = 109,
            	CPED_CONFIG_FLAG_DontActivateRagdollFromElectrocution = 110,
            	CPED_CONFIG_FLAG_IsBeingDraggedToSafety = 111,
            	CPED_CONFIG_FLAG_HasBeenDraggedToSafety = 112,
            	CPED_CONFIG_FLAG_KeepWeaponHolsteredUnlessFired = 113,
            	CPED_CONFIG_FLAG_ForceScriptControlledKnockout = 114,
            	CPED_CONFIG_FLAG_FallOutOfVehicleWhenKilled = 115,
            	CPED_CONFIG_FLAG_GetOutBurningVehicle = 116,
            	CPED_CONFIG_FLAG_BumpedByPlayer = 117,
            	CPED_CONFIG_FLAG_RunFromFiresAndExplosions = 118,
            	CPED_CONFIG_FLAG_TreatAsPlayerDuringTargeting = 119,
            	CPED_CONFIG_FLAG_IsHandCuffed = 120,
            	CPED_CONFIG_FLAG_IsAnkleCuffed = 121,
            	CPED_CONFIG_FLAG_DisableMelee = 122,
            	CPED_CONFIG_FLAG_DisableUnarmedDrivebys = 123,
            	CPED_CONFIG_FLAG_JustGetsPulledOutWhenElectrocuted = 124,
            	CPED_CONFIG_FLAG_UNUSED_REPLACE_ME = 125,
            	CPED_CONFIG_FLAG_WillNotHotwireLawEnforcementVehicle = 126,
            	CPED_CONFIG_FLAG_WillCommandeerRatherThanJack = 127,
            	CPED_CONFIG_FLAG_CanBeAgitated = 128,
            	CPED_CONFIG_FLAG_ForcePedToFaceLeftInCover = 129,
            	CPED_CONFIG_FLAG_ForcePedToFaceRightInCover = 130,
            	CPED_CONFIG_FLAG_BlockPedFromTurningInCover = 131,
            	CPED_CONFIG_FLAG_KeepRelationshipGroupAfterCleanUp = 132,
            	CPED_CONFIG_FLAG_ForcePedToBeDragged = 133,
            	CPED_CONFIG_FLAG_PreventPedFromReactingToBeingJacked = 134,
            	CPED_CONFIG_FLAG_IsScuba = 135,
            	CPED_CONFIG_FLAG_WillArrestRatherThanJack = 136,
            	CPED_CONFIG_FLAG_RemoveDeadExtraFarAway = 137,
            	CPED_CONFIG_FLAG_RidingTrain = 138,
            	CPED_CONFIG_FLAG_ArrestResult = 139,
            	CPED_CONFIG_FLAG_CanAttackFriendly = 140,
            	CPED_CONFIG_FLAG_WillJackAnyPlayer = 141,
            	CPED_CONFIG_FLAG_BumpedByPlayerVehicle = 142,
            	CPED_CONFIG_FLAG_DodgedPlayerVehicle = 143,
            	CPED_CONFIG_FLAG_WillJackWantedPlayersRatherThanStealCar = 144,
            	CPED_CONFIG_FLAG_NoCopWantedAggro = 145,
            	CPED_CONFIG_FLAG_DisableLadderClimbing = 146,
            	CPED_CONFIG_FLAG_StairsDetected = 147,
            	CPED_CONFIG_FLAG_SlopeDetected = 148,
            	CPED_CONFIG_FLAG_HelmetHasBeenShot = 149,
            	CPED_CONFIG_FLAG_CowerInsteadOfFlee = 150,
            	CPED_CONFIG_FLAG_CanActivateRagdollWhenVehicleUpsideDown = 151,
            	CPED_CONFIG_FLAG_AlwaysRespondToCriesForHelp = 152,
            	CPED_CONFIG_FLAG_DisableBloodPoolCreation = 153,
            	CPED_CONFIG_FLAG_ShouldFixIfNoCollision = 154,
            	CPED_CONFIG_FLAG_CanPerformArrest = 155,
            	CPED_CONFIG_FLAG_CanPerformUncuff = 156,
            	CPED_CONFIG_FLAG_CanBeArrested = 157,
            	CPED_CONFIG_FLAG_MoverConstrictedByOpposingCollisions = 158,
            	CPED_CONFIG_FLAG_PlayerPreferFrontSeatMP = 159,
            	CPED_CONFIG_FLAG_DontActivateRagdollFromImpactObject = 160,
            	CPED_CONFIG_FLAG_DontActivateRagdollFromMelee = 161,
            	CPED_CONFIG_FLAG_DontActivateRagdollFromWaterJet = 162,
            	CPED_CONFIG_FLAG_DontActivateRagdollFromDrowning = 163,
            	CPED_CONFIG_FLAG_DontActivateRagdollFromFalling = 164,
            	CPED_CONFIG_FLAG_DontActivateRagdollFromRubberBullet = 165,
            	CPED_CONFIG_FLAG_IsInjured = 166,
            	CPED_CONFIG_FLAG_DontEnterVehiclesInPlayersGroup = 167,
            	CPED_CONFIG_FLAG_SwimmingTasksRunning = 168,
            	CPED_CONFIG_FLAG_PreventAllMeleeTaunts = 169,
            	CPED_CONFIG_FLAG_ForceDirectEntry = 170,
            	CPED_CONFIG_FLAG_AlwaysSeeApproachingVehicles = 171,
            	CPED_CONFIG_FLAG_CanDiveAwayFromApproachingVehicles = 172,
            	CPED_CONFIG_FLAG_AllowPlayerToInterruptVehicleEntryExit = 173,
            	CPED_CONFIG_FLAG_OnlyAttackLawIfPlayerIsWanted = 174,
            	CPED_CONFIG_FLAG_PlayerInContactWithKinematicPed = 175,
            	CPED_CONFIG_FLAG_PlayerInContactWithSomethingOtherThanKinematicPed = 176,
            	CPED_CONFIG_FLAG_PedsJackingMeDontGetIn = 177,
            	CPED_CONFIG_FLAG_AdditionalRappellingPed = 178,
            	CPED_CONFIG_FLAG_PedIgnoresAnimInterruptEvents = 179,
            	CPED_CONFIG_FLAG_IsInCustody = 180,
            	CPED_CONFIG_FLAG_ForceStandardBumpReactionThresholds = 181,
            	CPED_CONFIG_FLAG_LawWillOnlyAttackIfPlayerIsWanted = 182,
            	CPED_CONFIG_FLAG_IsAgitated = 183,
            	CPED_CONFIG_FLAG_PreventAutoShuffleToDriversSeat = 184,
            	CPED_CONFIG_FLAG_UseKinematicModeWhenStationary = 185,
            	CPED_CONFIG_FLAG_EnableWeaponBlocking = 186,
            	CPED_CONFIG_FLAG_HasHurtStarted = 187,
            	CPED_CONFIG_FLAG_DisableHurt = 188,
            	CPED_CONFIG_FLAG_PlayerIsWeird = 189,
            	CPED_CONFIG_FLAG_PedHadPhoneConversation = 190,
            	CPED_CONFIG_FLAG_BeganCrossingRoad = 191,
            	CPED_CONFIG_FLAG_WarpIntoLeadersVehicle = 192,
            	CPED_CONFIG_FLAG_DoNothingWhenOnFootByDefault = 193,
            	CPED_CONFIG_FLAG_UsingScenario = 194,
            	CPED_CONFIG_FLAG_VisibleOnScreen = 195,
            	CPED_CONFIG_FLAG_DontCollideWithKinematic = 196,
            	CPED_CONFIG_FLAG_ActivateOnSwitchFromLowPhysicsLod = 197,
            	CPED_CONFIG_FLAG_DontActivateRagdollOnPedCollisionWhenDead = 198,
            	CPED_CONFIG_FLAG_DontActivateRagdollOnVehicleCollisionWhenDead = 199,
            	CPED_CONFIG_FLAG_HasBeenInArmedCombat = 200,
            	CPED_CONFIG_FLAG_UseDiminishingAmmoRate = 201,
            	CPED_CONFIG_FLAG_Avoidance_Ignore_All = 202,
            	CPED_CONFIG_FLAG_Avoidance_Ignored_by_All = 203,
            	CPED_CONFIG_FLAG_Avoidance_Ignore_Group1 = 204,
            	CPED_CONFIG_FLAG_Avoidance_Member_of_Group1 = 205,
            	CPED_CONFIG_FLAG_ForcedToUseSpecificGroupSeatIndex = 206,
            	CPED_CONFIG_FLAG_LowPhysicsLodMayPlaceOnNavMesh = 207,
            	CPED_CONFIG_FLAG_DisableExplosionReactions = 208,
            	CPED_CONFIG_FLAG_DodgedPlayer = 209,
            	CPED_CONFIG_FLAG_WaitingForPlayerControlInterrupt = 210,
            	CPED_CONFIG_FLAG_ForcedToStayInCover = 211,
            	CPED_CONFIG_FLAG_GeneratesSoundEvents = 212,
            	CPED_CONFIG_FLAG_ListensToSoundEvents = 213,
            	CPED_CONFIG_FLAG_AllowToBeTargetedInAVehicle = 214,
            	CPED_CONFIG_FLAG_WaitForDirectEntryPointToBeFreeWhenExiting = 215,
            	CPED_CONFIG_FLAG_OnlyRequireOnePressToExitVehicle = 216,
            	CPED_CONFIG_FLAG_ForceExitToSkyDive = 217,
            	CPED_CONFIG_FLAG_SteersAroundVehicles = 218,
            	CPED_CONFIG_FLAG_AllowPedInVehiclesOverrideTaskFlags = 219,
            	CPED_CONFIG_FLAG_DontEnterLeadersVehicle = 220,
            	CPED_CONFIG_FLAG_DisableExitToSkyDive = 221,
            	CPED_CONFIG_FLAG_ScriptHasDisabledCollision = 222,
            	CPED_CONFIG_FLAG_UseAmbientModelScaling = 223,
            	CPED_CONFIG_FLAG_DontWatchFirstOnNextHurryAway = 224,
            	CPED_CONFIG_FLAG_DisablePotentialToBeWalkedIntoResponse = 225,
            	CPED_CONFIG_FLAG_DisablePedAvoidance = 226,
            	CPED_CONFIG_FLAG_ForceRagdollUponDeath = 227,
            	CPED_CONFIG_FLAG_CanLosePropsOnDamage = 228,
            	CPED_CONFIG_FLAG_DisablePanicInVehicle = 229,
            	CPED_CONFIG_FLAG_AllowedToDetachTrailer = 230,
            	CPED_CONFIG_FLAG_HasShotBeenReactedToFromFront = 231,
            	CPED_CONFIG_FLAG_HasShotBeenReactedToFromBack = 232,
            	CPED_CONFIG_FLAG_HasShotBeenReactedToFromLeft = 233,
            	CPED_CONFIG_FLAG_HasShotBeenReactedToFromRight = 234,
            	CPED_CONFIG_FLAG_AllowBlockDeadPedRagdollActivation = 235,
            	CPED_CONFIG_FLAG_IsHoldingProp = 236,
            	CPED_CONFIG_FLAG_BlocksPathingWhenDead = 237,
            	CPED_CONFIG_FLAG_ForcePlayNormalScenarioExitOnNextScriptCommand = 238,
            	CPED_CONFIG_FLAG_ForcePlayImmediateScenarioExitOnNextScriptCommand = 239,
            	CPED_CONFIG_FLAG_ForceSkinCharacterCloth = 240,
            	CPED_CONFIG_FLAG_LeaveEngineOnWhenExitingVehicles = 241,
            	CPED_CONFIG_FLAG_PhoneDisableTextingAnimations = 242,
            	CPED_CONFIG_FLAG_PhoneDisableTalkingAnimations = 243,
            	CPED_CONFIG_FLAG_PhoneDisableCameraAnimations = 244,
            	CPED_CONFIG_FLAG_DisableBlindFiringInShotReactions = 245,
            	CPED_CONFIG_FLAG_AllowNearbyCoverUsage = 246,
            	CPED_CONFIG_FLAG_InStrafeTransition = 247,
            	CPED_CONFIG_FLAG_CanPlayInCarIdles = 248,
            	CPED_CONFIG_FLAG_CanAttackNonWantedPlayerAsLaw = 249,
            	CPED_CONFIG_FLAG_WillTakeDamageWhenVehicleCrashes = 250,
            	CPED_CONFIG_FLAG_AICanDrivePlayerAsRearPassenger = 251,
            	CPED_CONFIG_FLAG_PlayerCanJackFriendlyPlayers = 252,
            	CPED_CONFIG_FLAG_OnStairs = 253,
            	CPED_CONFIG_FLAG_SimulatingAiming = 254,
            	CPED_CONFIG_FLAG_AIDriverAllowFriendlyPassengerSeatEntry = 255,
            	CPED_CONFIG_FLAG_ParentCarIsBeingRemoved = 256,
            	CPED_CONFIG_FLAG_AllowMissionPedToUseInjuredMovement = 257,
            	CPED_CONFIG_FLAG_CanLoseHelmetOnDamage = 258,
            	CPED_CONFIG_FLAG_NeverDoScenarioExitProbeChecks = 259,
            	CPED_CONFIG_FLAG_SuppressLowLODRagdollSwitchWhenCorpseSettles = 260,
            	CPED_CONFIG_FLAG_PreventUsingLowerPrioritySeats = 261,
            	CPED_CONFIG_FLAG_JustLeftVehicleNeedsReset = 262,
            	CPED_CONFIG_FLAG_TeleportIfCantReachPlayer = 263,
            	CPED_CONFIG_FLAG_PedsInVehiclePositionNeedsReset = 264,
            	CPED_CONFIG_FLAG_PedsFullyInSeat = 265,
            	CPED_CONFIG_FLAG_AllowPlayerLockOnIfFriendly = 266,
            	CPED_CONFIG_FLAG_UseCameraHeadingForDesiredDirectionLockOnTest = 267,
            	CPED_CONFIG_FLAG_TeleportToLeaderVehicle = 268,
            	CPED_CONFIG_FLAG_Avoidance_Ignore_WeirdPedBuffer = 269,
            	CPED_CONFIG_FLAG_OnStairSlope = 270,
            	CPED_CONFIG_FLAG_HasPlayedNMGetup = 271,
            	CPED_CONFIG_FLAG_DontBlipCop = 272,
            	CPED_CONFIG_FLAG_SpawnedAtExtendedRangeScenario = 273,
            	CPED_CONFIG_FLAG_WalkAlongsideLeaderWhenClose = 274,
            	CPED_CONFIG_FLAG_KillWhenTrapped = 275,
            	CPED_CONFIG_FLAG_EdgeDetected = 276,
            	CPED_CONFIG_FLAG_AlwaysWakeUpPhysicsOfIntersectedPeds = 277,
            	CPED_CONFIG_FLAG_EquippedAmbientLoadOutWeapon = 278,
            	CPED_CONFIG_FLAG_AvoidTearGas = 279,
            	CPED_CONFIG_FLAG_StoppedSpeechUponFreezing = 280,
            	CPED_CONFIG_FLAG_DisableGoToWritheWhenInjured = 281,
            	CPED_CONFIG_FLAG_OnlyUseForcedSeatWhenEnteringHeliInGroup = 282,
            	CPED_CONFIG_FLAG_ThrownFromVehicleDueToExhaustion = 283,
            	CPED_CONFIG_FLAG_UpdateEnclosedSearchRegion = 284,
            	CPED_CONFIG_FLAG_DisableWeirdPedEvents = 285,
            	CPED_CONFIG_FLAG_ShouldChargeNow = 286,
            	CPED_CONFIG_FLAG_RagdollingOnBoat = 287,
            	CPED_CONFIG_FLAG_HasBrandishedWeapon = 288,
            	CPED_CONFIG_FLAG_AllowMinorReactionsAsMissionPed = 289,
            	CPED_CONFIG_FLAG_BlockDeadBodyShockingEventsWhenDead = 290,
            	CPED_CONFIG_FLAG_PedHasBeenSeen = 291,
            	CPED_CONFIG_FLAG_PedIsInReusePool = 292,
            	CPED_CONFIG_FLAG_PedWasReused = 293,
            	CPED_CONFIG_FLAG_DisableShockingEvents = 294,
            	CPED_CONFIG_FLAG_MovedUsingLowLodPhysicsSinceLastActive = 295,
            	CPED_CONFIG_FLAG_NeverReactToPedOnRoof = 296,
            	CPED_CONFIG_FLAG_ForcePlayFleeScenarioExitOnNextScriptCommand = 297,
            	CPED_CONFIG_FLAG_JustBumpedIntoVehicle = 298,
            	CPED_CONFIG_FLAG_DisableShockingDrivingOnPavementEvents = 299,
            	CPED_CONFIG_FLAG_ShouldThrowSmokeNow = 300,
            	CPED_CONFIG_FLAG_DisablePedConstraints = 301,
            	CPED_CONFIG_FLAG_ForceInitialPeekInCover = 302,
            	CPED_CONFIG_FLAG_CreatedByDispatch = 303,
            	CPED_CONFIG_FLAG_PointGunLeftHandSupporting = 304,
            	CPED_CONFIG_FLAG_DisableJumpingFromVehiclesAfterLeader = 305,
            	CPED_CONFIG_FLAG_DontActivateRagdollFromPlayerPedImpact = 306,
            	CPED_CONFIG_FLAG_DontActivateRagdollFromAiRagdollImpact = 307,
            	CPED_CONFIG_FLAG_DontActivateRagdollFromPlayerRagdollImpact = 308,
            	CPED_CONFIG_FLAG_DisableQuadrupedSpring = 309,
            	CPED_CONFIG_FLAG_IsInCluster = 310,
            	CPED_CONFIG_FLAG_ShoutToGroupOnPlayerMelee = 311,
            	CPED_CONFIG_FLAG_IgnoredByAutoOpenDoors = 312,
            	CPED_CONFIG_FLAG_PreferInjuredGetup = 313,
            	CPED_CONFIG_FLAG_ForceIgnoreMeleeActiveCombatant = 314,
            	CPED_CONFIG_FLAG_CheckLoSForSoundEvents = 315,
            	CPED_CONFIG_FLAG_JackedAbandonedCar = 316,
            	CPED_CONFIG_FLAG_CanSayFollowedByPlayerAudio = 317,
            	CPED_CONFIG_FLAG_ActivateRagdollFromMinorPlayerContact = 318,
            	CPED_CONFIG_FLAG_HasPortablePickupAttached = 319,
            	CPED_CONFIG_FLAG_ForcePoseCharacterCloth = 320,
            	CPED_CONFIG_FLAG_HasClothCollisionBounds = 321,
            	CPED_CONFIG_FLAG_HasHighHeels = 322,
            	CPED_CONFIG_FLAG_TreatAsAmbientPedForDriverLockOn = 323,
            	CPED_CONFIG_FLAG_DontBehaveLikeLaw = 324,
            	CPED_CONFIG_FLAG_SpawnedAtScenario = 325,
            	CPED_CONFIG_FLAG_DisablePoliceInvestigatingBody = 326,
            	CPED_CONFIG_FLAG_DisableWritheShootFromGround = 327,
            	CPED_CONFIG_FLAG_LowerPriorityOfWarpSeats = 328,
            	CPED_CONFIG_FLAG_DisableTalkTo = 329,
            	CPED_CONFIG_FLAG_DontBlip = 330,
            	CPED_CONFIG_FLAG_IsSwitchingWeapon = 331,
            	CPED_CONFIG_FLAG_IgnoreLegIkRestrictions = 332,
            	CPED_CONFIG_FLAG_ScriptForceNoTimesliceIntelligenceUpdate = 333,
            	CPED_CONFIG_FLAG_JackedOutOfMyVehicle = 334,
            	CPED_CONFIG_FLAG_WentIntoCombatAfterBeingJacked = 335,
            	CPED_CONFIG_FLAG_DontActivateRagdollForVehicleGrab = 336,
            	CPED_CONFIG_FLAG_ForcePackageCharacterCloth = 337,
            	CPED_CONFIG_FLAG_DontRemoveWithValidOrder = 338,
            	CPED_CONFIG_FLAG_AllowTaskDoNothingTimeslicing = 339,
            	CPED_CONFIG_FLAG_ForcedToStayInCoverDueToPlayerSwitch = 340,
            	CPED_CONFIG_FLAG_ForceProneCharacterCloth = 341,
            	CPED_CONFIG_FLAG_NotAllowedToJackAnyPlayers = 342,
            	CPED_CONFIG_FLAG_InToStrafeTransition = 343,
            	CPED_CONFIG_FLAG_KilledByStandardMelee = 344,
            	CPED_CONFIG_FLAG_AlwaysLeaveTrainUponArrival = 345,
            	CPED_CONFIG_FLAG_ForcePlayDirectedNormalScenarioExitOnNextScriptCommand = 346,
            	CPED_CONFIG_FLAG_OnlyWritheFromWeaponDamage = 347,
            	CPED_CONFIG_FLAG_UseSloMoBloodVfx = 348,
            	CPED_CONFIG_FLAG_EquipJetpack = 349,
            	CPED_CONFIG_FLAG_PreventDraggedOutOfCarThreatResponse = 350,
            	CPED_CONFIG_FLAG_ScriptHasCompletelyDisabledCollision = 351,
            	CPED_CONFIG_FLAG_NeverDoScenarioNavChecks = 352,
            	CPED_CONFIG_FLAG_ForceSynchronousScenarioExitChecking = 353,
            	CPED_CONFIG_FLAG_ThrowingGrenadeWhileAiming = 354,
            	CPED_CONFIG_FLAG_HeadbobToRadioEnabled = 355,
            	CPED_CONFIG_FLAG_ForceDeepSurfaceCheck = 356,
            	CPED_CONFIG_FLAG_DisableDeepSurfaceAnims = 357,
            	CPED_CONFIG_FLAG_DontBlipNotSynced = 358,
            	CPED_CONFIG_FLAG_IsDuckingInVehicle = 359,
            	CPED_CONFIG_FLAG_PreventAutoShuffleToTurretSeat = 360,
            	CPED_CONFIG_FLAG_DisableEventInteriorStatusCheck = 361,
            	CPED_CONFIG_FLAG_HasReserveParachute = 362,
            	CPED_CONFIG_FLAG_UseReserveParachute = 363,
            	CPED_CONFIG_FLAG_TreatDislikeAsHateWhenInCombat = 364,
            	CPED_CONFIG_FLAG_OnlyUpdateTargetWantedIfSeen = 365,
            	CPED_CONFIG_FLAG_AllowAutoShuffleToDriversSeat = 366,
            	CPED_CONFIG_FLAG_DontActivateRagdollFromSmokeGrenade = 367,
            	CPED_CONFIG_FLAG_LinkMBRToOwnerOnChain = 368,
            	CPED_CONFIG_FLAG_AmbientFriendBumpedByPlayer = 369,
            	CPED_CONFIG_FLAG_AmbientFriendBumpedByPlayerVehicle = 370,
            	CPED_CONFIG_FLAG_InFPSUnholsterTransition = 371,
            	CPED_CONFIG_FLAG_PreventReactingToSilencedCloneBullets = 372,
            	CPED_CONFIG_FLAG_DisableInjuredCryForHelpEvents = 373,
            	CPED_CONFIG_FLAG_NeverLeaveTrain = 374,
            	CPED_CONFIG_FLAG_DontDropJetpackOnDeath = 375,
            	CPED_CONFIG_FLAG_UseFPSUnholsterTransitionDuringCombatRoll = 376,
            	CPED_CONFIG_FLAG_ExitingFPSCombatRoll = 377,
            	CPED_CONFIG_FLAG_ScriptHasControlOfPlayer = 378,
            	CPED_CONFIG_FLAG_PlayFPSIdleFidgetsForProjectile = 379,
            	CPED_CONFIG_FLAG_DisableAutoEquipHelmetsInBikes = 380,
            	CPED_CONFIG_FLAG_DisableAutoEquipHelmetsInAircraft = 381,
            	CPED_CONFIG_FLAG_WasPlayingFPSGetup = 382,
            	CPED_CONFIG_FLAG_WasPlayingFPSMeleeActionResult = 383,
            	CPED_CONFIG_FLAG_PreferNoPriorityRemoval = 384,
            	CPED_CONFIG_FLAG_FPSFidgetsAbortedOnFire = 385,
            	CPED_CONFIG_FLAG_ForceFPSIKWithUpperBodyAnim = 386,
            	CPED_CONFIG_FLAG_SwitchingCharactersInFirstPerson = 387,
            	CPED_CONFIG_FLAG_IsClimbingLadder = 388,
            	CPED_CONFIG_FLAG_HasBareFeet = 389,
            	CPED_CONFIG_FLAG_UNUSED_REPLACE_ME_2 = 390,
            	CPED_CONFIG_FLAG_GoOnWithoutVehicleIfItIsUnableToGetBackToRoad = 391,
            	CPED_CONFIG_FLAG_BlockDroppingHealthSnacksOnDeath = 392,
            	CPED_CONFIG_FLAG_ResetLastVehicleOnVehicleExit = 393,
            	CPED_CONFIG_FLAG_ForceThreatResponseToNonFriendToFriendMeleeActions = 394,
            	CPED_CONFIG_FLAG_DontRespondToRandomPedsDamage = 395,
            	CPED_CONFIG_FLAG_AllowContinuousThreatResponseWantedLevelUpdates = 396,
            	CPED_CONFIG_FLAG_KeepTargetLossResponseOnCleanup = 397,
            	CPED_CONFIG_FLAG_PlayersDontDragMeOutOfCar = 398,
            	CPED_CONFIG_FLAG_BroadcastRepondedToThreatWhenGoingToPointShooting = 399,
            	CPED_CONFIG_FLAG_IgnorePedTypeForIsFriendlyWith = 400,
            	CPED_CONFIG_FLAG_TreatNonFriendlyAsHateWhenInCombat = 401,
            	CPED_CONFIG_FLAG_DontLeaveVehicleIfLeaderNotInVehicle = 402,
            	CPED_CONFIG_FLAG_ChangeFromPermanentToAmbientPopTypeOnMigration = 403,
            	CPED_CONFIG_FLAG_AllowMeleeReactionIfMeleeProofIsOn = 404,
            	CPED_CONFIG_FLAG_UsingLowriderLeans = 405,
            	CPED_CONFIG_FLAG_UsingAlternateLowriderLeans = 406,
            	CPED_CONFIG_FLAG_UseNormalExplosionDamageWhenBlownUpInVehicle = 407,
            	CPED_CONFIG_FLAG_DisableHomingMissileLockForVehiclePedInside = 408,
            	CPED_CONFIG_FLAG_DisableTakeOffScubaGear = 409,
            	CPED_CONFIG_FLAG_IgnoreMeleeFistWeaponDamageMult = 410,
            	CPED_CONFIG_FLAG_LawPedsCanFleeFromNonWantedPlayer = 411,
            	CPED_CONFIG_FLAG_ForceBlipSecurityPedsIfPlayerIsWanted = 412,
            	CPED_CONFIG_FLAG_IsHolsteringWeapon = 413,
            	CPED_CONFIG_FLAG_UseGoToPointForScenarioNavigation = 414,
            	CPED_CONFIG_FLAG_DontClearLocalPassengersWantedLevel = 415,
            	CPED_CONFIG_FLAG_BlockAutoSwapOnWeaponPickups = 416,
            	CPED_CONFIG_FLAG_ThisPedIsATargetPriorityForAI = 417,
            	CPED_CONFIG_FLAG_IsSwitchingHelmetVisor = 418,
            	CPED_CONFIG_FLAG_ForceHelmetVisorSwitch = 419,
            	CPED_CONFIG_FLAG_IsPerformingVehicleMelee = 420,
            	CPED_CONFIG_FLAG_UseOverrideFootstepPtFx = 421,
            	CPED_CONFIG_FLAG_DisableVehicleCombat = 422,
            	CPED_CONFIG_FLAG_TreatAsFriendlyForTargetingAndDamage = 423,
            	CPED_CONFIG_FLAG_AllowBikeAlternateAnimations = 424,
            	CPED_CONFIG_FLAG_TreatAsFriendlyForTargetingAndDamageNonSynced = 425,
            	CPED_CONFIG_FLAG_UseLockpickVehicleEntryAnimations = 426,
            	CPED_CONFIG_FLAG_IgnoreInteriorCheckForSprinting = 427,
            	CPED_CONFIG_FLAG_SwatHeliSpawnWithinLastSpottedLocation = 428,
            	CPED_CONFIG_FLAG_DisableStartEngine = 429,
            	CPED_CONFIG_FLAG_IgnoreBeingOnFire = 430,
            	CPED_CONFIG_FLAG_DisableTurretOrRearSeatPreference = 431,
            	CPED_CONFIG_FLAG_DisableWantedHelicopterSpawning = 432,
            	CPED_CONFIG_FLAG_UseTargetPerceptionForCreatingAimedAtEvents = 433,
            	CPED_CONFIG_FLAG_DisableHomingMissileLockon = 434,
            	CPED_CONFIG_FLAG_ForceIgnoreMaxMeleeActiveSupportCombatants = 435,
            	CPED_CONFIG_FLAG_StayInDefensiveAreaWhenInVehicle = 436,
            	CPED_CONFIG_FLAG_DontShoutTargetPosition = 437,
            	CPED_CONFIG_FLAG_DisableHelmetArmor = 438,
            	CPED_CONFIG_FLAG_CreatedByConcealedPlayer = 439,
            	CPED_CONFIG_FLAG_PermanentlyDisablePotentialToBeWalkedIntoResponse = 440,
            	CPED_CONFIG_FLAG_PreventVehExitDueToInvalidWeapon = 441,
            	CPED_CONFIG_FLAG_IgnoreNetSessionFriendlyFireCheckForAllowDamage = 442,
            	CPED_CONFIG_FLAG_DontLeaveCombatIfTargetPlayerIsAttackedByPolice = 443,
            	CPED_CONFIG_FLAG_CheckLockedBeforeWarp = 444,
            	CPED_CONFIG_FLAG_DontShuffleInVehicleToMakeRoom = 445,
            	CPED_CONFIG_FLAG_GiveWeaponOnGetup = 446,
            	CPED_CONFIG_FLAG_DontHitVehicleWithProjectiles = 447,
            	CPED_CONFIG_FLAG_DisableForcedEntryForOpenVehiclesFromTryLockedDoor = 448,
            	CPED_CONFIG_FLAG_FiresDummyRockets = 449,
            	CPED_CONFIG_FLAG_PedIsArresting = 450,
            	CPED_CONFIG_FLAG_IsDecoyPed = 451,
            	CPED_CONFIG_FLAG_HasEstablishedDecoy = 452,
            	CPED_CONFIG_FLAG_BlockDispatchedHelicoptersFromLanding = 453,
            	CPED_CONFIG_FLAG_DontCryForHelpOnStun = 454,
            	CPED_CONFIG_FLAG_HitByTranqWeapon = 455,
            	CPED_CONFIG_FLAG_CanBeIncapacitated = 456,
            	CPED_CONFIG_FLAG_ForcedAimFromArrest = 457,
            	CPED_CONFIG_FLAG_DontChangeTargetFromMelee = 458,
            	_0x4376ABF2 = 459,
            	CPED_CONFIG_FLAG_RagdollFloatsIndefinitely = 460,
            	CPED_CONFIG_FLAG_BlockElectricWeaponDamage = 461,
            	_0x262A3B8E = 462,
            	_0x1AA79A25 = 463,
            }
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedDefaultComponentVariation(System.Int32)">
            <summary>
            Sets Ped Default Clothes
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedEyeColor(System.Int32,System.Int32)">
            <summary>
            Used for freemode (online) characters.
            
            Indices:
            
            1.  black
            2.  very light blue/green
            3.  dark blue
            4.  brown
            5.  darker brown
            6.  light brown
            7.  blue
            8.  light blue
            9.  pink
            10. yellow
            11. purple
            12. black
            13. dark green
            14. light brown
            15. yellow/black pattern
            16. light colored spiral pattern
            17. shiny red
            18. shiny half blue/half red
            19. half black/half light blue
            20. white/red perimter
            21. green snake
            22. red snake
            23. dark blue snake
            24. dark yellow
            25. bright yellow
            26. all black
            27. red small pupil
            28. devil blue/black
            29. white small pupil
            30. glossed over
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedFaceFeature(System.Int32,System.Int32,System.Single)">
            <summary>
            Sets the various freemode face features, e.g. nose length, chin shape.
            
            **Indexes (From 0 to 19):**
            
            Parentheses indicate morph scale/direction as in (-1.0 to 1.0)
            
            *   **0**: Nose Width (Thin/Wide)
            *   **1**: Nose Peak (Up/Down)
            *   **2**: Nose Length (Long/Short)
            *   **3**: Nose Bone Curveness (Crooked/Curved)
            *   **4**: Nose Tip (Up/Down)
            *   **5**: Nose Bone Twist (Left/Right)
            *   **6**: Eyebrow (Up/Down)
            *   **7**: Eyebrow (In/Out)
            *   **8**: Cheek Bones (Up/Down)
            *   **9**: Cheek Sideways Bone Size (In/Out)
            *   **10**: Cheek Bones Width (Puffed/Gaunt)
            *   **11**: Eye Opening (Both) (Wide/Squinted)
            *   **12**: Lip Thickness (Both) (Fat/Thin)
            *   **13**: Jaw Bone Width (Narrow/Wide)
            *   **14**: Jaw Bone Shape (Round/Square)
            *   **15**: Chin Bone (Up/Down)
            *   **16**: Chin Bone Length (In/Out or Backward/Forward)
            *   **17**: Chin Bone Shape (Pointed/Square)
            *   **18**: Chin Hole (Chin Bum)
            *   **19**: Neck Thickness (Thin/Thick)
            
            **Note:**
            
            You may need to call [`SetPedHeadBlendData`](#\_0x9414E18B9434C2FE) prior to calling this native in order for it to work.
            </summary>
            <param name="ped">
            The ped entity
            </param>
            <param name="index">
            An integer ranging from 0 to 19
            </param>
            <param name="scale">
            A float ranging from -1.0 to 1.0
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedHairTint(System.Int32,System.Int32,System.Int32)">
            <summary>
            Sets the tint index for the hair on the specified ped.
            
            ```
            NativeDB Introduced: v323
            ```
            </summary>
            <param name="ped">
            The Ped whose hair tint is to be set.
            </param>
            <param name="colorID">
            The tint index for the primary hair color.
            </param>
            <param name="highlightColorID">
            The tint index for the hair highlight color.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedHairColor(System.Int32,System.Int32,System.Int32)">
            <summary>
            Sets the tint index for the hair on the specified ped.
            
            ```
            NativeDB Introduced: v323
            ```
            </summary>
            <param name="ped">
            The Ped whose hair tint is to be set.
            </param>
            <param name="colorID">
            The tint index for the primary hair color.
            </param>
            <param name="highlightColorID">
            The tint index for the hair highlight color.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedHeadBlendData(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Single,System.Single,System.Single,System.Boolean)">
            <summary>
            For more info please refer to [this](https://gtaforums.com/topic/858970-all-gtao-face-ids-pedset-ped-head-blend-data-explained) topic.
            
            **Other information:**
            
            IDs start at zero and go Male Non-DLC, Female Non-DLC, Male DLC, and Female DLC.&lt;/br&gt;
            
            This native function is often called prior to calling natives such as:
            
            *   [`SetPedHairColor`](#\_0xA23FE32C)
            *   [`SetPedHeadOverlayColor`](#\_0x78935A27)
            *   [`SetPedHeadOverlay`](#\_0xD28DBA90)
            *   [`SetPedFaceFeature`](#\_0x6C8D4458)
            </summary>
            <param name="ped">
            The ped entity
            </param>
            <param name="shapeFirstID">
            Controls the shape of the first ped's face
            </param>
            <param name="shapeSecondID">
            Controls the shape of the second ped's face
            </param>
            <param name="shapeThirdID">
            Controls the shape of the third ped's face
            </param>
            <param name="skinFirstID">
            Controls the first id's skin tone
            </param>
            <param name="skinSecondID">
            Controls the second id's skin tone
            </param>
            <param name="skinThirdID">
            Controls the third id's skin tone
            </param>
            <param name="shapeMix">
            0.0 - 1.0 Of whose characteristics to take Mother -&gt; Father (shapeFirstID and shapeSecondID)
            </param>
            <param name="skinMix">
            0.0 - 1.0 Of whose characteristics to take Mother -&gt; Father (skinFirstID and skinSecondID)
            </param>
            <param name="thirdMix">
            Overrides the others in favor of the third IDs.
            </param>
            <param name="isParent">
            IsParent is set for "children" of the player character's grandparents during old-gen character creation. It has unknown effect otherwise.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedHeadOverlay(System.Int32,System.Int32,System.Int32,System.Single)">
            <summary>
            ```
            OverlayID ranges from 0 to 12, index from 0 to _GET_NUM_OVERLAY_VALUES(overlayID)-1, and opacity from 0.0 to 1.0.   
            overlayID       Part                  Index, to disable  
            0               Blemishes             0 - 23, 255  
            1               Facial Hair           0 - 28, 255  
            2               Eyebrows              0 - 33, 255  
            3               Ageing                0 - 14, 255  
            4               Makeup                0 - 74, 255  
            5               Blush                 0 - 6, 255  
            6               Complexion            0 - 11, 255  
            7               Sun Damage            0 - 10, 255  
            8               Lipstick              0 - 9, 255  
            9               Moles/Freckles        0 - 17, 255  
            10              Chest Hair            0 - 16, 255  
            11              Body Blemishes        0 - 11, 255  
            12              Add Body Blemishes    0 - 1, 255  
            ```
            
            **Note:**
            
            You may need to call [`SetPedHeadBlendData`](#\_0x9414E18B9434C2FE) prior to calling this native in order for it to work.
            </summary>
            <param name="ped">
            The ped entity
            </param>
            <param name="overlayID">
            The overlay id displayed up above.
            </param>
            <param name="index">
            An integer representing the index (from 0 to `_GET_NUM_OVERLAY_VALUES(overlayID)-1`)
            </param>
            <param name="opacity">
            A float ranging from 0.0 to 1.0
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedHeadOverlayColor(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            ```
            Used for freemode (online) characters. 
            Called after SET_PED_HEAD_OVERLAY().  
            ```
            
            **Note:**
            
            You may need to call [`SetPedHeadBlendData`](#\_0x9414E18B9434C2FE) prior to calling this native in order for it to work.
            </summary>
            <param name="ped">
            The ped entity
            </param>
            <param name="overlayID">
            An integer representing the overlay id
            </param>
            <param name="colorType">
            1 for eyebrows, beards, makeup, and chest hair; 2 for blush and lipstick; and 0 otherwise, though not called in those cases.
            </param>
            <param name="colorID">
            An integer representing the primary color id
            </param>
            <param name="secondColorID">
            An integer representing the secondary color id
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedIntoVehicle(System.Int32,System.Int32,System.Int32)">
            <summary>
            SET_PED_INTO_VEHICLE
            </summary>
            <param name="seatIndex">
            See eSeatPosition declared in [`IS_VEHICLE_SEAT_FREE`](#\_0x22AC59A870E6A669). -2 for the first available seat.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedPropIndex(System.Int32,System.Int32,System.Int32,System.Int32,System.Boolean)">
            <summary>
            This native is used to set prop variation on a ped. Components, drawables and textures IDs are related to the ped model.
            
            ### MP Freemode list of props
            
            **0**: Hats
            **1**: Glasses
            **2**: Ears
            **6**: Watches
            **7**: Bracelets
            
            List of Prop IDs
            
            ```cpp
            enum eAnchorPoints
            {
                ANCHOR_HEAD = 0, // "p_head"
                ANCHOR_EYES = 1, // "p_eyes"
                ANCHOR_EARS = 2, // "p_ears"
                ANCHOR_MOUTH = 3, // "p_mouth"
                ANCHOR_LEFT_HAND = 4, // "p_lhand"
                ANCHOR_RIGHT_HAND = 5, // "p_rhand"
                ANCHOR_LEFT_WRIST = 6, // "p_lwrist"
                ANCHOR_RIGHT_WRIST = 7, // "p_rwrist"
                ANCHOR_HIP = 8, // "p_lhip"
                ANCHOR_LEFT_FOOT = 9, // "p_lfoot"
                ANCHOR_RIGHT_FOOT = 10, // "p_rfoot"
                ANCHOR_PH_L_HAND = 11, // "ph_lhand"
                ANCHOR_PH_R_HAND = 12, // "ph_rhand"
                NUM_ANCHORS = 13,
            };
            ```
            </summary>
            <param name="ped">
            The ped handle.
            </param>
            <param name="componentId">
            The component that you want to set. Refer to [SET_PED_COMPONENT_VARIATION](#\_0x262B14F48D29DE80).
            </param>
            <param name="drawableId">
            The drawable id that is going to be set. Refer to [GET_NUMBER_OF_PED_PROP_DRAWABLE_VARIATIONS](#\_0x5FAF9754E789FB47).
            </param>
            <param name="textureId">
            The texture id of the drawable. Refer to [GET_NUMBER_OF_PED_PROP_TEXTURE_VARIATIONS](#\_0xA6E7F1CEB523E171).
            </param>
            <param name="attach">
            Attached or not.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedRandomComponentVariation(System.Int32,System.Int32)">
            <summary>
            p1 is always 0 in R* scripts; and a quick disassembly seems to indicate that p1 is unused.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedRandomProps(System.Int32)">
            <summary>
            SET_PED_RANDOM_PROPS
            </summary>
            <param name="ped">
            The ped handle.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedResetFlag(System.Int32,System.Int32,System.Boolean)">
            <summary>
            PED::SET_PED_RESET_FLAG(PLAYER::PLAYER_PED_ID(), 240, 1);
            Known values:
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedToRagdoll(System.Int32,System.Int32,System.Int32,System.Int32,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            p4/p5: Unusued in TU27
            
            ### Ragdoll Types
            
            **0**: CTaskNMRelax
            **1**: CTaskNMScriptControl: Hardcoded not to work in networked environments.
            **Else**: CTaskNMBalance
            </summary>
            <param name="ped">
            The ped to ragdoll.
            </param>
            <param name="minTime">
            Time(ms) Ped is in ragdoll mode; only applies to ragdoll types 0 and not 1.
            </param>
            <param name="bAbortIfInjured">
            unused
            </param>
            <param name="bAbortIfDead">
            unused
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedToRagdollWithFall(System.Int32,System.Int32,System.Int32,System.Int32,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)">
            <summary>
            cpp
            enum eNMFallType {
                TYPE_FROM_HIGH = 0,
                TYPE_OVER_WALL = 1,
                TYPE_DOWN_STAIRS = 2,
                TYPE_DIE_TYPES = 3,
                TYPE_DIE_FROM_HIGH = 4,
                TYPE_DIE_OVER_WALL = 5,
                TYPE_DIE_DOWN_STAIRS = 6
            }
            ```
            
            ```
            Return variable is never used in R*'s scripts.  
            Not sure what p2 does. It seems like it would be a time judging by it's usage in R*'s scripts, but didn't seem to affect anything in my testings.  
            x, y, and z are coordinates, most likely to where the ped will fall.  
            p7 is probably the force of the fall, but untested, so I left the variable name the same.  
            p8 to p13 are always 0f in R*'s scripts.  
            (Simplified) Example of the usage of the function from R*'s scripts:  
            ped::set_ped_to_ragdoll_with_fall(ped, 1500, 2000, 1, -entity::get_entity_forward_vector(ped), 1f, 0f, 0f, 0f, 0f, 0f, 0f);
            </summary>
            <param name="ped">
            The ped to ragdoll.
            </param>
            <param name="nFallType">
            The type of fall.
            </param>
            <param name="dirX">
            The x direction of the fall.
            </param>
            <param name="dirY">
            The y direction of the fall.
            </param>
            <param name="dirZ">
            The z direction of the fall.
            </param>
            <param name="fGroundHeight">
            The ground height (z).
            </param>
            <param name="grab1X">
            unused
            </param>
            <param name="grab1Y">
            unused
            </param>
            <param name="grab1Z">
            unused
            </param>
            <param name="grab2X">
            unused
            </param>
            <param name="grab2Y">
            unused
            </param>
            <param name="grab2Z">
            unused
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPlayerControl(CitizenFX.Core.CString,System.Boolean,System.Int32)">
            <summary>
            Flags:
            SPC_AMBIENT_SCRIPT = (1 &lt;&lt; 1),
            SPC_CLEAR_TASKS = (1 &lt;&lt; 2),
            SPC_REMOVE_FIRES = (1 &lt;&lt; 3),
            SPC_REMOVE_EXPLOSIONS = (1 &lt;&lt; 4),
            SPC_REMOVE_PROJECTILES = (1 &lt;&lt; 5),
            SPC_DEACTIVATE_GADGETS = (1 &lt;&lt; 6),
            SPC_REENABLE_CONTROL_ON_DEATH = (1 &lt;&lt; 7),
            SPC_LEAVE_CAMERA_CONTROL_ON = (1 &lt;&lt; 8),
            SPC_ALLOW_PLAYER_DAMAGE = (1 &lt;&lt; 9),
            SPC_DONT_STOP_OTHER_CARS_AROUND_PLAYER = (1 &lt;&lt; 10),
            SPC_PREVENT_EVERYBODY_BACKOFF = (1 &lt;&lt; 11),
            SPC_ALLOW_PAD_SHAKE = (1 &lt;&lt; 12)
            See: https://alloc8or.re/gta5/doc/enums/eSetPlayerControlFlag.txt
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPlayerCullingRadius(CitizenFX.Core.CString,System.Single)">
            <summary>
            Sets the culling radius for the specified player.
            Set to `0.0` to reset.
            
            **WARNING**: Culling natives are deprecated and have known, [unfixable issues](https://forum.cfx.re/t/issue-with-culling-radius-and-server-side-entities/4900677/4)
            </summary>
            <param name="playerSrc">
            The player to set the culling radius for.
            </param>
            <param name="radius">
            The radius.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPlayerInvincible(CitizenFX.Core.CString,System.Boolean)">
            <summary>
            Make the player impervious to all forms of damage.
            </summary>
            <param name="player">
            The player index.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPlayerModel(CitizenFX.Core.CString,System.UInt32)">
            <summary>
            Set the model for a specific Player. Note that this will destroy the current Ped for the Player and create a new one, any reference to the old ped will be invalid after calling this.
            
            As per usual, make sure to request the model first and wait until it has loaded.
            </summary>
            <param name="player">
            The player to set the model for
            </param>
            <param name="model">
            The model to use
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPlayerRoutingBucket(CitizenFX.Core.CString,System.Int32)">
            <summary>
            Sets the routing bucket for the specified player.
            
            Routing buckets are also known as 'dimensions' or 'virtual worlds' in past echoes, however they are population-aware.
            </summary>
            <param name="playerSrc">
            The player to set the routing bucket for.
            </param>
            <param name="bucket">
            The bucket ID.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPlayerWantedLevel(CitizenFX.Core.CString,System.Int32,System.Boolean)">
            <summary>
            SET_PLAYER_WANTED_LEVEL
            </summary>
            <param name="player">
            the target player
            </param>
            <param name="wantedLevel">
            the wanted level 1-5
            </param>
            <param name="delayedResponse">
            false = 0-10sec police spawn response time, true = 10-20sec police spawn response time
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetResourceKvp(CitizenFX.Core.CString,CitizenFX.Core.CString)">
            <summary>
            A setter for [GET_RESOURCE_KVP_STRING](#\_0x5240DA5A).
            </summary>
            <param name="key">
            The key to set
            </param>
            <param name="_value">
            The value to write
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetResourceKvpFloat(CitizenFX.Core.CString,System.Single)">
            <summary>
            A setter for [GET_RESOURCE_KVP_FLOAT](#\_0x35BDCEEA).
            </summary>
            <param name="key">
            The key to set
            </param>
            <param name="_value">
            The value to write
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetResourceKvpFloatNoSync(CitizenFX.Core.CString,System.Single)">
            <summary>
            Nonsynchronous [SET_RESOURCE_KVP_FLOAT](#\_0x9ADD2938) operation; see [FLUSH_RESOURCE_KVP](#\_0x5240DA5A).
            </summary>
            <param name="key">
            The key to set
            </param>
            <param name="_value">
            The value to write
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetResourceKvpInt(CitizenFX.Core.CString,System.Int32)">
            <summary>
            A setter for [GET_RESOURCE_KVP_INT](#\_0x557B586A).
            </summary>
            <param name="key">
            The key to set
            </param>
            <param name="_value">
            The value to write
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetResourceKvpIntNoSync(CitizenFX.Core.CString,System.Int32)">
            <summary>
            Nonsynchronous [SET_RESOURCE_KVP_INT](#\_0x6A2B1E8) operation; see [FLUSH_RESOURCE_KVP](#\_0x5240DA5A).
            </summary>
            <param name="key">
            The key to set
            </param>
            <param name="_value">
            The value to write
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetResourceKvpNoSync(CitizenFX.Core.CString,CitizenFX.Core.CString)">
            <summary>
            Nonsynchronous [SET_RESOURCE_KVP](#\_0x21C7A35B) operation; see [FLUSH_RESOURCE_KVP](#\_0x5240DA5A).
            </summary>
            <param name="key">
            The key to set
            </param>
            <param name="_value">
            The value to write
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetRoutingBucketEntityLockdownMode(System.Int32,CitizenFX.Core.CString)">
            <summary>
            Sets the entity lockdown mode for a specific routing bucket.
            
            Lockdown modes are:
            
            | Mode       | Meaning                                                    |
            | ---------- | ---------------------------------------------------------- |
            | `strict`   | No entities can be created by clients at all.              |
            | `relaxed`  | Only script-owned entities created by clients are blocked. |
            | `inactive` | Clients can create any entity they want.                   |
            </summary>
            <param name="bucketId">
            The routing bucket ID to adjust.
            </param>
            <param name="mode">
            One of aforementioned modes.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetRoutingBucketPopulationEnabled(System.Int32,System.Boolean)">
            <summary>
            Sets whether or not the specified routing bucket has automatically-created population enabled.
            </summary>
            <param name="bucketId">
            The routing bucket ID to adjust.
            </param>
            <param name="mode">
            `true` to enable population, `false` to disable population.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetStateBagValue(CitizenFX.Core.CString,CitizenFX.Core.CString,CitizenFX.Core.CString,System.Int32,System.Boolean)">
            <summary>
            Internal function for setting a state bag value.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetVehicleAlarm(System.Int32,System.Boolean)">
            <summary>
            SET_VEHICLE_ALARM
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetVehicleBodyHealth(System.Int32,System.Single)">
            <summary>
            p2 often set to 1000.0 in the decompiled scripts.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetVehicleColourCombination(System.Int32,System.Int32)">
            <summary>
            Sets the selected vehicle's colors to their default value (specific variant specified using the colorCombination parameter).
            
            Range of possible values for colorCombination is currently unknown, I couldn't find where these values are stored either (Disquse's guess was vehicles.meta but I haven't seen it in there.)
            </summary>
            <param name="vehicle">
            The vehicle to modify.
            </param>
            <param name="colorCombination">
            One of the default color values of the vehicle.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetVehicleColours(System.Int32,System.Int32,System.Int32)">
            <summary>
            colorPrimary &amp; colorSecondary are the paint indexes for the vehicle.
            
            For a list of valid paint indexes, view: pastebin.com/pwHci0xK
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetVehicleCustomPrimaryColour(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            p1, p2, p3 are RGB values for color (255,0,0 for Red, ect)
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetVehicleCustomSecondaryColour(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            p1, p2, p3 are RGB values for color (255,0,0 for Red, ect)
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetVehicleDirtLevel(System.Int32,System.Single)">
            <summary>
            Sets the dirt level of the passed vehicle.
            </summary>
            <param name="vehicle">
            The vehicle to set.
            </param>
            <param name="dirtLevel">
            A number between 0.0 and 15.0 representing the vehicles dirt level.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetVehicleDoorBroken(System.Int32,System.Int32,System.Boolean)">
            <summary>
            See eDoorId declared in [`SET_VEHICLE_DOOR_SHUT`](#\_0x93D9BD300D7789E5)
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetVehicleDoorsLocked(System.Int32,System.Int32)">
            <summary>
            Locks the doors of a specified vehicle to a defined lock state, affecting how players and NPCs can interact with the vehicle.
            
            ```
            NativeDB Introduced: v323
            ```
            
            ```cpp
            enum eVehicleLockState {
                // No specific lock state, vehicle behaves according to the game's default settings.
                VEHICLELOCK_NONE = 0,
                // Vehicle is fully unlocked, allowing free entry by players and NPCs.
                VEHICLELOCK_UNLOCKED = 1,
                // Vehicle is locked, preventing entry by players and NPCs.
                VEHICLELOCK_LOCKED = 2,
                // Vehicle locks out only players, allowing NPCs to enter.
                VEHICLELOCK_LOCKOUT_PLAYER_ONLY = 3,
                // Vehicle is locked once a player enters, preventing others from entering.
                VEHICLELOCK_LOCKED_PLAYER_INSIDE = 4,
                // Vehicle starts in a locked state, but may be unlocked through game events.
                VEHICLELOCK_LOCKED_INITIALLY = 5,
                // Forces the vehicle's doors to shut and lock.
                VEHICLELOCK_FORCE_SHUT_DOORS = 6,
                // Vehicle is locked but can still be damaged.
                VEHICLELOCK_LOCKED_BUT_CAN_BE_DAMAGED = 7,
                // Vehicle is locked, but its trunk/boot remains unlocked.
                VEHICLELOCK_LOCKED_BUT_BOOT_UNLOCKED = 8,
                // Vehicle is locked and does not allow passengers, except for the driver.
                VEHICLELOCK_LOCKED_NO_PASSENGERS = 9,
                // Vehicle is completely locked, preventing entry entirely, even if previously inside.
                VEHICLELOCK_CANNOT_ENTER = 10 
            };
            
            ```
            </summary>
            <param name="vehicle">
            The vehicle whose doors are to be locked.
            </param>
            <param name="doorLockStatus">
            The lock state to apply to the vehicle's doors, see `eVehicleLockState`.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetVehicleNumberPlateText(System.Int32,CitizenFX.Core.CString)">
            <summary>
            SET_VEHICLE_NUMBER_PLATE_TEXT
            </summary>
            <param name="vehicle">
            The vehicle to set the plate for
            </param>
            <param name="plateText">
            The text to set the plate to, 8 chars maximum
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.StartFindKvp(CitizenFX.Core.CString)">
            <summary>
            START_FIND_KVP
            </summary>
            <param name="prefix">
            A prefix match
            </param>
            <returns>
            A KVP find handle to use with [FIND_KVP](#\_0xBD7BEBC5) and close with [END_FIND_KVP](#\_0xB3210203)
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.StartResource(CitizenFX.Core.CString)">
            <summary>
            START_RESOURCE
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.StateBagHasKey(CitizenFX.Core.CString,CitizenFX.Core.CString)">
            <summary>
            STATE_BAG_HAS_KEY
            </summary>
            <param name="bagName">
            The name of the bag.
            </param>
            <param name="key">
            The key used to check data existence.
            </param>
            <returns>
            Returns true if the data associated with the specified key exists; otherwise, returns false.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.StopResource(CitizenFX.Core.CString)">
            <summary>
            STOP_RESOURCE
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TaskCombatPed(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Makes the specified ped attack the target ped.  
            p2 should be 0  
            p3 should be 16
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TaskDriveBy(System.Int32,System.Int32,System.Int32,System.Single,System.Single,System.Single,System.Single,System.Int32,System.Boolean,System.UInt32)">
            <summary>
            Example:
            TASK::TASK_DRIVE_BY(l_467[1/*22*/], PLAYER::PLAYER_PED_ID(), 0, 0.0, 0.0, 2.0, 300.0, 100, 0, ${firing_pattern_burst_fire_driveby});
            Needs working example. Doesn't seem to do anything.
            I marked p2 as targetVehicle as all these shooting related tasks seem to have that in common.
            I marked p6 as distanceToShoot as if you think of GTA's Logic with the native SET_VEHICLE_SHOOT natives, it won't shoot till it gets within a certain distance of the target.
            I marked p7 as pedAccuracy as it seems it's mostly 100 (Completely Accurate), 75, 90, etc. Although this could be the ammo count within the gun, but I highly doubt it. I will change this comment once I find out if it's ammo count or not.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TaskEnterVehicle(System.Int32,System.Int32,System.Int32,System.Int32,System.Single,System.Int32,System.Int64)">
            <summary>
            speed 1.0 = walk, 2.0 = run  
            p5 1 = normal, 3 = teleport to vehicle, 8 = normal/carjack ped from seat, 16 = teleport directly into vehicle  
            p6 is always 0
            </summary>
            <param name="seatIndex">
            See eSeatPosition declared in [`IS_VEHICLE_SEAT_FREE`](#\_0x22AC59A870E6A669).
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TaskEveryoneLeaveVehicle(System.Int32)">
            <summary>
            TASK_EVERYONE_LEAVE_VEHICLE
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TaskGoStraightToCoord(System.Int32,System.Single,System.Single,System.Single,System.Single,System.Int32,System.Single,System.Single)">
            <summary>
            TASK_GO_STRAIGHT_TO_COORD
            </summary>
            <param name="ped">
            The ped handle.
            </param>
            <param name="x">
            The x coordinate.
            </param>
            <param name="y">
            The y coordinate.
            </param>
            <param name="z">
            The z coordinate.
            </param>
            <param name="speed">
            The ped movement speed.
            </param>
            <param name="timeout">
            \-1 , other values appear to break the ped movement.
            </param>
            <param name="targetHeading">
            The heading you want the ped to be on x,y,z coord.
            </param>
            <param name="distanceToSlide">
            The distance from x,y,z where the ped will start sliding.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TaskGoToCoordAnyMeans(System.Int32,System.Single,System.Single,System.Single,System.Single,System.Int32,System.Boolean,System.Int32,System.Single)">
            <summary>
            Tells a ped to go to a coord by any means.
            
            ```cpp
            enum eDrivingMode {
              DF_StopForCars = 1,
              DF_StopForPeds = 2,
              DF_SwerveAroundAllCars = 4,
              DF_SteerAroundStationaryCars = 8,
              DF_SteerAroundPeds = 16,
              DF_SteerAroundObjects = 32,
              DF_DontSteerAroundPlayerPed = 64,
              DF_StopAtLights = 128,
              DF_GoOffRoadWhenAvoiding = 256,
              DF_DriveIntoOncomingTraffic = 512,
              DF_DriveInReverse = 1024,
            
              // If pathfinding fails, cruise randomly instead of going on a straight line
              DF_UseWanderFallbackInsteadOfStraightLine = 2048,
            
              DF_AvoidRestrictedAreas = 4096,
            
              // These only work on MISSION_CRUISE
              DF_PreventBackgroundPathfinding = 8192,
              DF_AdjustCruiseSpeedBasedOnRoadSpeed = 16384,
            
              DF_UseShortCutLinks =  262144,
              DF_ChangeLanesAroundObstructions = 524288,
              // cruise tasks ignore this anyway--only used for goto's
              DF_UseSwitchedOffNodes =  2097152,
              // if you're going to be primarily driving off road
              DF_PreferNavmeshRoute =  4194304,
            
              // Only works for planes using MISSION_GOTO, will cause them to drive along the ground instead of fly
              DF_PlaneTaxiMode =  8388608,
            
              DF_ForceStraightLine = 16777216,
              DF_UseStringPullingAtJunctions = 33554432,
            
              DF_AvoidHighways = *********,
              DF_ForceJoinInRoadDirection = 1073741824,
            
              // Standard driving mode. stops for cars, peds, and lights, goes around stationary obstructions
              DRIVINGMODE_STOPFORCARS = 786603, // DF_StopForCars|DF_StopForPeds|DF_SteerAroundObjects|DF_SteerAroundStationaryCars|DF_StopAtLights|DF_UseShortCutLinks|DF_ChangeLanesAroundObstructions,		// Obey lights too
            
              // Like the above, but doesn't steer around anything in its way - will only wait instead.
              DRIVINGMODE_STOPFORCARS_STRICT = 262275, // DF_StopForCars|DF_StopForPeds|DF_StopAtLights|DF_UseShortCutLinks, // Doesn't deviate an inch.
            
              // Default "alerted" driving mode. drives around everything, doesn't obey lights
              DRIVINGMODE_AVOIDCARS = 786469, // DF_SwerveAroundAllCars|DF_SteerAroundObjects|DF_UseShortCutLinks|DF_ChangeLanesAroundObstructions|DF_StopForCars,
            
              // Very erratic driving. difference between this and AvoidCars is that it doesn't use the brakes at ALL to help with steering
              DRIVINGMODE_AVOIDCARS_RECKLESS = 786468, // DF_SwerveAroundAllCars|DF_SteerAroundObjects|DF_UseShortCutLinks|DF_ChangeLanesAroundObstructions,
            
              // Smashes through everything
              DRIVINGMODE_PLOUGHTHROUGH = 262144, // DF_UseShortCutLinks
            
              // Drives normally except for the fact that it ignores lights
              DRIVINGMODE_STOPFORCARS_IGNORELIGHTS = 786475, // DF_StopForCars|DF_SteerAroundStationaryCars|DF_StopForPeds|DF_SteerAroundObjects|DF_UseShortCutLinks|DF_ChangeLanesAroundObstructions
            
              // Try to swerve around everything, but stop for lights if necessary
              DRIVINGMODE_AVOIDCARS_OBEYLIGHTS = 786597, // DF_SwerveAroundAllCars|DF_StopAtLights|DF_SteerAroundObjects|DF_UseShortCutLinks|DF_ChangeLanesAroundObstructions|DF_StopForCars
            
              // Swerve around cars, be careful around peds, and stop for lights
              DRIVINGMODE_AVOIDCARS_STOPFORPEDS_OBEYLIGHTS = 786599 // DF_SwerveAroundAllCars|DF_StopAtLights|DF_StopForPeds|DF_SteerAroundObjects|DF_UseShortCutLinks|DF_ChangeLanesAroundObstructions|DF_StopForCars
            };
            ```
            </summary>
            <param name="ped">
            The `Ped` Handle.
            </param>
            <param name="x">
            The goto target coordinate.
            </param>
            <param name="y">
            The goto target coordinate.
            </param>
            <param name="z">
            The goto target coordinate.
            </param>
            <param name="fMoveBlendRatio">
            0.0 = still, 1.0 = walk, 2.0 = run, 3.0 = sprint.
            </param>
            <param name="vehicle">
            If defined, the pedestrian will only move if said vehicle exists. If you don't want any sort of association, just set it to `0`.
            </param>
            <param name="bUseLongRangeVehiclePathing">
            Setting to `true` tells the vehicle to use longrange vehicle pathing.
            </param>
            <param name="drivingFlags">
            See `eDrivingMode` enum.
            </param>
            <param name="fMaxRangeToShootTargets">
            Determines the maximum distance at which the `Ped` will engage in combat with threatening targets.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TaskGoToEntity(System.Int32,System.Int32,System.Int32,System.Single,System.Single,System.Single,System.Int32)">
            <summary>
            The entity will move towards the target until time is over (duration) or get in target's range (distance). p5 and p6 are unknown, but you could leave p5 = 1073741824 or 100 or even 0 (didn't see any difference but on the decompiled scripts, they use 1073741824 mostly) and p6 = 0
            Note: I've only tested it on entity -&gt; ped and target -&gt; vehicle. It could work differently on other entities, didn't try it yet.
            Example: TASK::TASK_GO_TO_ENTITY(pedHandle, vehicleHandle, 5000, 4.0, 100, 1073741824, 0)
            Ped will run towards the vehicle for 5 seconds and stop when time is over or when he gets 4 meters(?) around the vehicle (with duration = -1, the task duration will be ignored).
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TaskHandsUp(System.Int32,System.Int32,System.Int32,System.Int32,System.Boolean)">
            <summary>
            In the scripts, p3 was always -1.  
            p3 seems to be duration or timeout of turn animation.  
            Also facingPed can be 0 or -1 so ped will just raise hands up.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TaskLeaveAnyVehicle(System.Int32,System.Int32,System.Int32)">
            <summary>
            Flags are the same flags used in [`TASK_LEAVE_VEHICLE`](#\_0xD3DBCE61A490BE02)
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TaskLeaveVehicle(System.Int32,System.Int32,System.Int32)">
            <summary>
            Flags from decompiled scripts:  
            0 = normal exit and closes door.  
            1 = normal exit and closes door.  
            16 = teleports outside, door kept closed.  (This flag does not seem to work for the front seats in buses, NPCs continue to exit normally)
            64 = normal exit and closes door, maybe a bit slower animation than 0.  
            256 = normal exit but does not close the door.  
            4160 = ped is throwing himself out, even when the vehicle is still.  
            262144 = ped moves to passenger seat first, then exits normally  
            Others to be tried out: 320, 512, 131072.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TaskPlayAnim(System.Int32,CitizenFX.Core.CString,CitizenFX.Core.CString,System.Single,System.Single,System.Int32,System.Int32,System.Single,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            [Animations list](https://alexguirre.github.io/animations-list/)
            
            ```cpp
            enum eScriptedAnimFlags
            {
                AF_LOOPING = 1,
                AF_HOLD_LAST_FRAME = 2,
                AF_REPOSITION_WHEN_FINISHED = 4,
                AF_NOT_INTERRUPTABLE = 8,
                AF_UPPERBODY = 16,
                AF_SECONDARY = 32,
                AF_REORIENT_WHEN_FINISHED = 64,
                AF_ABORT_ON_PED_MOVEMENT = 128,
                AF_ADDITIVE = 256,
                AF_TURN_OFF_COLLISION = 512,
                AF_OVERRIDE_PHYSICS = 1024,
                AF_IGNORE_GRAVITY = 2048,
                AF_EXTRACT_INITIAL_OFFSET = 4096,
                AF_EXIT_AFTER_INTERRUPTED = 8192,
                AF_TAG_SYNC_IN = 16384,
                AF_TAG_SYNC_OUT = 32768,
                AF_TAG_SYNC_CONTINUOUS = 65536,
                AF_FORCE_START = 131072,
                AF_USE_KINEMATIC_PHYSICS = 262144,
                AF_USE_MOVER_EXTRACTION = 524288,
                AF_HIDE_WEAPON = 1048576,
                AF_ENDS_IN_DEAD_POSE = 2097152,
                AF_ACTIVATE_RAGDOLL_ON_COLLISION = 4194304,
                AF_DONT_EXIT_ON_DEATH = 8388608,
                AF_ABORT_ON_WEAPON_DAMAGE = 16777216,
                AF_DISABLE_FORCED_PHYSICS_UPDATE = 33554432,
                AF_PROCESS_ATTACHMENTS_ON_START = 67108864,
                AF_EXPAND_PED_CAPSULE_FROM_SKELETON = 134217728,
                AF_USE_ALTERNATIVE_FP_ANIM = 268435456,
                AF_BLENDOUT_WRT_LAST_FRAME = *********,
                AF_USE_FULL_BLENDING = 1073741824
            }
            ```
            </summary>
            <param name="ped">
            The ped you want to play the animation
            </param>
            <param name="animDictionary">
            The animation dictionary
            </param>
            <param name="animationName">
            The animation name
            </param>
            <param name="blendInSpeed">
            The speed at which the animation blends in. Lower is slower and higher is faster, 1.0 is normal, 8.0 is basically instant
            </param>
            <param name="blendOutSpeed">
            The speed at which the animation blends out. Lower is slower and higher is faster, 1.0 is normal, 8.0 is basically instant
            </param>
            <param name="duration">
            The duration of the animation in milliseconds. -1 will play the animation until canceled
            </param>
            <param name="flag">
            The animation flags (see enum)
            </param>
            <param name="playbackRate">
            The playback rate (between 0.0 and 1.0)
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TaskPlayAnimAdvanced(System.Int32,CitizenFX.Core.CString,CitizenFX.Core.CString,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Int32,System.Int64,System.Single,System.Int64,System.Int64)">
            <summary>
            Similar in functionality to [`TASK_PLAY_ANIM`](#\_0xEA47FE3719165B94), except the position and rotation parameters let you specify the initial position and rotation of the task. The ped is teleported to the position specified.
            
            [Animations list](https://alexguirre.github.io/animations-list/)
            </summary>
            <param name="ped">
            The ped you want to play the animation
            </param>
            <param name="animDictionary">
            The animation dictionary
            </param>
            <param name="animationName">
            The animation name
            </param>
            <param name="posX">
            Initial X position of the task
            </param>
            <param name="posY">
            Initial Y position of the task
            </param>
            <param name="posZ">
            Initial Z position of the task
            </param>
            <param name="rotX">
            Initial X rotation of the task
            </param>
            <param name="rotY">
            Initial Y rotation of the task
            </param>
            <param name="rotZ">
            Initial Z rotation of the task
            </param>
            <param name="blendInSpeed">
            The speed at which the animation blends in. Lower is slower and higher is faster, 1.0 is normal, 8.0 is basically instant
            </param>
            <param name="blendOutSpeed">
            The speed at which the animation blends out. Lower is slower and higher is faster, 1.0 is normal, 8.0 is basically instant
            </param>
            <param name="duration">
            The duration of the animation in milliseconds. -1 will play the animation until canceled
            </param>
            <param name="flag">
            See [`TASK_PLAY_ANIM`](#\_0xEA47FE3719165B94)
            </param>
            <param name="animTime">
            Value between 0.0 and 1.0, lets you start an animation from the given point
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TaskReactAndFleePed(System.Int32,System.Int32)">
            <summary>
            TASK_REACT_AND_FLEE_PED
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TaskShootAtCoord(System.Int32,System.Single,System.Single,System.Single,System.Int32,System.UInt32)">
            <summary>
            Firing Pattern Hash Information: https://pastebin.com/Px036isB
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TaskShootAtEntity(System.Int32,System.Int32,System.Int32,System.UInt32)">
            <summary>
            //this part of the code is to determine at which entity the player is aiming, for example if you want to create a mod where you give orders to peds
            Entity aimedentity;
            Player player = PLAYER::PLAYER_ID();
            PLAYER::_GET_AIMED_ENTITY(player, &amp;aimedentity);
            //bg is an array of peds
            TASK::TASK_SHOOT_AT_ENTITY(bg[i], aimedentity, 5000, MISC::GET_HASH_KEY("FIRING_PATTERN_FULL_AUTO"));
            in practical usage, getting the entity the player is aiming at and then task the peds to shoot at the entity, at a button press event would be better.
            Firing Pattern Hash Information: https://pastebin.com/Px036isB
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TaskWarpPedIntoVehicle(System.Int32,System.Int32,System.Int32)">
            <summary>
            ```
            NativeDB Introduced: v323
            ```
            
            Warp a ped into a vehicle.
            
            **Note**: It's better to use [`TASK_ENTER_VEHICLE`](#\_0xC20E50AA46D09CA8) with the flag "warp" flag instead of this native.
            </summary>
            <param name="ped">
            The Ped to be warped into the vehicle.
            </param>
            <param name="vehicle">
            The target vehicle into which the ped will be warped.
            </param>
            <param name="seatIndex">
            See eSeatPosition declared in [`IS_VEHICLE_SEAT_FREE`](#\_0x22AC59A870E6A669).
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TempBanPlayer(CitizenFX.Core.CString,CitizenFX.Core.CString)">
            <summary>
            TEMP_BAN_PLAYER
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TriggerClientEventInternal(CitizenFX.Core.CString,CitizenFX.Core.CString,CitizenFX.Core.CString,System.Int32)">
            <summary>
            The backing function for TriggerClientEvent.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TriggerEventInternal(CitizenFX.Core.CString,CitizenFX.Core.CString,System.Int32)">
            <summary>
            The backing function for TriggerEvent.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TriggerLatentClientEventInternal(CitizenFX.Core.CString,CitizenFX.Core.CString,CitizenFX.Core.CString,System.Int32,System.Int32)">
            <summary>
            The backing function for TriggerLatentClientEvent.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.VerifyPasswordHash(CitizenFX.Core.CString,CitizenFX.Core.CString)">
            <summary>
            VERIFY_PASSWORD_HASH
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.WasEventCanceled">
            <summary>
            Returns whether or not the currently executing event was canceled.
            </summary>
            <returns>
            A boolean.
            </returns>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash._ADD_BLIP_FOR_AREA">
            <summary>
            Adds a rectangular blip for the specified coordinates/area.
            
            It is recommended to use [SET_BLIP_ROTATION](#\_0xF87683CDF73C3F6E) and [SET_BLIP_COLOUR](#\_0x03D7FB09E75D6B7E) to make the blip not rotate along with the camera.
            
            By default, the blip will show as a *regular* blip with the specified color/sprite if it is outside of the minimap view.
            
            Example image:
            ![minimap](https://i.imgur.com/qLbXWcQ.png)
            ![big map](https://i.imgur.com/0j7O7Rh.png)
            
            (Native name is *likely* to actually be ADD_BLIP_FOR_AREA, but due to the usual reasons this can't be confirmed)
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.ADD_BLIP_FOR_COORD">
            <summary>
            Creates a blip for the specified coordinates. You can use `SET_BLIP_` natives to change the blip.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.ADD_BLIP_FOR_ENTITY">
            <summary>
            Create a blip that by default is red (enemy), you can use [SET_BLIP_AS_FRIENDLY](#\_0xC6F43D0E) to make it blue (friend).\
            Can be used for objects, vehicles and peds.
            
            Example of enemy:
            ![enemy](https://i.imgur.com/fl78svv.png)
            Example of friend:
            ![friend](https://i.imgur.com/Q16ho5d.png)
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.ADD_BLIP_FOR_RADIUS">
            <summary>
            Create a blip with a radius for the specified coordinates (it doesnt create the blip sprite, so you need to use [AddBlipCoords](#\_0xC6F43D0E))
            
            Example image:
            ![example](https://i.imgur.com/9hQl3DB.png)
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.ADD_CONVAR_CHANGE_LISTENER">
            <summary>
            Adds a listener for Console Variable changes.
            
            The function called expects to match the following signature:
            
            ```ts
            function ConVarChangeListener(conVarName: string, reserved: any);
            ```
            
            *   **conVarName**: The ConVar that changed.
            *   **reserved**: Currently unused.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.ADD_PED_DECORATION_FROM_HASHES">
            <summary>
            Applies an Item from a PedDecorationCollection to a ped. These include tattoos and shirt decals.
            collection - PedDecorationCollection filename hash
            overlay - Item name hash
            Example:
            Entry inside "mpbeach_overlays.xml" -
            &lt;Item&gt;
              &lt;uvPos x="0.500000" y="0.500000" /&gt;
              &lt;scale x="0.600000" y="0.500000" /&gt;
              &lt;rotation value="0.000000" /&gt;
              &lt;nameHash&gt;FM_Hair_Fuzz&lt;/nameHash&gt;
              &lt;txdHash&gt;mp_hair_fuzz&lt;/txdHash&gt;
              &lt;txtHash&gt;mp_hair_fuzz&lt;/txtHash&gt;
              &lt;zone&gt;ZONE_HEAD&lt;/zone&gt;
              &lt;type&gt;TYPE_TATTOO&lt;/type&gt;
              &lt;faction&gt;FM&lt;/faction&gt;
              &lt;garment&gt;All&lt;/garment&gt;
              &lt;gender&gt;GENDER_DONTCARE&lt;/gender&gt;
              &lt;award /&gt;
              &lt;awardLevel /&gt;
            &lt;/Item&gt;
            Code:
            PED::_0x5F5D1665E352A839(PLAYER::PLAYER_PED_ID(), MISC::GET_HASH_KEY("mpbeach_overlays"), MISC::GET_HASH_KEY("fm_hair_fuzz"))
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.ADD_STATE_BAG_CHANGE_HANDLER">
            <summary>
            Adds a handler for changes to a state bag.
            
            The function called expects to match the following signature:
            
            ```ts
            function StateBagChangeHandler(bagName: string, key: string, value: any, reserved: number, replicated: boolean);
            ```
            
            *   **bagName**: The internal bag ID for the state bag which changed. This is usually `player:Source`, `entity:NetID`
                or `localEntity:Handle`.
            *   **key**: The changed key.
            *   **value**: The new value stored at key. The old value is still stored in the state bag at the time this callback executes.
            *   **reserved**: Currently unused.
            *   **replicated**: Whether the set is meant to be replicated.
            
            At this time, the change handler can't opt to reject changes.
            
            If bagName refers to an entity, use [GET_ENTITY_FROM_STATE_BAG_NAME](#\_0x4BDF1867) to get the entity handle
            If bagName refers to a player, use [GET_PLAYER_FROM_STATE_BAG_NAME](#\_0xA56135E0) to get the player handle
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.APPLY_FORCE_TO_ENTITY">
            <summary>
            cpp
            enum eApplyForceTypes {
                APPLY_TYPE_FORCE = 0,
                APPLY_TYPE_IMPULSE = 1,
                APPLY_TYPE_EXTERNAL_FORCE = 2,
                APPLY_TYPE_EXTERNAL_IMPULSE = 3,
                APPLY_TYPE_TORQUE = 4,
                APPLY_TYPE_ANGULAR_IMPULSE = 5
            }
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.CAN_PLAYER_START_COMMERCE_SESSION">
            <summary>
            Returns whether or not the specified player has enough information to start a commerce session for.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.CANCEL_EVENT">
            <summary>
            Cancels the currently executing event.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.CLEAR_PED_PROP">
            <summary>
            CLEAR_PED_PROP
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.CLEAR_PED_SECONDARY_TASK">
            <summary>
            CLEAR_PED_SECONDARY_TASK
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.CLEAR_PED_TASKS">
            <summary>
            Clear a ped's tasks. Stop animations and other tasks created by scripts.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.CLEAR_PED_TASKS_IMMEDIATELY">
            <summary>
            Immediately stops the pedestrian from whatever it's doing. The difference between this and [CLEAR_PED_TASKS](#\_0xE1EF3C1216AFF2CD) is that this one teleports the ped but does not change the position of the ped.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.CLEAR_PLAYER_WANTED_LEVEL">
            <summary>
            This executes at the same as speed as PLAYER::SET_PLAYER_WANTED_LEVEL(player, 0, false);  
            PLAYER::GET_PLAYER_WANTED_LEVEL(player); executes in less than half the time. Which means that it's worth first checking if the wanted level needs to be cleared before clearing. However, this is mostly about good code practice and can important in other situations. The difference in time in this example is negligible.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.CREATE_OBJECT">
            <summary>
            Creates an object (prop) with the specified model at the specified position, offset on the Z axis by the radius of the object's model.
            This object will initially be owned by the creating script as a mission entity, and the model should be loaded already (e.g. using REQUEST_MODEL).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.CREATE_OBJECT_NO_OFFSET">
            <summary>
            Creates an object (prop) with the specified model centered at the specified position.
            This object will initially be owned by the creating script as a mission entity, and the model should be loaded already (e.g. using REQUEST_MODEL).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.CREATE_PED">
            <summary>
            Creates a ped (biped character, pedestrian, actor) with the specified model at the specified position and heading.
            This ped will initially be owned by the creating script as a mission entity, and the model should be loaded already
            (e.g. using REQUEST_MODEL).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.CREATE_PED_INSIDE_VEHICLE">
            <summary>
            CREATE_PED_INSIDE_VEHICLE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.CREATE_VEHICLE">
            <summary>
            Creates a vehicle with the specified model at the specified position. This vehicle will initially be owned by the creating
            script as a mission entity, and the model should be loaded already (e.g. using REQUEST_MODEL).
            
            ```
            NativeDB Added Parameter 8: BOOL p7
            ```
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.CREATE_VEHICLE_SERVER_SETTER">
            <summary>
            Equivalent to CREATE_VEHICLE, but it uses 'server setter' logic (like the former CREATE_AUTOMOBILE) as a workaround for
            reliability concerns regarding entity creation RPC.
            
            Unlike CREATE_AUTOMOBILE, this supports other vehicle types as well.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.DELETE_ENTITY">
            <summary>
            Deletes the specified entity.
            
            **NOTE**: For trains this will only work if called on the train engine, it will not work on its carriages.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.DELETE_FUNCTION_REFERENCE">
            <summary>
            DELETE_FUNCTION_REFERENCE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.DELETE_RESOURCE_KVP">
            <summary>
            DELETE_RESOURCE_KVP
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.DELETE_RESOURCE_KVP_NO_SYNC">
            <summary>
            Nonsynchronous [DELETE_RESOURCE_KVP](#\_0x7389B5DF) operation; see [FLUSH_RESOURCE_KVP](#\_0x5240DA5A).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.DELETE_TRAIN">
            <summary>
            Deletes the specified `entity` and any carriage its attached to, or that is attached to it.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.DOES_BOAT_SINK_WHEN_WRECKED">
            <summary>
            DOES_BOAT_SINK_WHEN_WRECKED
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.DOES_ENTITY_EXIST">
            <summary>
            DOES_ENTITY_EXIST
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.DOES_PLAYER_EXIST">
            <summary>
            Returns whether or not the player exists
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.DOES_PLAYER_OWN_SKU">
            <summary>
            Requests whether or not the player owns the specified SKU.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.DOES_PLAYER_OWN_SKU_EXT">
            <summary>
            Requests whether or not the player owns the specified package.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.DOES_TRAIN_STOP_AT_STATIONS">
            <summary>
            DOES_TRAIN_STOP_AT_STATIONS
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.DROP_PLAYER">
            <summary>
            DROP_PLAYER
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.DUPLICATE_FUNCTION_REFERENCE">
            <summary>
            DUPLICATE_FUNCTION_REFERENCE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.ENABLE_ENHANCED_HOST_SUPPORT">
            <summary>
            ENABLE_ENHANCED_HOST_SUPPORT
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.END_FIND_KVP">
            <summary>
            END_FIND_KVP
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.ENSURE_ENTITY_STATE_BAG">
            <summary>
            Internal function for ensuring an entity has a state bag.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.EXECUTE_COMMAND">
            <summary>
            Depending on your use case you may need to use `add_acl resource.&lt;your_resource_name&gt; command.&lt;command_name&gt; allow` to use this native in your resource.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.FIND_KVP">
            <summary>
            FIND_KVP
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.FLAG_SERVER_AS_PRIVATE">
            <summary>
            FLAG_SERVER_AS_PRIVATE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.FLUSH_RESOURCE_KVP">
            <summary>
            Nonsynchronous operations will not wait for a disk/filesystem flush before returning from a write or delete call. They will be much faster than their synchronous counterparts (e.g., bulk operations), however, a system crash may lose the data to some recent operations.
            
            This native ensures all `_NO_SYNC` operations are synchronized with the disk/filesystem.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.FORMAT_STACK_TRACE">
            <summary>
            An internal function for converting a stack trace object to a string.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.FREEZE_ENTITY_POSITION">
            <summary>
            Freezes or unfreezes an entity preventing its coordinates to change by the player if set to `true`. You can still change the entity position using [`SET_ENTITY_COORDS`](#\_0x06843DA7060A026B).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_AIR_DRAG_MULTIPLIER_FOR_PLAYERS_VEHICLE">
            <summary>
            GET_AIR_DRAG_MULTIPLIER_FOR_PLAYERS_VEHICLE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ALL_OBJECTS">
            <summary>
            Returns all object handles known to the server.
            The data returned adheres to the following layout:
            
            ```
            [127, 42, 13, 37]
            ```
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ALL_PEDS">
            <summary>
            Returns all peds handles known to the server.
            The data returned adheres to the following layout:
            
            ```
            [127, 42, 13, 37]
            ```
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ALL_VEHICLES">
            <summary>
            Returns all vehicle handles known to the server.
            The data returned adheres to the following layout:
            
            ```
            [127, 42, 13, 37]
            ```
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_CONSOLE_BUFFER">
            <summary>
            Returns the current console output buffer.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_CONVAR">
            <summary>
            Can be used to get a console variable of type `char*`, for example a string.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_CONVAR_BOOL">
            <summary>
            Can be used to get a console variable casted back to `bool`.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_CONVAR_FLOAT">
            <summary>
            This will have floating point inaccuracy.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_CONVAR_INT">
            <summary>
            Can be used to get a console variable casted back to `int` (an integer value).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_CURRENT_PED_WEAPON">
            <summary>
            Returns the hash of weapon the Ped is currently using.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_CURRENT_RESOURCE_NAME">
            <summary>
            Returns the name of the currently executing resource.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ENTITIES_IN_RADIUS">
            <summary>
            ### Supported types
            
            *   \[1] : Peds (including animals) and players.
            *   \[2] : Vehicles.
            *   \[3] : Objects (props), doors, and projectiles.
            
            ### Coordinates need to be send unpacked (x,y,z)
            
            ```lua
            
            -- Define the allowed model hashes
            local allowedModelHashes = { GetHashKey("p_crate03x"), GetHashKey("p_crate22x") }
            
            -- Get the player's current coordinates
            local playerCoords = GetEntityCoords(PlayerPedId())
            
            -- Retrieve all entities of type Object (type 3) within a radius of 10.0 units
            -- that match the allowed model hashes
            -- and sort output entities by distance
            local entities = GetEntitiesInRadius(playerCoords.x, playerCoords.y, playerCoords.z, 10.0, 3, true, allowedModelHashes)
            
            -- Iterate through the list of entities and print their ids
            for i = 1, #entities do
                local entity = entities[i]
                print(entity)
            end
            
            ```
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ENTITY_ATTACHED_TO">
            <summary>
            Gets the entity that this entity is attached to.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ENTITY_COLLISION_DISABLED">
            <summary>
            GET_ENTITY_COLLISION_DISABLED
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ENTITY_COORDS">
            <summary>
            Gets the current coordinates for a specified entity. This native is used server side when using OneSync.
            
            See [GET_ENTITY_COORDS](#\_0x3FEF770D40960D5A) for client side.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ENTITY_FROM_STATE_BAG_NAME">
            <summary>
            Returns the entity handle for the specified state bag name. For use with [ADD_STATE_BAG_CHANGE_HANDLER](#\_0x5BA35AAF).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ENTITY_HEADING">
            <summary>
            GET_ENTITY_HEADING
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ENTITY_HEALTH">
            <summary>
            Only works for vehicle and peds
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ENTITY_MAX_HEALTH">
            <summary>
            Currently it only works with peds.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ENTITY_MODEL">
            <summary>
            GET_ENTITY_MODEL
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ENTITY_ORPHAN_MODE">
            <summary>
            GET_ENTITY_ORPHAN_MODE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ENTITY_POPULATION_TYPE">
            <summary>
            This native gets an entity's population type.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ENTITY_REMOTE_SYNCED_SCENES_ALLOWED">
            <summary>
            GET_ENTITY_REMOTE_SYNCED_SCENES_ALLOWED
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ENTITY_ROTATION">
            <summary>
            GET_ENTITY_ROTATION
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ENTITY_ROTATION_VELOCITY">
            <summary>
            GET_ENTITY_ROTATION_VELOCITY
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ENTITY_ROUTING_BUCKET">
            <summary>
            Gets the routing bucket for the specified entity.
            
            Routing buckets are also known as 'dimensions' or 'virtual worlds' in past echoes, however they are population-aware.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ENTITY_SCRIPT">
            <summary>
            GET_ENTITY_SCRIPT
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ENTITY_SPEED">
            <summary>
            Gets the current speed of the entity in meters per second.
            
            ```
            To convert to MPH: speed * 2.236936
            To convert to KPH: speed * 3.6
            ```
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ENTITY_TYPE">
            <summary>
            Gets the entity type (as an integer), which can be one of the following defined down below:
            
            **The following entities will return type `1`:**
            
            *   Ped
            *   Player
            *   Animal (Red Dead Redemption 2)
            *   Horse (Red Dead Redemption 2)
            
            **The following entities will return type `2`:**
            
            *   Automobile
            *   Bike
            *   Boat
            *   Heli
            *   Plane
            *   Submarine
            *   Trailer
            *   Train
            *   DraftVeh (Red Dead Redemption 2)
            
            **The following entities will return type `3`:**
            
            *   Object
            *   Door
            *   Pickup
            
            Otherwise, a value of `0` will be returned.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ENTITY_VELOCITY">
            <summary>
            GET_ENTITY_VELOCITY
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_GAME_BUILD_NUMBER">
            <summary>
            Returns the internal build number of the current game being executed.
            
            Possible values:
            
            *   FiveM
                *   1604
                *   2060
                *   2189
                *   2372
                *   2545
                *   2612
                *   2699
                *   2802
                *   2944
                *   3095
                *   3258
                *   3323
                *   3407
                *   3570
            *   RedM
                *   1311
                *   1355
                *   1436
                *   1491
            *   LibertyM
                *   43
            *   FXServer
                *   0
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_GAME_NAME">
            <summary>
            Returns the current game being executed.
            
            Possible values:
            
            | Return value | Meaning                        |
            | ------------ | ------------------------------ |
            | `fxserver`   | Server-side code ('Duplicity') |
            | `fivem`      | FiveM for GTA V                |
            | `libertym`   | LibertyM for GTA IV            |
            | `redm`       | RedM for Red Dead Redemption 2 |
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_GAME_POOL">
            <summary>
            Returns a list of entity handles (script GUID) for all entities in the specified pool - the data returned is an array as
            follows:
            
            ```json
            [ 770, 1026, 1282, 1538, 1794, 2050, 2306, 2562, 2818, 3074, 3330, 3586, 3842, 4098, 4354, 4610, ...]
            ```
            
            ### Supported pools
            
            *   `CPed`: Peds (including animals) and players.
            *   `CObject`: Objects (props), doors, and projectiles.
            *   `CNetObject`: Networked objects
            *   `CVehicle`: Vehicles.
            *   `CPickup`: Pickups.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_GAME_TIMER">
            <summary>
            Gets the current game timer in milliseconds.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_HASH_KEY">
            <summary>
            This native converts the passed string to a hash.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_HELI_BODY_HEALTH">
            <summary>
            **Note** This native will always return `1000.0` unless [SET_VEHICLE_BODY_HEALTH](#\_0xB77D05AC8C78AADB), [SET_VEHICLE_ENGINE_HEALTH](#\_0x45F6D8EEF34ABEF1), or [SET_VEHICLE_PETROL_TANK_HEALTH](#\_0x70DB57649FA8D0D8) have been called with a value greater than `1000.0`.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_HELI_DISABLE_EXPLODE_FROM_BODY_DAMAGE">
            <summary>
            This is a getter for [SET_DISABLE_HELI_EXPLODE_FROM_BODY_DAMAGE](#\_0xEDBC8405B3895CC9)
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_HELI_ENGINE_HEALTH">
            <summary>
            **Note** This native will always return `1000.0` unless [SET_VEHICLE_BODY_HEALTH](#\_0xB77D05AC8C78AADB), [SET_VEHICLE_ENGINE_HEALTH](#\_0x45F6D8EEF34ABEF1), or [SET_VEHICLE_PETROL_TANK_HEALTH](#\_0x70DB57649FA8D0D8) have been called with a value greater than `1000.0`.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_HELI_GAS_TANK_HEALTH">
            <summary>
            **Note** This native will always return `1000.0` unless [SET_VEHICLE_BODY_HEALTH](#\_0xB77D05AC8C78AADB), [SET_VEHICLE_ENGINE_HEALTH](#\_0x45F6D8EEF34ABEF1), or [SET_VEHICLE_PETROL_TANK_HEALTH](#\_0x70DB57649FA8D0D8) have been called with a value greater than `1000.0`.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_HELI_MAIN_ROTOR_DAMAGE_SCALE">
            <summary>
            GET_HELI_MAIN_ROTOR_DAMAGE_SCALE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_HELI_MAIN_ROTOR_HEALTH">
            <summary>
            GET_HELI_MAIN_ROTOR_HEALTH
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_HELI_PITCH_CONTROL">
            <summary>
            GET_HELI_PITCH_CONTROL
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_HELI_REAR_ROTOR_DAMAGE_SCALE">
            <summary>
            GET_HELI_REAR_ROTOR_DAMAGE_SCALE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_HELI_REAR_ROTOR_HEALTH">
            <summary>
            This native is a getter for [SET_HELI_TAIL_ROTOR_HEALTH](#\_0xFE205F38AAA58E5B)
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_HELI_ROLL_CONTROL">
            <summary>
            GET_HELI_ROLL_CONTROL
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_HELI_TAIL_ROTOR_DAMAGE_SCALE">
            <summary>
            GET_HELI_TAIL_ROTOR_DAMAGE_SCALE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_HELI_TAIL_ROTOR_HEALTH">
            <summary>
            **Note**: This native is deprecated, please use [`GET_HELI_REAR_ROTOR_HEALTH`](#\_0x33EE6E2B) instead.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_HELI_THROTTLE_CONTROL">
            <summary>
            GET_HELI_THROTTLE_CONTROL
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_HELI_YAW_CONTROL">
            <summary>
            GET_HELI_YAW_CONTROL
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_HOST_ID">
            <summary>
            GET_HOST_ID
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_INSTANCE_ID">
            <summary>
            GET_INSTANCE_ID
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_INVOKING_RESOURCE">
            <summary>
            GET_INVOKING_RESOURCE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_IS_HELI_ENGINE_RUNNING">
            <summary>
            GET_IS_HELI_ENGINE_RUNNING
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_IS_VEHICLE_ENGINE_RUNNING">
            <summary>
            GET_IS_VEHICLE_ENGINE_RUNNING
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_IS_VEHICLE_PRIMARY_COLOUR_CUSTOM">
            <summary>
            GET_IS_VEHICLE_PRIMARY_COLOUR_CUSTOM
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_IS_VEHICLE_SECONDARY_COLOUR_CUSTOM">
            <summary>
            GET_IS_VEHICLE_SECONDARY_COLOUR_CUSTOM
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_LANDING_GEAR_STATE">
            <summary>
            See the client-side [GET_LANDING_GEAR_STATE](#\_0x9B0F3DCA3DB0F4CD) native for a description of landing gear states.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_LAST_PED_IN_VEHICLE_SEAT">
            <summary>
            GET_LAST_PED_IN_VEHICLE_SEAT
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_NET_TYPE_FROM_ENTITY">
            <summary>
            Gets the specific entity type (as an integer), which can be one of the following defined down below:
            
            #### FiveM:
            
            ```cpp
            enum eNetObjEntityType
            {
                Automobile = 0,
                Bike = 1,
                Boat = 2,
                Door = 3,
                Heli = 4,
                Object = 5,
                Ped = 6,
                Pickup = 7,
                PickupPlacement = 8,
                Plane = 9,
                Submarine = 10,
                Player = 11,
                Trailer = 12,
                Train = 13
            };
            ```
            
            #### RedM:
            
            ```cpp
            enum eNetObjEntityType
            {
                Animal = 0,
                Automobile = 1,
                Bike = 2,
                Boat = 3,
                Door = 4,
                Heli = 5,
                Object = 6,
                Ped = 7,
                Pickup = 8,
                PickupPlacement = 9,
                Plane = 10,
                Submarine = 11,
                Player = 12,
                Trailer = 13,
                Train = 14,
                DraftVeh = 15,
                StatsTracker = 16,
                PropSet = 17,
                AnimScene = 18,
                GroupScenario = 19,
                Herd = 20,
                Horse = 21,
                WorldState = 22,
                WorldProjectile = 23,
                Incident = 24,
                Guardzone = 25,
                PedGroup = 26,
                CombatDirector = 27,
                PedSharedTargeting = 28,
                Persistent = 29
            };
            ```
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_NUM_PLAYER_IDENTIFIERS">
            <summary>
            GET_NUM_PLAYER_IDENTIFIERS
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_NUM_PLAYER_INDICES">
            <summary>
            GET_NUM_PLAYER_INDICES
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_NUM_PLAYER_TOKENS">
            <summary>
            GET_NUM_PLAYER_TOKENS
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_NUM_RESOURCE_METADATA">
            <summary>
            Gets the amount of metadata values with the specified key existing in the specified resource's manifest.
            See also: [Resource manifest](https://docs.fivem.net/docs/scripting-reference/resource-manifest/resource-manifest/)
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_NUM_RESOURCES">
            <summary>
            GET_NUM_RESOURCES
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PASSWORD_HASH">
            <summary>
            GET_PASSWORD_HASH
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PED_ARMOUR">
            <summary>
            GET_PED_ARMOUR
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PED_CAUSE_OF_DEATH">
            <summary>
            GET_PED_CAUSE_OF_DEATH
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PED_DESIRED_HEADING">
            <summary>
            GET_PED_DESIRED_HEADING
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PED_IN_VEHICLE_SEAT">
            <summary>
            GET_PED_IN_VEHICLE_SEAT
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PED_MAX_HEALTH">
            <summary>
            GET_PED_MAX_HEALTH
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PED_RELATIONSHIP_GROUP_HASH">
            <summary>
            Gets the current relationship group hash of a ped.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PED_SCRIPT_TASK_COMMAND">
            <summary>
            Gets the script task command currently assigned to the ped.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PED_SCRIPT_TASK_STAGE">
            <summary>
            Gets the stage of the peds scripted task.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PED_SOURCE_OF_DAMAGE">
            <summary>
            Get the last entity that damaged the ped. This native is used server side when using OneSync.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PED_SOURCE_OF_DEATH">
            <summary>
            Get the entity that killed the ped. This native is used server side when using OneSync.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PED_SPECIFIC_TASK_TYPE">
            <summary>
            Gets the type of a ped's specific task given an index of the CPedTaskSpecificDataNode nodes.
            A ped will typically have a task at index 0, if a ped has multiple tasks at once they will be in the order 0, 1, 2, etc.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PED_STEALTH_MOVEMENT">
            <summary>
            GET_PED_STEALTH_MOVEMENT
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_CAMERA_ROTATION">
            <summary>
            Gets the current camera rotation for a specified player. This native is used server side when using OneSync.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_ENDPOINT">
            <summary>
            GET_PLAYER_ENDPOINT
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_FAKE_WANTED_LEVEL">
            <summary>
            Gets the current fake wanted level for a specified player. This native is used server side when using OneSync.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_FOCUS_POS">
            <summary>
            Gets the focus position (i.e. the position of the active camera in the game world) of a player.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_FROM_INDEX">
            <summary>
            GET_PLAYER_FROM_INDEX
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_FROM_STATE_BAG_NAME">
            <summary>
            On the server this will return the players source, on the client it will return the player handle.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_GUID">
            <summary>
            GET_PLAYER_GUID
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_IDENTIFIER">
            <summary>
            To get the number of identifiers, use [GET_NUM_PLAYER_IDENTIFIERS](#\_0xFF7F66AB)
            
            To get a specific type of identifier, use [GET_PLAYER_IDENTIFIER_BY_TYPE](#\_0xA61C8FC6)
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_IDENTIFIER_BY_TYPE">
            <summary>
            Get an identifier from a player by the type of the identifier.
            Known [Identifiers](https://docs.fivem.net/docs/scripting-reference/runtimes/lua/functions/GetPlayerIdentifiers/#identifier-types)
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_INVINCIBLE">
            <summary>
            GET_PLAYER_INVINCIBLE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_LAST_MSG">
            <summary>
            GET_PLAYER_LAST_MSG
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_MAX_ARMOUR">
            <summary>
            GET_PLAYER_MAX_ARMOUR
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_MAX_HEALTH">
            <summary>
            GET_PLAYER_MAX_HEALTH
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_MELEE_WEAPON_DAMAGE_MODIFIER">
            <summary>
            A getter for [SET_PLAYER_MELEE_WEAPON_DAMAGE_MODIFIER](#\_0x4A3DC7ECCC321032).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_NAME">
            <summary>
            GET_PLAYER_NAME
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_PED">
            <summary>
            Used to get the player's Ped Entity ID when a valid `playerSrc` is passed.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_PEER_STATISTICS">
            <summary>
            ```cpp
            const int ENET_PACKET_LOSS_SCALE = 65536;
            
            enum PeerStatistics
            {
            	// PacketLoss will only update once every 10 seconds, use PacketLossEpoch if you want the time
            	// since the last time the packet loss was updated.
            
            	// the amount of packet loss the player has, needs to be scaled with PACKET_LOSS_SCALE
            	PacketLoss = 0,
            	// The variance in the packet loss
            	PacketLossVariance = 1,
            	// The time since the last packet update in ms, relative to the peers connection time
            	PacketLossEpoch = 2,
            	// The mean amount of time it takes for a packet to get to the client (ping)
            	RoundTripTime = 3,
            	// The variance in the round trip time
            	RoundTripTimeVariance = 4,
            	// Despite their name, these are only updated once every 5 seconds, you can get the last time this was updated with PacketThrottleEpoch
            	// The last recorded round trip time of a packet
            	LastRoundTripTime = 5,
            	// The last round trip time variance
            	LastRoundTripTimeVariance = 6,
            	// The time since the last packet throttle update, relative to the peers connection time
            	PacketThrottleEpoch = 7,
            };
            ```
            
            These statistics only update once every 10 seconds.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_PING">
            <summary>
            See [GET_PLAYER_PEER_STATISTICS](#\_0x9A928294) if you want more detailed information, like packet loss, and packet/rtt variance
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_ROUTING_BUCKET">
            <summary>
            Gets the routing bucket for the specified player.
            
            Routing buckets are also known as 'dimensions' or 'virtual worlds' in past echoes, however they are population-aware.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_TEAM">
            <summary>
            GET_PLAYER_TEAM
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_TIME_IN_PURSUIT">
            <summary>
            Gets the amount of time player has spent evading the cops.
            Counter starts and increments only when cops are chasing the player.
            If the player is evading, the timer will pause.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_TIME_ONLINE">
            <summary>
            Gets the current time online for a specified player.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_TOKEN">
            <summary>
            Gets a player's token. Tokens can be used to enhance banning logic, however are specific to a server.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_WANTED_CENTRE_POSITION">
            <summary>
            Gets the current known coordinates for the specified player from cops perspective. This native is used server side when using OneSync.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_WANTED_LEVEL">
            <summary>
            Returns given players wanted level server-side.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_WEAPON_DAMAGE_MODIFIER">
            <summary>
            A getter for [SET_PLAYER_WEAPON_DAMAGE_MODIFIER](#\_0xCE07B9F7817AADA3).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_WEAPON_DEFENSE_MODIFIER">
            <summary>
            A getter for [SET_PLAYER_WEAPON_DEFENSE_MODIFIER](#\_0x2D83BC011CA14A3C).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_WEAPON_DEFENSE_MODIFIER_2">
            <summary>
            A getter for [\_SET_PLAYER_WEAPON_DEFENSE_MODIFIER\_2](#\_0xBCFDE9EDE4CF27DC).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_REGISTERED_COMMANDS">
            <summary>
            Returns all commands that are registered in the command system.
            The data returned adheres to the following layout:
            
            ```
            [
            {
            "name": "cmdlist",
            "resource": "resource",
            "arity" = -1,
            },
            {
            "name": "command1"
            "resource": "resource_2",
            "arity" = -1,
            }
            ]
            ```
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_RESOURCE_BY_FIND_INDEX">
            <summary>
            GET_RESOURCE_BY_FIND_INDEX
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_RESOURCE_COMMANDS">
            <summary>
            Returns all commands registered by the specified resource.
            The data returned adheres to the following layout:
            
            ```
            [
            {
            "name": "cmdlist",
            "resource": "example_resource",
            "arity" = -1,
            },
            {
            "name": "command1"
            "resource": "example_resource2",
            "arity" = -1,
            }
            ]
            ```
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_RESOURCE_KVP_FLOAT">
            <summary>
            A getter for [SET_RESOURCE_KVP_FLOAT](#\_0x9ADD2938).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_RESOURCE_KVP_INT">
            <summary>
            A getter for [SET_RESOURCE_KVP_INT](#\_0x6A2B1E8).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_RESOURCE_KVP_STRING">
            <summary>
            A getter for [SET_RESOURCE_KVP](#\_0x21C7A35B).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_RESOURCE_METADATA">
            <summary>
            Gets the metadata value at a specified key/index from a resource's manifest.
            See also: [Resource manifest](https://docs.fivem.net/docs/scripting-reference/resource-manifest/resource-manifest/)
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_RESOURCE_PATH">
            <summary>
            Returns the physical on-disk path of the specified resource.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_RESOURCE_STATE">
            <summary>
            Returns the current state of the specified resource.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_SELECTED_PED_WEAPON">
            <summary>
            An alias of [GET_CURRENT_PED_WEAPON](#\_0xB0237302).
            
            Note, the client-side [GET_SELECTED_PED_WEAPON](#\_0x0A6DB4965674D243) native returns the weapon selected via the HUD (weapon wheel). This data is not available to FXServer.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_STATE_BAG_KEYS">
            <summary>
            GET_STATE_BAG_KEYS
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_STATE_BAG_VALUE">
            <summary>
            Returns the value of a state bag key.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_THRUSTER_SIDE_RCS_THROTTLE">
            <summary>
            GET_THRUSTER_SIDE_RCS_THROTTLE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_THRUSTER_THROTTLE">
            <summary>
            GET_THRUSTER_THROTTLE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_TRAIN_BACKWARD_CARRIAGE">
            <summary>
            GET_TRAIN_BACKWARD_CARRIAGE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_TRAIN_CARRIAGE_ENGINE">
            <summary>
            GET_TRAIN_CARRIAGE_ENGINE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_TRAIN_CARRIAGE_INDEX">
            <summary>
            GET_TRAIN_CARRIAGE_INDEX
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_TRAIN_CRUISE_SPEED">
            <summary>
            Gets the trains desired speed.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_TRAIN_DIRECTION">
            <summary>
            Gets the direction the train is facing
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_TRAIN_FORWARD_CARRIAGE">
            <summary>
            GET_TRAIN_FORWARD_CARRIAGE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_TRAIN_STATE">
            <summary>
            GET_TRAIN_STATE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_TRAIN_TRACK_INDEX">
            <summary>
            GET_TRAIN_TRACK_INDEX
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_BODY_HEALTH">
            <summary>
            GET_VEHICLE_BODY_HEALTH
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_COLOURS">
            <summary>
            GET_VEHICLE_COLOURS
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_CUSTOM_PRIMARY_COLOUR">
            <summary>
            GET_VEHICLE_CUSTOM_PRIMARY_COLOUR
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_CUSTOM_SECONDARY_COLOUR">
            <summary>
            GET_VEHICLE_CUSTOM_SECONDARY_COLOUR
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_DASHBOARD_COLOUR">
            <summary>
            GET_VEHICLE_DASHBOARD_COLOUR
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_DIRT_LEVEL">
            <summary>
            GET_VEHICLE_DIRT_LEVEL
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_DOOR_LOCK_STATUS">
            <summary>
            ```lua
            enum_VehicleLockStatus = {
                None = 0,
                Locked = 2,
                LockedForPlayer = 3,
                StickPlayerInside = 4, -- Doesn't allow players to exit the vehicle with the exit vehicle key.
                CanBeBrokenInto = 7, -- Can be broken into the car. If the glass is broken, the value will be set to 1
                CanBeBrokenIntoPersist = 8, -- Can be broken into persist
                CannotBeTriedToEnter = 10, -- Cannot be tried to enter (Nothing happens when you press the vehicle enter key).
            }
            ```
            
            It should be [noted](https://forum.cfx.re/t/4863241) that while the [client-side command](#\_0x25BC98A59C2EA962) and its
            setter distinguish between states 0 (unset) and 1 (unlocked), the game will synchronize both as state 0, so the server-side
            command will return only '0' if unlocked.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_DOOR_STATUS">
            <summary>
            Returns the open position of the specified door on the target vehicle.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_DOORS_LOCKED_FOR_PLAYER">
            <summary>
            Currently it only works when set to "all players".
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_ENGINE_HEALTH">
            <summary>
            GET_VEHICLE_ENGINE_HEALTH
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_EXTRA_COLOURS">
            <summary>
            GET_VEHICLE_EXTRA_COLOURS
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_FLIGHT_NOZZLE_POSITION">
            <summary>
            Gets the flight nozzel position for the specified vehicle. See the client-side [\_GET_VEHICLE_FLIGHT_NOZZLE_POSITION](#\_0xDA62027C8BDB326E) native for usage examples.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_HANDBRAKE">
            <summary>
            GET_VEHICLE_HANDBRAKE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_HEADLIGHTS_COLOUR">
            <summary>
            GET_VEHICLE_HEADLIGHTS_COLOUR
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_HOMING_LOCKON_STATE">
            <summary>
            Gets the lock on state for the specified vehicle. See the client-side [GET_VEHICLE_HOMING_LOCKON_STATE](#\_0xE6B0E8CFC3633BF0) native for a description of lock on states.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_HORN_TYPE">
            <summary>
            This is a getter for the client-side native [`START_VEHICLE_HORN`](#\_0x9C8C6504B5B63D2C), which allows you to return the horn type of the vehicle.
            
            **Note**: This native only gets the hash value set with `START_VEHICLE_HORN`. If a wrong hash is passed into `START_VEHICLE_HORN`, it will return this wrong hash.
            
            ```cpp
            enum eHornTypes
            {
                NORMAL = 1330140148,
                HELDDOWN = -2087385909,
                AGGRESSIVE = -92810745
            }
            ```
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_INTERIOR_COLOUR">
            <summary>
            GET_VEHICLE_INTERIOR_COLOUR
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_LIGHTS_STATE">
            <summary>
            GET_VEHICLE_LIGHTS_STATE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_LIVERY">
            <summary>
            GET_VEHICLE_LIVERY
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_LOCK_ON_TARGET">
            <summary>
            Gets the vehicle that is locked on to for the specified vehicle.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_NEON_COLOUR">
            <summary>
            Getter to check the neon colour of a vehicle. This native is the server side getter of [GET_VEHICLE_NEON_LIGHTS_COLOUR](#\_0x7619EEE8C886757F).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_NEON_ENABLED">
            <summary>
            Getter to check if one of the neon lights of a vehicle is enabled. This native is the server side getter of [IS_VEHICLE_NEON_LIGHT_ENABLED](#\_0x8C4B92553E4766A5).
            
            ```cpp
            enum neonIndex
            {
                NEON_BACK = 0,   // Back neon
                NEON_RIGHT = 1,  // Right neon
                NEON_LEFT = 2,   // Left neon
                NEON_FRONT = 3   // Front neon
            };
            ```
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_NUMBER_PLATE_TEXT">
            <summary>
            GET_VEHICLE_NUMBER_PLATE_TEXT
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_NUMBER_PLATE_TEXT_INDEX">
            <summary>
            GET_VEHICLE_NUMBER_PLATE_TEXT_INDEX
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_PED_IS_IN">
            <summary>
            Gets the vehicle the specified Ped is/was in depending on bool value. This native is used server side when using OneSync.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_PETROL_TANK_HEALTH">
            <summary>
            GET_VEHICLE_PETROL_TANK_HEALTH
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_RADIO_STATION_INDEX">
            <summary>
            GET_VEHICLE_RADIO_STATION_INDEX
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_ROOF_LIVERY">
            <summary>
            GET_VEHICLE_ROOF_LIVERY
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_STEERING_ANGLE">
            <summary>
            GET_VEHICLE_STEERING_ANGLE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_TOTAL_REPAIRS">
            <summary>
            GET_VEHICLE_TOTAL_REPAIRS
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_TYPE">
            <summary>
            Returns the type of the passed vehicle.
            
            For client scripts, reference the more detailed [GET_VEHICLE_TYPE_RAW](#\_0xDE73BC10) native.
            
            ### Vehicle types
            
            *   automobile
            *   bike
            *   boat
            *   heli
            *   plane
            *   submarine
            *   trailer
            *   train
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_TYRE_SMOKE_COLOR">
            <summary>
            GET_VEHICLE_TYRE_SMOKE_COLOR
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_WHEEL_TYPE">
            <summary>
            GET_VEHICLE_WHEEL_TYPE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_WINDOW_TINT">
            <summary>
            GET_VEHICLE_WINDOW_TINT
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GIVE_WEAPON_COMPONENT_TO_PED">
            <summary>
            GIVE_WEAPON_COMPONENT_TO_PED
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GIVE_WEAPON_TO_PED">
            <summary>
            GIVE_WEAPON_TO_PED
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.HAS_ENTITY_BEEN_MARKED_AS_NO_LONGER_NEEDED">
            <summary>
            HAS_ENTITY_BEEN_MARKED_AS_NO_LONGER_NEEDED
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.HAS_VEHICLE_BEEN_DAMAGED_BY_BULLETS">
            <summary>
            HAS_VEHICLE_BEEN_DAMAGED_BY_BULLETS
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.HAS_VEHICLE_BEEN_OWNED_BY_PLAYER">
            <summary>
            HAS_VEHICLE_BEEN_OWNED_BY_PLAYER
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_ACE_ALLOWED">
            <summary>
            IS_ACE_ALLOWED
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_BOAT_ANCHORED_AND_FROZEN">
            <summary>
            IS_BOAT_ANCHORED_AND_FROZEN
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_BOAT_WRECKED">
            <summary>
            IS_BOAT_WRECKED
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_DUPLICITY_VERSION">
            <summary>
            Gets whether or not this is the CitizenFX server.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_ENTITY_POSITION_FROZEN">
            <summary>
            A getter for [FREEZE_ENTITY_POSITION](#\_0x428CA6DBD1094446).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_ENTITY_VISIBLE">
            <summary>
            This native checks if the given entity is visible.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_FLASH_LIGHT_ON">
            <summary>
            IS_FLASH_LIGHT_ON
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_HELI_TAIL_BOOM_BREAKABLE">
            <summary>
            This is a getter for [SET_HELI_TAIL_EXPLODE_THROW_DASHBOARD](#\_0x3EC8BF18AA453FE9)
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_HELI_TAIL_BOOM_BROKEN">
            <summary>
            IS_HELI_TAIL_BOOM_BROKEN
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_PED_A_PLAYER">
            <summary>
            This native checks if the given ped is a player.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_PED_HANDCUFFED">
            <summary>
            IS_PED_HANDCUFFED
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_PED_RAGDOLL">
            <summary>
            IS_PED_RAGDOLL
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_PED_STRAFING">
            <summary>
            IS_PED_STRAFING
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_PED_USING_ACTION_MODE">
            <summary>
            IS_PED_USING_ACTION_MODE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_PLAYER_ACE_ALLOWED">
            <summary>
            IS_PLAYER_ACE_ALLOWED
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_PLAYER_COMMERCE_INFO_LOADED">
            <summary>
            Requests whether or not the commerce data for the specified player has loaded.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_PLAYER_COMMERCE_INFO_LOADED_EXT">
            <summary>
            Requests whether or not the commerce data for the specified player has loaded from Tebex.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_PLAYER_EVADING_WANTED_LEVEL">
            <summary>
            This will return true if the player is evading wanted level, meaning that the wanted level stars are blink.
            Otherwise will return false.
            
            If the player is not wanted, it simply returns false.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_PLAYER_IN_FREE_CAM_MODE">
            <summary>
            IS_PLAYER_IN_FREE_CAM_MODE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_PLAYER_USING_SUPER_JUMP">
            <summary>
            IS_PLAYER_USING_SUPER_JUMP
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_PRINCIPAL_ACE_ALLOWED">
            <summary>
            IS_PRINCIPAL_ACE_ALLOWED
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_TRAIN_CABOOSE">
            <summary>
            IS_TRAIN_CABOOSE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_VEHICLE_ENGINE_STARTING">
            <summary>
            IS_VEHICLE_ENGINE_STARTING
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_VEHICLE_EXTRA_TURNED_ON">
            <summary>
            IS_VEHICLE_EXTRA_TURNED_ON
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_VEHICLE_SIREN_ON">
            <summary>
            IS_VEHICLE_SIREN_ON
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_VEHICLE_TYRE_BURST">
            <summary>
            IS_VEHICLE_TYRE_BURST
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_VEHICLE_WINDOW_INTACT">
            <summary>
            See the client-side [IS_VEHICLE_WINDOW_INTACT](#\_0x46E571A0E20D01F1) for a window indexes list.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.LOAD_PLAYER_COMMERCE_DATA">
            <summary>
            Requests the commerce data for the specified player, including the owned SKUs. Use `IS_PLAYER_COMMERCE_INFO_LOADED` to check if it has loaded.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.LOAD_PLAYER_COMMERCE_DATA_EXT">
            <summary>
            Requests the commerce data from Tebex for the specified player, including the owned SKUs.
            
            Use [`IS_PLAYER_COMMERCE_INFO_LOADED_EXT`](#\_0x1D14F4FE) to check if it has loaded.
            
            This will not automatically update whenever a client purchases a package, if you want to fetch new purchases you will need to call this native again.
            
            This native will temporarily cache the players commerce data for 10 seconds, a call to this native after 10 seconds will re-fetch the players commerce data.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.LOAD_RESOURCE_FILE">
            <summary>
            Reads the contents of a text file in a specified resource.
            If executed on the client, this file has to be included in `files` in the resource manifest.
            Example: `local data = LoadResourceFile("devtools", "data.json")`
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.MUMBLE_CREATE_CHANNEL">
            <summary>
            Create a permanent voice channel.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.MUMBLE_IS_PLAYER_MUTED">
            <summary>
            Checks if the player is currently muted
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.MUMBLE_SET_PLAYER_MUTED">
            <summary>
            Mutes or unmutes the specified player
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.NETWORK_GET_ENTITY_FROM_NETWORK_ID">
            <summary>
            NETWORK_GET_ENTITY_FROM_NETWORK_ID
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.NETWORK_GET_ENTITY_OWNER">
            <summary>
            Returns the owner ID of the specified entity.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.NETWORK_GET_FIRST_ENTITY_OWNER">
            <summary>
            Returns the first owner ID of the specified entity.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.NETWORK_GET_NETWORK_ID_FROM_ENTITY">
            <summary>
            NETWORK_GET_NETWORK_ID_FROM_ENTITY
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.NETWORK_GET_VOICE_PROXIMITY_OVERRIDE_FOR_PLAYER">
            <summary>
            NETWORK_GET_VOICE_PROXIMITY_OVERRIDE_FOR_PLAYER
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.PERFORM_HTTP_REQUEST_INTERNAL">
            <summary>
            PERFORM_HTTP_REQUEST_INTERNAL
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.PERFORM_HTTP_REQUEST_INTERNAL_EX">
            <summary>
            PERFORM_HTTP_REQUEST_INTERNAL_EX
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.PRINT_STRUCTURED_TRACE">
            <summary>
            Prints 'structured trace' data to the server `file descriptor 3` channel. This is not generally useful outside of
            server monitoring utilities.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.PROFILER_ENTER_SCOPE">
            <summary>
            Scope entry for profiler.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.PROFILER_EXIT_SCOPE">
            <summary>
            Scope exit for profiler.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.PROFILER_IS_RECORDING">
            <summary>
            Returns true if the profiler is active.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.REGISTER_COMMAND">
            <summary>
            Registered commands can be executed by entering them in the client console (this works for client side and server side registered commands). Or by entering them in the server console/through an RCON client (only works for server side registered commands). Or if you use a supported chat resource, like the default one provided in the cfx-server-data repository, then you can enter the command in chat by prefixing it with a `/`.
            
            Commands registered using this function can also be executed by resources, using the [`ExecuteCommand` native](#\_0x561C060B).
            
            The restricted bool is not used on the client side. Permissions can only be checked on the server side, so if you want to limit your command with an ace permission automatically, make it a server command (by registering it in a server script).
            
            **Example result**:
            
            ![](https://i.imgur.com/TaCnG09.png)
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.REGISTER_CONSOLE_LISTENER">
            <summary>
            Registers a listener for console output messages.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.REGISTER_RESOURCE_AS_EVENT_HANDLER">
            <summary>
            An internal function which allows the current resource's HLL script runtimes to receive state for the specified event.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.REGISTER_RESOURCE_ASSET">
            <summary>
            **Experimental**: This native may be altered or removed in future versions of CitizenFX without warning.
            
            Registers a cached resource asset with the resource system, similar to the automatic scanning of the `stream/` folder.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.REGISTER_RESOURCE_BUILD_TASK_FACTORY">
            <summary>
            Registers a build task factory for resources.
            The function should return an object (msgpack map) with the following fields:
            
            ```
            {
            // returns whether the specific resource should be built
            shouldBuild = func(resourceName: string): bool,
            
            // asynchronously start building the specific resource.
            // call cb when completed
            build = func(resourceName: string, cb: func(success: bool, status: string): void): void
            }
            ```
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.REMOVE_ALL_PED_WEAPONS">
            <summary>
            Parameter `p1` does not seem to be used or referenced in game binaries.\
            **Note:** When called for networked entities, a `CRemoveAllWeaponsEvent` will be created per request.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.REMOVE_BLIP">
            <summary>
            Removes the blip from your map.
            
            **Note:** This function only works on the script that created the blip, if you wish to remove blips created by other scripts, see [`SET_THIS_SCRIPT_CAN_REMOVE_BLIPS_CREATED_BY_ANY_SCRIPT`](#\_0x86A652570E5F25DD).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.REMOVE_CONVAR_CHANGE_LISTENER">
            <summary>
            REMOVE_CONVAR_CHANGE_LISTENER
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.REMOVE_STATE_BAG_CHANGE_HANDLER">
            <summary>
            **Experimental**: This native may be altered or removed in future versions of CitizenFX without warning.
            
            Removes a handler for changes to a state bag.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.REMOVE_WEAPON_COMPONENT_FROM_PED">
            <summary>
            REMOVE_WEAPON_COMPONENT_FROM_PED
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.REMOVE_WEAPON_FROM_PED">
            <summary>
            This native removes a specified weapon from your selected ped.  
            Weapon Hashes: pastebin.com/0wwDZgkF  
            Example:  
            C#:  
            Function.Call(Hash.REMOVE_WEAPON_FROM_PED, Game.Player.Character, 0x99B507EA);  
            C++:  
            WEAPON::REMOVE_WEAPON_FROM_PED(PLAYER::PLAYER_PED_ID(), 0x99B507EA);  
            The code above removes the knife from the player.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.REQUEST_PLAYER_COMMERCE_SESSION">
            <summary>
            Requests the specified player to buy the passed SKU. This'll pop up a prompt on the client, which upon acceptance
            will open the browser prompting further purchase details.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SAVE_RESOURCE_FILE">
            <summary>
            Writes the specified data to a file in the specified resource.
            Using a length of `-1` will automatically detect the length assuming the data is a C string.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SCAN_RESOURCE_ROOT">
            <summary>
            Scans the resources in the specified resource root. This function is only available in the 'monitor mode' process and is
            not available for user resources.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SCHEDULE_RESOURCE_TICK">
            <summary>
            Schedules the specified resource to run a tick as soon as possible, bypassing the server's fixed tick rate.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_BLIP_SPRITE">
            <summary>
            Sets the displayed sprite for a specific blip.
            
            There's a [list of sprites](https://docs.fivem.net/game-references/blips/) on the FiveM documentation site.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_CONVAR">
            <summary>
            SET_CONVAR
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_CONVAR_REPLICATED">
            <summary>
            Used to replicate a server variable onto clients.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_CONVAR_SERVER_INFO">
            <summary>
            SET_CONVAR_SERVER_INFO
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_CURRENT_PED_WEAPON">
            <summary>
            SET_CURRENT_PED_WEAPON
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_ENTITY_COORDS">
            <summary>
            Sets the coordinates (world position) for a specified entity, offset by the radius of the entity on the Z axis.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_ENTITY_DISTANCE_CULLING_RADIUS">
            <summary>
            It overrides the default distance culling radius of an entity. Set to `0.0` to reset.
            If you want to interact with an entity outside of your players' scopes set the radius to a huge number.
            
            **WARNING**: Culling natives are deprecated and have known, [unfixable issues](https://forum.cfx.re/t/issue-with-culling-radius-and-server-side-entities/4900677/4)
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_ENTITY_HEADING">
            <summary>
            Set the heading of an entity in degrees also known as "Yaw".
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_ENTITY_IGNORE_REQUEST_CONTROL_FILTER">
            <summary>
            It allows to flag an entity to ignore the request control filter policy.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_ENTITY_ORPHAN_MODE">
            <summary>
            ```cpp
            enum EntityOrphanMode {
                // Default, this will delete the entity when it isn't relevant to any players
                // NOTE: this *doesn't* mean when they're no longer in scope
                DeleteWhenNotRelevant = 0,
                // The entity will be deleted whenever its original owner disconnects
                // NOTE: if this is set when the entities original owner has already left it will be
                // marked for deletion (similar to just calling DELETE_ENTITY)
                DeleteOnOwnerDisconnect = 1,
                // The entity will never be deleted by the server when it does relevancy checks
                // you should only use this on entities that need to be relatively persistent
                KeepEntity = 2
            }
            ```
            
            Sets what the server will do when the entity no longer has its original owner. By default the server will cleanup entities that it considers "no longer relevant".
            
            When used on trains, this native will recursively call onto all attached carriages.
            
            **NOTE**: When used with `KeepEntity` (2) this native only guarantees that the ***server*** will not delete the entity, client requests to delete the entity will still work perfectly fine.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_ENTITY_REMOTE_SYNCED_SCENES_ALLOWED">
            <summary>
            Enables or disables the owner check for the specified entity in network-synchronized scenes. When set to `false`, the entity cannot participate in synced scenes initiated by clients that do not own the entity.
            
            By default, this is `false` for all entities, meaning only the entity's owner can include it in networked synchronized scenes.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_ENTITY_ROTATION">
            <summary>
            Sets the rotation of a specified entity in the game world.
            
            ```
            NativeDB Introduced: v323
            ```
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_ENTITY_ROUTING_BUCKET">
            <summary>
            Sets the routing bucket for the specified entity.
            
            Routing buckets are also known as 'dimensions' or 'virtual worlds' in past echoes, however they are population-aware.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_ENTITY_VELOCITY">
            <summary>
            Note that the third parameter(denoted as z) is "up and down" with positive numbers encouraging upwards movement.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_GAME_TYPE">
            <summary>
            SET_GAME_TYPE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_HTTP_HANDLER">
            <summary>
            Sets the handler for HTTP requests made to the executing resource.
            
            Example request URL: `http://localhost:30120/http-test/ping` - this request will be sent to the `http-test` resource with the `/ping` path.
            
            The handler function assumes the following signature:
            
            ```ts
            function HttpHandler(
              request: {
                address: string;
                headers: Record&lt;string, string&gt;;
                method: string;
                path: string;
                setDataHandler(handler: (data: string) =&gt; void): void;
                setDataHandler(handler: (data: ArrayBuffer) =&gt; void, binary: 'binary'): void;
                setCancelHandler(handler: () =&gt; void): void;
              },
              response: {
                writeHead(code: number, headers?: Record&lt;string, string | string[]&gt;): void;
                write(data: string): void;
                send(data?: string): void;
              }
            ): void;
            ```
            
            *   **request**: The request object.
                *   **address**: The IP address of the request sender.
                *   **path**: The path to where the request was sent.
                *   **headers**: The headers sent with the request.
                *   **method**: The request method.
                *   **setDataHandler**: Sets the handler for when a data body is passed with the request. Additionally you can pass the `'binary'` argument to receive a `BufferArray` in JavaScript or `System.Byte[]` in C# (has no effect in Lua).
                *   **setCancelHandler**: Sets the handler for when the request is cancelled.
            *   **response**: An object to control the response.
                *   **writeHead**: Sets the status code &amp; headers of the response. Can be only called once and won't work if called after running other response functions.
                *   **write**: Writes to the response body without sending it. Can be called multiple times.
                *   **send**: Writes to the response body and then sends it along with the status code &amp; headers, finishing the request.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_MAP_NAME">
            <summary>
            SET_MAP_NAME
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PED_AMMO">
            <summary>
            NativeDB Added Parameter 4: BOOL p3
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PED_ARMOUR">
            <summary>
            Sets the armor of the specified ped.  
            ped: The Ped to set the armor of.  
            amount: A value between 0 and 100 indicating the value to set the Ped's armor to.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PED_CAN_RAGDOLL">
            <summary>
            SET_PED_CAN_RAGDOLL
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PED_COMPONENT_VARIATION">
            <summary>
            This native is used to set component variation on a ped. Components, drawables and textures IDs are related to the ped model.
            
            ### MP Freemode list of components
            
            **0**: Face
            **1**: Mask
            **2**: Hair
            **3**: Torso
            **4**: Leg
            **5**: Parachute / bag
            **6**: Shoes
            **7**: Accessory
            **8**: Undershirt
            **9**: Kevlar
            **10**: Badge
            **11**: Torso 2
            
            List of Component IDs
            
            ```cpp
            // Components
            enum ePedVarComp
            {
                PV_COMP_INVALID = 0xFFFFFFFF,
                PV_COMP_HEAD = 0, // "HEAD"
                PV_COMP_BERD = 1, // "BEARD"
                PV_COMP_HAIR = 2, // "HAIR"
                PV_COMP_UPPR = 3, // "UPPER"
                PV_COMP_LOWR = 4, // "LOWER"
                PV_COMP_HAND = 5, // "HAND"
                PV_COMP_FEET = 6, // "FEET"
                PV_COMP_TEEF = 7, // "TEETH"
                PV_COMP_ACCS = 8, // "ACCESSORIES"
                PV_COMP_TASK = 9, // "TASK"
                PV_COMP_DECL = 10, // "DECL"
                PV_COMP_JBIB = 11, // "JBIB"
                PV_COMP_MAX = 12,
            };
            ```
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PED_CONFIG_FLAG">
            <summary>
            cpp
            // Potential names and hash collisions included as comments
            enum ePedConfigFlags {
            	CPED_CONFIG_FLAG_CreatedByFactory = 0,
            	CPED_CONFIG_FLAG_CanBeShotInVehicle = 1,
            	CPED_CONFIG_FLAG_NoCriticalHits = 2,
            	CPED_CONFIG_FLAG_DrownsInWater = 3,
            	CPED_CONFIG_FLAG_DrownsInSinkingVehicle = 4,
            	CPED_CONFIG_FLAG_DiesInstantlyWhenSwimming = 5,
            	CPED_CONFIG_FLAG_HasBulletProofVest = 6,
            	CPED_CONFIG_FLAG_UpperBodyDamageAnimsOnly = 7,
            	CPED_CONFIG_FLAG_NeverFallOffSkis = 8,
            	CPED_CONFIG_FLAG_NeverEverTargetThisPed = 9,
            	CPED_CONFIG_FLAG_ThisPedIsATargetPriority = 10,
            	CPED_CONFIG_FLAG_TargettableWithNoLos = 11,
            	CPED_CONFIG_FLAG_DoesntListenToPlayerGroupCommands = 12,
            	CPED_CONFIG_FLAG_NeverLeavesGroup = 13,
            	CPED_CONFIG_FLAG_DoesntDropWeaponsWhenDead = 14,
            	CPED_CONFIG_FLAG_SetDelayedWeaponAsCurrent = 15,
            	CPED_CONFIG_FLAG_KeepTasksAfterCleanUp = 16,
            	CPED_CONFIG_FLAG_BlockNonTemporaryEvents = 17,
            	CPED_CONFIG_FLAG_HasAScriptBrain = 18,
            	CPED_CONFIG_FLAG_WaitingForScriptBrainToLoad = 19,
            	CPED_CONFIG_FLAG_AllowMedicsToReviveMe = 20,
            	CPED_CONFIG_FLAG_MoneyHasBeenGivenByScript = 21,
            	CPED_CONFIG_FLAG_NotAllowedToCrouch = 22,
            	CPED_CONFIG_FLAG_DeathPickupsPersist = 23,
            	CPED_CONFIG_FLAG_IgnoreSeenMelee = 24,
            	CPED_CONFIG_FLAG_ForceDieIfInjured = 25,
            	CPED_CONFIG_FLAG_DontDragMeOutCar = 26,
            	CPED_CONFIG_FLAG_StayInCarOnJack = 27,
            	CPED_CONFIG_FLAG_ForceDieInCar = 28,
            	CPED_CONFIG_FLAG_GetOutUndriveableVehicle = 29,
            	CPED_CONFIG_FLAG_WillRemainOnBoatAfterMissionEnds = 30,
            	CPED_CONFIG_FLAG_DontStoreAsPersistent = 31,
            	CPED_CONFIG_FLAG_WillFlyThroughWindscreen = 32,
            	CPED_CONFIG_FLAG_DieWhenRagdoll = 33,
            	CPED_CONFIG_FLAG_HasHelmet = 34,
            	CPED_CONFIG_FLAG_UseHelmet = 35,
            	CPED_CONFIG_FLAG_DontTakeOffHelmet = 36,
            	CPED_CONFIG_FLAG_HideInCutscene = 37,
            	CPED_CONFIG_FLAG_PedIsEnemyToPlayer = 38,
            	CPED_CONFIG_FLAG_DisableEvasiveDives = 39,
            	CPED_CONFIG_FLAG_PedGeneratesDeadBodyEvents = 40,
            	CPED_CONFIG_FLAG_DontAttackPlayerWithoutWantedLevel = 41,
            	CPED_CONFIG_FLAG_DontInfluenceWantedLevel = 42,
            	CPED_CONFIG_FLAG_DisablePlayerLockon = 43,
            	CPED_CONFIG_FLAG_DisableLockonToRandomPeds = 44,
            	CPED_CONFIG_FLAG_AllowLockonToFriendlyPlayers = 45,
            	_0xDB115BFA = 46,
            	CPED_CONFIG_FLAG_PedBeingDeleted = 47,
            	CPED_CONFIG_FLAG_BlockWeaponSwitching = 48,
            	CPED_CONFIG_FLAG_BlockGroupPedAimedAtResponse = 49,
            	CPED_CONFIG_FLAG_WillFollowLeaderAnyMeans = 50,
            	CPED_CONFIG_FLAG_BlippedByScript = 51,
            	CPED_CONFIG_FLAG_DrawRadarVisualField = 52,
            	CPED_CONFIG_FLAG_StopWeaponFiringOnImpact = 53,
            	CPED_CONFIG_FLAG_DissableAutoFallOffTests = 54,
            	CPED_CONFIG_FLAG_SteerAroundDeadBodies = 55,
            	CPED_CONFIG_FLAG_ConstrainToNavMesh = 56,
            	CPED_CONFIG_FLAG_SyncingAnimatedProps = 57,
            	CPED_CONFIG_FLAG_IsFiring = 58,
            	CPED_CONFIG_FLAG_WasFiring = 59,
            	CPED_CONFIG_FLAG_IsStanding = 60,
            	CPED_CONFIG_FLAG_WasStanding = 61,
            	CPED_CONFIG_FLAG_InVehicle = 62,
            	CPED_CONFIG_FLAG_OnMount = 63,
            	CPED_CONFIG_FLAG_AttachedToVehicle = 64,
            	CPED_CONFIG_FLAG_IsSwimming = 65,
            	CPED_CONFIG_FLAG_WasSwimming = 66,
            	CPED_CONFIG_FLAG_IsSkiing = 67,
            	CPED_CONFIG_FLAG_IsSitting = 68,
            	CPED_CONFIG_FLAG_KilledByStealth = 69,
            	CPED_CONFIG_FLAG_KilledByTakedown = 70,
            	CPED_CONFIG_FLAG_Knockedout = 71,
            	CPED_CONFIG_FLAG_ClearRadarBlipOnDeath = 72,
            	CPED_CONFIG_FLAG_JustGotOffTrain = 73,
            	CPED_CONFIG_FLAG_JustGotOnTrain = 74,
            	CPED_CONFIG_FLAG_UsingCoverPoint = 75,
            	CPED_CONFIG_FLAG_IsInTheAir = 76,
            	CPED_CONFIG_FLAG_KnockedUpIntoAir = 77,
            	CPED_CONFIG_FLAG_IsAimingGun = 78,
            	CPED_CONFIG_FLAG_HasJustLeftCar = 79,
            	CPED_CONFIG_FLAG_TargetWhenInjuredAllowed = 80,
            	CPED_CONFIG_FLAG_CurrLeftFootCollNM = 81,
            	CPED_CONFIG_FLAG_PrevLeftFootCollNM = 82,
            	CPED_CONFIG_FLAG_CurrRightFootCollNM = 83,
            	CPED_CONFIG_FLAG_PrevRightFootCollNM = 84,
            	CPED_CONFIG_FLAG_HasBeenBumpedInCar = 85,
            	CPED_CONFIG_FLAG_InWaterTaskQuitToClimbLadder = 86,
            	CPED_CONFIG_FLAG_NMTwoHandedWeaponBothHandsConstrained = 87,
            	CPED_CONFIG_FLAG_CreatedBloodPoolTimer = 88,
            	CPED_CONFIG_FLAG_DontActivateRagdollFromAnyPedImpact = 89,
            	CPED_CONFIG_FLAG_GroupPedFailedToEnterCover = 90,
            	CPED_CONFIG_FLAG_AlreadyChattedOnPhone = 91,
            	CPED_CONFIG_FLAG_AlreadyReactedToPedOnRoof = 92,
            	CPED_CONFIG_FLAG_ForcePedLoadCover = 93,
            	CPED_CONFIG_FLAG_BlockCoweringInCover = 94,
            	CPED_CONFIG_FLAG_BlockPeekingInCover = 95,
            	CPED_CONFIG_FLAG_JustLeftCarNotCheckedForDoors = 96,
            	CPED_CONFIG_FLAG_VaultFromCover = 97,
            	CPED_CONFIG_FLAG_AutoConversationLookAts = 98,
            	CPED_CONFIG_FLAG_UsingCrouchedPedCapsule = 99,
            	CPED_CONFIG_FLAG_HasDeadPedBeenReported = 100,
            	CPED_CONFIG_FLAG_ForcedAim = 101,
            	CPED_CONFIG_FLAG_SteersAroundPeds = 102,
            	CPED_CONFIG_FLAG_SteersAroundObjects = 103,
            	CPED_CONFIG_FLAG_OpenDoorArmIK = 104,
            	CPED_CONFIG_FLAG_ForceReload = 105,
            	CPED_CONFIG_FLAG_DontActivateRagdollFromVehicleImpact = 106,
            	CPED_CONFIG_FLAG_DontActivateRagdollFromBulletImpact = 107,
            	CPED_CONFIG_FLAG_DontActivateRagdollFromExplosions = 108,
            	CPED_CONFIG_FLAG_DontActivateRagdollFromFire = 109,
            	CPED_CONFIG_FLAG_DontActivateRagdollFromElectrocution = 110,
            	CPED_CONFIG_FLAG_IsBeingDraggedToSafety = 111,
            	CPED_CONFIG_FLAG_HasBeenDraggedToSafety = 112,
            	CPED_CONFIG_FLAG_KeepWeaponHolsteredUnlessFired = 113,
            	CPED_CONFIG_FLAG_ForceScriptControlledKnockout = 114,
            	CPED_CONFIG_FLAG_FallOutOfVehicleWhenKilled = 115,
            	CPED_CONFIG_FLAG_GetOutBurningVehicle = 116,
            	CPED_CONFIG_FLAG_BumpedByPlayer = 117,
            	CPED_CONFIG_FLAG_RunFromFiresAndExplosions = 118,
            	CPED_CONFIG_FLAG_TreatAsPlayerDuringTargeting = 119,
            	CPED_CONFIG_FLAG_IsHandCuffed = 120,
            	CPED_CONFIG_FLAG_IsAnkleCuffed = 121,
            	CPED_CONFIG_FLAG_DisableMelee = 122,
            	CPED_CONFIG_FLAG_DisableUnarmedDrivebys = 123,
            	CPED_CONFIG_FLAG_JustGetsPulledOutWhenElectrocuted = 124,
            	CPED_CONFIG_FLAG_UNUSED_REPLACE_ME = 125,
            	CPED_CONFIG_FLAG_WillNotHotwireLawEnforcementVehicle = 126,
            	CPED_CONFIG_FLAG_WillCommandeerRatherThanJack = 127,
            	CPED_CONFIG_FLAG_CanBeAgitated = 128,
            	CPED_CONFIG_FLAG_ForcePedToFaceLeftInCover = 129,
            	CPED_CONFIG_FLAG_ForcePedToFaceRightInCover = 130,
            	CPED_CONFIG_FLAG_BlockPedFromTurningInCover = 131,
            	CPED_CONFIG_FLAG_KeepRelationshipGroupAfterCleanUp = 132,
            	CPED_CONFIG_FLAG_ForcePedToBeDragged = 133,
            	CPED_CONFIG_FLAG_PreventPedFromReactingToBeingJacked = 134,
            	CPED_CONFIG_FLAG_IsScuba = 135,
            	CPED_CONFIG_FLAG_WillArrestRatherThanJack = 136,
            	CPED_CONFIG_FLAG_RemoveDeadExtraFarAway = 137,
            	CPED_CONFIG_FLAG_RidingTrain = 138,
            	CPED_CONFIG_FLAG_ArrestResult = 139,
            	CPED_CONFIG_FLAG_CanAttackFriendly = 140,
            	CPED_CONFIG_FLAG_WillJackAnyPlayer = 141,
            	CPED_CONFIG_FLAG_BumpedByPlayerVehicle = 142,
            	CPED_CONFIG_FLAG_DodgedPlayerVehicle = 143,
            	CPED_CONFIG_FLAG_WillJackWantedPlayersRatherThanStealCar = 144,
            	CPED_CONFIG_FLAG_NoCopWantedAggro = 145,
            	CPED_CONFIG_FLAG_DisableLadderClimbing = 146,
            	CPED_CONFIG_FLAG_StairsDetected = 147,
            	CPED_CONFIG_FLAG_SlopeDetected = 148,
            	CPED_CONFIG_FLAG_HelmetHasBeenShot = 149,
            	CPED_CONFIG_FLAG_CowerInsteadOfFlee = 150,
            	CPED_CONFIG_FLAG_CanActivateRagdollWhenVehicleUpsideDown = 151,
            	CPED_CONFIG_FLAG_AlwaysRespondToCriesForHelp = 152,
            	CPED_CONFIG_FLAG_DisableBloodPoolCreation = 153,
            	CPED_CONFIG_FLAG_ShouldFixIfNoCollision = 154,
            	CPED_CONFIG_FLAG_CanPerformArrest = 155,
            	CPED_CONFIG_FLAG_CanPerformUncuff = 156,
            	CPED_CONFIG_FLAG_CanBeArrested = 157,
            	CPED_CONFIG_FLAG_MoverConstrictedByOpposingCollisions = 158,
            	CPED_CONFIG_FLAG_PlayerPreferFrontSeatMP = 159,
            	CPED_CONFIG_FLAG_DontActivateRagdollFromImpactObject = 160,
            	CPED_CONFIG_FLAG_DontActivateRagdollFromMelee = 161,
            	CPED_CONFIG_FLAG_DontActivateRagdollFromWaterJet = 162,
            	CPED_CONFIG_FLAG_DontActivateRagdollFromDrowning = 163,
            	CPED_CONFIG_FLAG_DontActivateRagdollFromFalling = 164,
            	CPED_CONFIG_FLAG_DontActivateRagdollFromRubberBullet = 165,
            	CPED_CONFIG_FLAG_IsInjured = 166,
            	CPED_CONFIG_FLAG_DontEnterVehiclesInPlayersGroup = 167,
            	CPED_CONFIG_FLAG_SwimmingTasksRunning = 168,
            	CPED_CONFIG_FLAG_PreventAllMeleeTaunts = 169,
            	CPED_CONFIG_FLAG_ForceDirectEntry = 170,
            	CPED_CONFIG_FLAG_AlwaysSeeApproachingVehicles = 171,
            	CPED_CONFIG_FLAG_CanDiveAwayFromApproachingVehicles = 172,
            	CPED_CONFIG_FLAG_AllowPlayerToInterruptVehicleEntryExit = 173,
            	CPED_CONFIG_FLAG_OnlyAttackLawIfPlayerIsWanted = 174,
            	CPED_CONFIG_FLAG_PlayerInContactWithKinematicPed = 175,
            	CPED_CONFIG_FLAG_PlayerInContactWithSomethingOtherThanKinematicPed = 176,
            	CPED_CONFIG_FLAG_PedsJackingMeDontGetIn = 177,
            	CPED_CONFIG_FLAG_AdditionalRappellingPed = 178,
            	CPED_CONFIG_FLAG_PedIgnoresAnimInterruptEvents = 179,
            	CPED_CONFIG_FLAG_IsInCustody = 180,
            	CPED_CONFIG_FLAG_ForceStandardBumpReactionThresholds = 181,
            	CPED_CONFIG_FLAG_LawWillOnlyAttackIfPlayerIsWanted = 182,
            	CPED_CONFIG_FLAG_IsAgitated = 183,
            	CPED_CONFIG_FLAG_PreventAutoShuffleToDriversSeat = 184,
            	CPED_CONFIG_FLAG_UseKinematicModeWhenStationary = 185,
            	CPED_CONFIG_FLAG_EnableWeaponBlocking = 186,
            	CPED_CONFIG_FLAG_HasHurtStarted = 187,
            	CPED_CONFIG_FLAG_DisableHurt = 188,
            	CPED_CONFIG_FLAG_PlayerIsWeird = 189,
            	CPED_CONFIG_FLAG_PedHadPhoneConversation = 190,
            	CPED_CONFIG_FLAG_BeganCrossingRoad = 191,
            	CPED_CONFIG_FLAG_WarpIntoLeadersVehicle = 192,
            	CPED_CONFIG_FLAG_DoNothingWhenOnFootByDefault = 193,
            	CPED_CONFIG_FLAG_UsingScenario = 194,
            	CPED_CONFIG_FLAG_VisibleOnScreen = 195,
            	CPED_CONFIG_FLAG_DontCollideWithKinematic = 196,
            	CPED_CONFIG_FLAG_ActivateOnSwitchFromLowPhysicsLod = 197,
            	CPED_CONFIG_FLAG_DontActivateRagdollOnPedCollisionWhenDead = 198,
            	CPED_CONFIG_FLAG_DontActivateRagdollOnVehicleCollisionWhenDead = 199,
            	CPED_CONFIG_FLAG_HasBeenInArmedCombat = 200,
            	CPED_CONFIG_FLAG_UseDiminishingAmmoRate = 201,
            	CPED_CONFIG_FLAG_Avoidance_Ignore_All = 202,
            	CPED_CONFIG_FLAG_Avoidance_Ignored_by_All = 203,
            	CPED_CONFIG_FLAG_Avoidance_Ignore_Group1 = 204,
            	CPED_CONFIG_FLAG_Avoidance_Member_of_Group1 = 205,
            	CPED_CONFIG_FLAG_ForcedToUseSpecificGroupSeatIndex = 206,
            	CPED_CONFIG_FLAG_LowPhysicsLodMayPlaceOnNavMesh = 207,
            	CPED_CONFIG_FLAG_DisableExplosionReactions = 208,
            	CPED_CONFIG_FLAG_DodgedPlayer = 209,
            	CPED_CONFIG_FLAG_WaitingForPlayerControlInterrupt = 210,
            	CPED_CONFIG_FLAG_ForcedToStayInCover = 211,
            	CPED_CONFIG_FLAG_GeneratesSoundEvents = 212,
            	CPED_CONFIG_FLAG_ListensToSoundEvents = 213,
            	CPED_CONFIG_FLAG_AllowToBeTargetedInAVehicle = 214,
            	CPED_CONFIG_FLAG_WaitForDirectEntryPointToBeFreeWhenExiting = 215,
            	CPED_CONFIG_FLAG_OnlyRequireOnePressToExitVehicle = 216,
            	CPED_CONFIG_FLAG_ForceExitToSkyDive = 217,
            	CPED_CONFIG_FLAG_SteersAroundVehicles = 218,
            	CPED_CONFIG_FLAG_AllowPedInVehiclesOverrideTaskFlags = 219,
            	CPED_CONFIG_FLAG_DontEnterLeadersVehicle = 220,
            	CPED_CONFIG_FLAG_DisableExitToSkyDive = 221,
            	CPED_CONFIG_FLAG_ScriptHasDisabledCollision = 222,
            	CPED_CONFIG_FLAG_UseAmbientModelScaling = 223,
            	CPED_CONFIG_FLAG_DontWatchFirstOnNextHurryAway = 224,
            	CPED_CONFIG_FLAG_DisablePotentialToBeWalkedIntoResponse = 225,
            	CPED_CONFIG_FLAG_DisablePedAvoidance = 226,
            	CPED_CONFIG_FLAG_ForceRagdollUponDeath = 227,
            	CPED_CONFIG_FLAG_CanLosePropsOnDamage = 228,
            	CPED_CONFIG_FLAG_DisablePanicInVehicle = 229,
            	CPED_CONFIG_FLAG_AllowedToDetachTrailer = 230,
            	CPED_CONFIG_FLAG_HasShotBeenReactedToFromFront = 231,
            	CPED_CONFIG_FLAG_HasShotBeenReactedToFromBack = 232,
            	CPED_CONFIG_FLAG_HasShotBeenReactedToFromLeft = 233,
            	CPED_CONFIG_FLAG_HasShotBeenReactedToFromRight = 234,
            	CPED_CONFIG_FLAG_AllowBlockDeadPedRagdollActivation = 235,
            	CPED_CONFIG_FLAG_IsHoldingProp = 236,
            	CPED_CONFIG_FLAG_BlocksPathingWhenDead = 237,
            	CPED_CONFIG_FLAG_ForcePlayNormalScenarioExitOnNextScriptCommand = 238,
            	CPED_CONFIG_FLAG_ForcePlayImmediateScenarioExitOnNextScriptCommand = 239,
            	CPED_CONFIG_FLAG_ForceSkinCharacterCloth = 240,
            	CPED_CONFIG_FLAG_LeaveEngineOnWhenExitingVehicles = 241,
            	CPED_CONFIG_FLAG_PhoneDisableTextingAnimations = 242,
            	CPED_CONFIG_FLAG_PhoneDisableTalkingAnimations = 243,
            	CPED_CONFIG_FLAG_PhoneDisableCameraAnimations = 244,
            	CPED_CONFIG_FLAG_DisableBlindFiringInShotReactions = 245,
            	CPED_CONFIG_FLAG_AllowNearbyCoverUsage = 246,
            	CPED_CONFIG_FLAG_InStrafeTransition = 247,
            	CPED_CONFIG_FLAG_CanPlayInCarIdles = 248,
            	CPED_CONFIG_FLAG_CanAttackNonWantedPlayerAsLaw = 249,
            	CPED_CONFIG_FLAG_WillTakeDamageWhenVehicleCrashes = 250,
            	CPED_CONFIG_FLAG_AICanDrivePlayerAsRearPassenger = 251,
            	CPED_CONFIG_FLAG_PlayerCanJackFriendlyPlayers = 252,
            	CPED_CONFIG_FLAG_OnStairs = 253,
            	CPED_CONFIG_FLAG_SimulatingAiming = 254,
            	CPED_CONFIG_FLAG_AIDriverAllowFriendlyPassengerSeatEntry = 255,
            	CPED_CONFIG_FLAG_ParentCarIsBeingRemoved = 256,
            	CPED_CONFIG_FLAG_AllowMissionPedToUseInjuredMovement = 257,
            	CPED_CONFIG_FLAG_CanLoseHelmetOnDamage = 258,
            	CPED_CONFIG_FLAG_NeverDoScenarioExitProbeChecks = 259,
            	CPED_CONFIG_FLAG_SuppressLowLODRagdollSwitchWhenCorpseSettles = 260,
            	CPED_CONFIG_FLAG_PreventUsingLowerPrioritySeats = 261,
            	CPED_CONFIG_FLAG_JustLeftVehicleNeedsReset = 262,
            	CPED_CONFIG_FLAG_TeleportIfCantReachPlayer = 263,
            	CPED_CONFIG_FLAG_PedsInVehiclePositionNeedsReset = 264,
            	CPED_CONFIG_FLAG_PedsFullyInSeat = 265,
            	CPED_CONFIG_FLAG_AllowPlayerLockOnIfFriendly = 266,
            	CPED_CONFIG_FLAG_UseCameraHeadingForDesiredDirectionLockOnTest = 267,
            	CPED_CONFIG_FLAG_TeleportToLeaderVehicle = 268,
            	CPED_CONFIG_FLAG_Avoidance_Ignore_WeirdPedBuffer = 269,
            	CPED_CONFIG_FLAG_OnStairSlope = 270,
            	CPED_CONFIG_FLAG_HasPlayedNMGetup = 271,
            	CPED_CONFIG_FLAG_DontBlipCop = 272,
            	CPED_CONFIG_FLAG_SpawnedAtExtendedRangeScenario = 273,
            	CPED_CONFIG_FLAG_WalkAlongsideLeaderWhenClose = 274,
            	CPED_CONFIG_FLAG_KillWhenTrapped = 275,
            	CPED_CONFIG_FLAG_EdgeDetected = 276,
            	CPED_CONFIG_FLAG_AlwaysWakeUpPhysicsOfIntersectedPeds = 277,
            	CPED_CONFIG_FLAG_EquippedAmbientLoadOutWeapon = 278,
            	CPED_CONFIG_FLAG_AvoidTearGas = 279,
            	CPED_CONFIG_FLAG_StoppedSpeechUponFreezing = 280,
            	CPED_CONFIG_FLAG_DisableGoToWritheWhenInjured = 281,
            	CPED_CONFIG_FLAG_OnlyUseForcedSeatWhenEnteringHeliInGroup = 282,
            	CPED_CONFIG_FLAG_ThrownFromVehicleDueToExhaustion = 283,
            	CPED_CONFIG_FLAG_UpdateEnclosedSearchRegion = 284,
            	CPED_CONFIG_FLAG_DisableWeirdPedEvents = 285,
            	CPED_CONFIG_FLAG_ShouldChargeNow = 286,
            	CPED_CONFIG_FLAG_RagdollingOnBoat = 287,
            	CPED_CONFIG_FLAG_HasBrandishedWeapon = 288,
            	CPED_CONFIG_FLAG_AllowMinorReactionsAsMissionPed = 289,
            	CPED_CONFIG_FLAG_BlockDeadBodyShockingEventsWhenDead = 290,
            	CPED_CONFIG_FLAG_PedHasBeenSeen = 291,
            	CPED_CONFIG_FLAG_PedIsInReusePool = 292,
            	CPED_CONFIG_FLAG_PedWasReused = 293,
            	CPED_CONFIG_FLAG_DisableShockingEvents = 294,
            	CPED_CONFIG_FLAG_MovedUsingLowLodPhysicsSinceLastActive = 295,
            	CPED_CONFIG_FLAG_NeverReactToPedOnRoof = 296,
            	CPED_CONFIG_FLAG_ForcePlayFleeScenarioExitOnNextScriptCommand = 297,
            	CPED_CONFIG_FLAG_JustBumpedIntoVehicle = 298,
            	CPED_CONFIG_FLAG_DisableShockingDrivingOnPavementEvents = 299,
            	CPED_CONFIG_FLAG_ShouldThrowSmokeNow = 300,
            	CPED_CONFIG_FLAG_DisablePedConstraints = 301,
            	CPED_CONFIG_FLAG_ForceInitialPeekInCover = 302,
            	CPED_CONFIG_FLAG_CreatedByDispatch = 303,
            	CPED_CONFIG_FLAG_PointGunLeftHandSupporting = 304,
            	CPED_CONFIG_FLAG_DisableJumpingFromVehiclesAfterLeader = 305,
            	CPED_CONFIG_FLAG_DontActivateRagdollFromPlayerPedImpact = 306,
            	CPED_CONFIG_FLAG_DontActivateRagdollFromAiRagdollImpact = 307,
            	CPED_CONFIG_FLAG_DontActivateRagdollFromPlayerRagdollImpact = 308,
            	CPED_CONFIG_FLAG_DisableQuadrupedSpring = 309,
            	CPED_CONFIG_FLAG_IsInCluster = 310,
            	CPED_CONFIG_FLAG_ShoutToGroupOnPlayerMelee = 311,
            	CPED_CONFIG_FLAG_IgnoredByAutoOpenDoors = 312,
            	CPED_CONFIG_FLAG_PreferInjuredGetup = 313,
            	CPED_CONFIG_FLAG_ForceIgnoreMeleeActiveCombatant = 314,
            	CPED_CONFIG_FLAG_CheckLoSForSoundEvents = 315,
            	CPED_CONFIG_FLAG_JackedAbandonedCar = 316,
            	CPED_CONFIG_FLAG_CanSayFollowedByPlayerAudio = 317,
            	CPED_CONFIG_FLAG_ActivateRagdollFromMinorPlayerContact = 318,
            	CPED_CONFIG_FLAG_HasPortablePickupAttached = 319,
            	CPED_CONFIG_FLAG_ForcePoseCharacterCloth = 320,
            	CPED_CONFIG_FLAG_HasClothCollisionBounds = 321,
            	CPED_CONFIG_FLAG_HasHighHeels = 322,
            	CPED_CONFIG_FLAG_TreatAsAmbientPedForDriverLockOn = 323,
            	CPED_CONFIG_FLAG_DontBehaveLikeLaw = 324,
            	CPED_CONFIG_FLAG_SpawnedAtScenario = 325,
            	CPED_CONFIG_FLAG_DisablePoliceInvestigatingBody = 326,
            	CPED_CONFIG_FLAG_DisableWritheShootFromGround = 327,
            	CPED_CONFIG_FLAG_LowerPriorityOfWarpSeats = 328,
            	CPED_CONFIG_FLAG_DisableTalkTo = 329,
            	CPED_CONFIG_FLAG_DontBlip = 330,
            	CPED_CONFIG_FLAG_IsSwitchingWeapon = 331,
            	CPED_CONFIG_FLAG_IgnoreLegIkRestrictions = 332,
            	CPED_CONFIG_FLAG_ScriptForceNoTimesliceIntelligenceUpdate = 333,
            	CPED_CONFIG_FLAG_JackedOutOfMyVehicle = 334,
            	CPED_CONFIG_FLAG_WentIntoCombatAfterBeingJacked = 335,
            	CPED_CONFIG_FLAG_DontActivateRagdollForVehicleGrab = 336,
            	CPED_CONFIG_FLAG_ForcePackageCharacterCloth = 337,
            	CPED_CONFIG_FLAG_DontRemoveWithValidOrder = 338,
            	CPED_CONFIG_FLAG_AllowTaskDoNothingTimeslicing = 339,
            	CPED_CONFIG_FLAG_ForcedToStayInCoverDueToPlayerSwitch = 340,
            	CPED_CONFIG_FLAG_ForceProneCharacterCloth = 341,
            	CPED_CONFIG_FLAG_NotAllowedToJackAnyPlayers = 342,
            	CPED_CONFIG_FLAG_InToStrafeTransition = 343,
            	CPED_CONFIG_FLAG_KilledByStandardMelee = 344,
            	CPED_CONFIG_FLAG_AlwaysLeaveTrainUponArrival = 345,
            	CPED_CONFIG_FLAG_ForcePlayDirectedNormalScenarioExitOnNextScriptCommand = 346,
            	CPED_CONFIG_FLAG_OnlyWritheFromWeaponDamage = 347,
            	CPED_CONFIG_FLAG_UseSloMoBloodVfx = 348,
            	CPED_CONFIG_FLAG_EquipJetpack = 349,
            	CPED_CONFIG_FLAG_PreventDraggedOutOfCarThreatResponse = 350,
            	CPED_CONFIG_FLAG_ScriptHasCompletelyDisabledCollision = 351,
            	CPED_CONFIG_FLAG_NeverDoScenarioNavChecks = 352,
            	CPED_CONFIG_FLAG_ForceSynchronousScenarioExitChecking = 353,
            	CPED_CONFIG_FLAG_ThrowingGrenadeWhileAiming = 354,
            	CPED_CONFIG_FLAG_HeadbobToRadioEnabled = 355,
            	CPED_CONFIG_FLAG_ForceDeepSurfaceCheck = 356,
            	CPED_CONFIG_FLAG_DisableDeepSurfaceAnims = 357,
            	CPED_CONFIG_FLAG_DontBlipNotSynced = 358,
            	CPED_CONFIG_FLAG_IsDuckingInVehicle = 359,
            	CPED_CONFIG_FLAG_PreventAutoShuffleToTurretSeat = 360,
            	CPED_CONFIG_FLAG_DisableEventInteriorStatusCheck = 361,
            	CPED_CONFIG_FLAG_HasReserveParachute = 362,
            	CPED_CONFIG_FLAG_UseReserveParachute = 363,
            	CPED_CONFIG_FLAG_TreatDislikeAsHateWhenInCombat = 364,
            	CPED_CONFIG_FLAG_OnlyUpdateTargetWantedIfSeen = 365,
            	CPED_CONFIG_FLAG_AllowAutoShuffleToDriversSeat = 366,
            	CPED_CONFIG_FLAG_DontActivateRagdollFromSmokeGrenade = 367,
            	CPED_CONFIG_FLAG_LinkMBRToOwnerOnChain = 368,
            	CPED_CONFIG_FLAG_AmbientFriendBumpedByPlayer = 369,
            	CPED_CONFIG_FLAG_AmbientFriendBumpedByPlayerVehicle = 370,
            	CPED_CONFIG_FLAG_InFPSUnholsterTransition = 371,
            	CPED_CONFIG_FLAG_PreventReactingToSilencedCloneBullets = 372,
            	CPED_CONFIG_FLAG_DisableInjuredCryForHelpEvents = 373,
            	CPED_CONFIG_FLAG_NeverLeaveTrain = 374,
            	CPED_CONFIG_FLAG_DontDropJetpackOnDeath = 375,
            	CPED_CONFIG_FLAG_UseFPSUnholsterTransitionDuringCombatRoll = 376,
            	CPED_CONFIG_FLAG_ExitingFPSCombatRoll = 377,
            	CPED_CONFIG_FLAG_ScriptHasControlOfPlayer = 378,
            	CPED_CONFIG_FLAG_PlayFPSIdleFidgetsForProjectile = 379,
            	CPED_CONFIG_FLAG_DisableAutoEquipHelmetsInBikes = 380,
            	CPED_CONFIG_FLAG_DisableAutoEquipHelmetsInAircraft = 381,
            	CPED_CONFIG_FLAG_WasPlayingFPSGetup = 382,
            	CPED_CONFIG_FLAG_WasPlayingFPSMeleeActionResult = 383,
            	CPED_CONFIG_FLAG_PreferNoPriorityRemoval = 384,
            	CPED_CONFIG_FLAG_FPSFidgetsAbortedOnFire = 385,
            	CPED_CONFIG_FLAG_ForceFPSIKWithUpperBodyAnim = 386,
            	CPED_CONFIG_FLAG_SwitchingCharactersInFirstPerson = 387,
            	CPED_CONFIG_FLAG_IsClimbingLadder = 388,
            	CPED_CONFIG_FLAG_HasBareFeet = 389,
            	CPED_CONFIG_FLAG_UNUSED_REPLACE_ME_2 = 390,
            	CPED_CONFIG_FLAG_GoOnWithoutVehicleIfItIsUnableToGetBackToRoad = 391,
            	CPED_CONFIG_FLAG_BlockDroppingHealthSnacksOnDeath = 392,
            	CPED_CONFIG_FLAG_ResetLastVehicleOnVehicleExit = 393,
            	CPED_CONFIG_FLAG_ForceThreatResponseToNonFriendToFriendMeleeActions = 394,
            	CPED_CONFIG_FLAG_DontRespondToRandomPedsDamage = 395,
            	CPED_CONFIG_FLAG_AllowContinuousThreatResponseWantedLevelUpdates = 396,
            	CPED_CONFIG_FLAG_KeepTargetLossResponseOnCleanup = 397,
            	CPED_CONFIG_FLAG_PlayersDontDragMeOutOfCar = 398,
            	CPED_CONFIG_FLAG_BroadcastRepondedToThreatWhenGoingToPointShooting = 399,
            	CPED_CONFIG_FLAG_IgnorePedTypeForIsFriendlyWith = 400,
            	CPED_CONFIG_FLAG_TreatNonFriendlyAsHateWhenInCombat = 401,
            	CPED_CONFIG_FLAG_DontLeaveVehicleIfLeaderNotInVehicle = 402,
            	CPED_CONFIG_FLAG_ChangeFromPermanentToAmbientPopTypeOnMigration = 403,
            	CPED_CONFIG_FLAG_AllowMeleeReactionIfMeleeProofIsOn = 404,
            	CPED_CONFIG_FLAG_UsingLowriderLeans = 405,
            	CPED_CONFIG_FLAG_UsingAlternateLowriderLeans = 406,
            	CPED_CONFIG_FLAG_UseNormalExplosionDamageWhenBlownUpInVehicle = 407,
            	CPED_CONFIG_FLAG_DisableHomingMissileLockForVehiclePedInside = 408,
            	CPED_CONFIG_FLAG_DisableTakeOffScubaGear = 409,
            	CPED_CONFIG_FLAG_IgnoreMeleeFistWeaponDamageMult = 410,
            	CPED_CONFIG_FLAG_LawPedsCanFleeFromNonWantedPlayer = 411,
            	CPED_CONFIG_FLAG_ForceBlipSecurityPedsIfPlayerIsWanted = 412,
            	CPED_CONFIG_FLAG_IsHolsteringWeapon = 413,
            	CPED_CONFIG_FLAG_UseGoToPointForScenarioNavigation = 414,
            	CPED_CONFIG_FLAG_DontClearLocalPassengersWantedLevel = 415,
            	CPED_CONFIG_FLAG_BlockAutoSwapOnWeaponPickups = 416,
            	CPED_CONFIG_FLAG_ThisPedIsATargetPriorityForAI = 417,
            	CPED_CONFIG_FLAG_IsSwitchingHelmetVisor = 418,
            	CPED_CONFIG_FLAG_ForceHelmetVisorSwitch = 419,
            	CPED_CONFIG_FLAG_IsPerformingVehicleMelee = 420,
            	CPED_CONFIG_FLAG_UseOverrideFootstepPtFx = 421,
            	CPED_CONFIG_FLAG_DisableVehicleCombat = 422,
            	CPED_CONFIG_FLAG_TreatAsFriendlyForTargetingAndDamage = 423,
            	CPED_CONFIG_FLAG_AllowBikeAlternateAnimations = 424,
            	CPED_CONFIG_FLAG_TreatAsFriendlyForTargetingAndDamageNonSynced = 425,
            	CPED_CONFIG_FLAG_UseLockpickVehicleEntryAnimations = 426,
            	CPED_CONFIG_FLAG_IgnoreInteriorCheckForSprinting = 427,
            	CPED_CONFIG_FLAG_SwatHeliSpawnWithinLastSpottedLocation = 428,
            	CPED_CONFIG_FLAG_DisableStartEngine = 429,
            	CPED_CONFIG_FLAG_IgnoreBeingOnFire = 430,
            	CPED_CONFIG_FLAG_DisableTurretOrRearSeatPreference = 431,
            	CPED_CONFIG_FLAG_DisableWantedHelicopterSpawning = 432,
            	CPED_CONFIG_FLAG_UseTargetPerceptionForCreatingAimedAtEvents = 433,
            	CPED_CONFIG_FLAG_DisableHomingMissileLockon = 434,
            	CPED_CONFIG_FLAG_ForceIgnoreMaxMeleeActiveSupportCombatants = 435,
            	CPED_CONFIG_FLAG_StayInDefensiveAreaWhenInVehicle = 436,
            	CPED_CONFIG_FLAG_DontShoutTargetPosition = 437,
            	CPED_CONFIG_FLAG_DisableHelmetArmor = 438,
            	CPED_CONFIG_FLAG_CreatedByConcealedPlayer = 439,
            	CPED_CONFIG_FLAG_PermanentlyDisablePotentialToBeWalkedIntoResponse = 440,
            	CPED_CONFIG_FLAG_PreventVehExitDueToInvalidWeapon = 441,
            	CPED_CONFIG_FLAG_IgnoreNetSessionFriendlyFireCheckForAllowDamage = 442,
            	CPED_CONFIG_FLAG_DontLeaveCombatIfTargetPlayerIsAttackedByPolice = 443,
            	CPED_CONFIG_FLAG_CheckLockedBeforeWarp = 444,
            	CPED_CONFIG_FLAG_DontShuffleInVehicleToMakeRoom = 445,
            	CPED_CONFIG_FLAG_GiveWeaponOnGetup = 446,
            	CPED_CONFIG_FLAG_DontHitVehicleWithProjectiles = 447,
            	CPED_CONFIG_FLAG_DisableForcedEntryForOpenVehiclesFromTryLockedDoor = 448,
            	CPED_CONFIG_FLAG_FiresDummyRockets = 449,
            	CPED_CONFIG_FLAG_PedIsArresting = 450,
            	CPED_CONFIG_FLAG_IsDecoyPed = 451,
            	CPED_CONFIG_FLAG_HasEstablishedDecoy = 452,
            	CPED_CONFIG_FLAG_BlockDispatchedHelicoptersFromLanding = 453,
            	CPED_CONFIG_FLAG_DontCryForHelpOnStun = 454,
            	CPED_CONFIG_FLAG_HitByTranqWeapon = 455,
            	CPED_CONFIG_FLAG_CanBeIncapacitated = 456,
            	CPED_CONFIG_FLAG_ForcedAimFromArrest = 457,
            	CPED_CONFIG_FLAG_DontChangeTargetFromMelee = 458,
            	_0x4376ABF2 = 459,
            	CPED_CONFIG_FLAG_RagdollFloatsIndefinitely = 460,
            	CPED_CONFIG_FLAG_BlockElectricWeaponDamage = 461,
            	_0x262A3B8E = 462,
            	_0x1AA79A25 = 463,
            }
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PED_DEFAULT_COMPONENT_VARIATION">
            <summary>
            Sets Ped Default Clothes
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash._SET_PED_EYE_COLOR">
            <summary>
            Used for freemode (online) characters.
            
            Indices:
            
            1.  black
            2.  very light blue/green
            3.  dark blue
            4.  brown
            5.  darker brown
            6.  light brown
            7.  blue
            8.  light blue
            9.  pink
            10. yellow
            11. purple
            12. black
            13. dark green
            14. light brown
            15. yellow/black pattern
            16. light colored spiral pattern
            17. shiny red
            18. shiny half blue/half red
            19. half black/half light blue
            20. white/red perimter
            21. green snake
            22. red snake
            23. dark blue snake
            24. dark yellow
            25. bright yellow
            26. all black
            27. red small pupil
            28. devil blue/black
            29. white small pupil
            30. glossed over
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash._SET_PED_FACE_FEATURE">
            <summary>
            Sets the various freemode face features, e.g. nose length, chin shape.
            
            **Indexes (From 0 to 19):**
            
            Parentheses indicate morph scale/direction as in (-1.0 to 1.0)
            
            *   **0**: Nose Width (Thin/Wide)
            *   **1**: Nose Peak (Up/Down)
            *   **2**: Nose Length (Long/Short)
            *   **3**: Nose Bone Curveness (Crooked/Curved)
            *   **4**: Nose Tip (Up/Down)
            *   **5**: Nose Bone Twist (Left/Right)
            *   **6**: Eyebrow (Up/Down)
            *   **7**: Eyebrow (In/Out)
            *   **8**: Cheek Bones (Up/Down)
            *   **9**: Cheek Sideways Bone Size (In/Out)
            *   **10**: Cheek Bones Width (Puffed/Gaunt)
            *   **11**: Eye Opening (Both) (Wide/Squinted)
            *   **12**: Lip Thickness (Both) (Fat/Thin)
            *   **13**: Jaw Bone Width (Narrow/Wide)
            *   **14**: Jaw Bone Shape (Round/Square)
            *   **15**: Chin Bone (Up/Down)
            *   **16**: Chin Bone Length (In/Out or Backward/Forward)
            *   **17**: Chin Bone Shape (Pointed/Square)
            *   **18**: Chin Hole (Chin Bum)
            *   **19**: Neck Thickness (Thin/Thick)
            
            **Note:**
            
            You may need to call [`SetPedHeadBlendData`](#\_0x9414E18B9434C2FE) prior to calling this native in order for it to work.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PED_HAIR_TINT">
            <summary>
            Sets the tint index for the hair on the specified ped.
            
            ```
            NativeDB Introduced: v323
            ```
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PED_HEAD_BLEND_DATA">
            <summary>
            For more info please refer to [this](https://gtaforums.com/topic/858970-all-gtao-face-ids-pedset-ped-head-blend-data-explained) topic.
            
            **Other information:**
            
            IDs start at zero and go Male Non-DLC, Female Non-DLC, Male DLC, and Female DLC.&lt;/br&gt;
            
            This native function is often called prior to calling natives such as:
            
            *   [`SetPedHairColor`](#\_0xA23FE32C)
            *   [`SetPedHeadOverlayColor`](#\_0x78935A27)
            *   [`SetPedHeadOverlay`](#\_0xD28DBA90)
            *   [`SetPedFaceFeature`](#\_0x6C8D4458)
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PED_HEAD_OVERLAY">
            <summary>
            ```
            OverlayID ranges from 0 to 12, index from 0 to _GET_NUM_OVERLAY_VALUES(overlayID)-1, and opacity from 0.0 to 1.0.   
            overlayID       Part                  Index, to disable  
            0               Blemishes             0 - 23, 255  
            1               Facial Hair           0 - 28, 255  
            2               Eyebrows              0 - 33, 255  
            3               Ageing                0 - 14, 255  
            4               Makeup                0 - 74, 255  
            5               Blush                 0 - 6, 255  
            6               Complexion            0 - 11, 255  
            7               Sun Damage            0 - 10, 255  
            8               Lipstick              0 - 9, 255  
            9               Moles/Freckles        0 - 17, 255  
            10              Chest Hair            0 - 16, 255  
            11              Body Blemishes        0 - 11, 255  
            12              Add Body Blemishes    0 - 1, 255  
            ```
            
            **Note:**
            
            You may need to call [`SetPedHeadBlendData`](#\_0x9414E18B9434C2FE) prior to calling this native in order for it to work.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash._SET_PED_HEAD_OVERLAY_COLOR">
            <summary>
            ```
            Used for freemode (online) characters. 
            Called after SET_PED_HEAD_OVERLAY().  
            ```
            
            **Note:**
            
            You may need to call [`SetPedHeadBlendData`](#\_0x9414E18B9434C2FE) prior to calling this native in order for it to work.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PED_INTO_VEHICLE">
            <summary>
            SET_PED_INTO_VEHICLE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PED_PROP_INDEX">
            <summary>
            This native is used to set prop variation on a ped. Components, drawables and textures IDs are related to the ped model.
            
            ### MP Freemode list of props
            
            **0**: Hats
            **1**: Glasses
            **2**: Ears
            **6**: Watches
            **7**: Bracelets
            
            List of Prop IDs
            
            ```cpp
            enum eAnchorPoints
            {
                ANCHOR_HEAD = 0, // "p_head"
                ANCHOR_EYES = 1, // "p_eyes"
                ANCHOR_EARS = 2, // "p_ears"
                ANCHOR_MOUTH = 3, // "p_mouth"
                ANCHOR_LEFT_HAND = 4, // "p_lhand"
                ANCHOR_RIGHT_HAND = 5, // "p_rhand"
                ANCHOR_LEFT_WRIST = 6, // "p_lwrist"
                ANCHOR_RIGHT_WRIST = 7, // "p_rwrist"
                ANCHOR_HIP = 8, // "p_lhip"
                ANCHOR_LEFT_FOOT = 9, // "p_lfoot"
                ANCHOR_RIGHT_FOOT = 10, // "p_rfoot"
                ANCHOR_PH_L_HAND = 11, // "ph_lhand"
                ANCHOR_PH_R_HAND = 12, // "ph_rhand"
                NUM_ANCHORS = 13,
            };
            ```
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PED_RANDOM_COMPONENT_VARIATION">
            <summary>
            p1 is always 0 in R* scripts; and a quick disassembly seems to indicate that p1 is unused.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PED_RANDOM_PROPS">
            <summary>
            SET_PED_RANDOM_PROPS
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PED_RESET_FLAG">
            <summary>
            PED::SET_PED_RESET_FLAG(PLAYER::PLAYER_PED_ID(), 240, 1);
            Known values:
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PED_TO_RAGDOLL">
            <summary>
            p4/p5: Unusued in TU27
            
            ### Ragdoll Types
            
            **0**: CTaskNMRelax
            **1**: CTaskNMScriptControl: Hardcoded not to work in networked environments.
            **Else**: CTaskNMBalance
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PED_TO_RAGDOLL_WITH_FALL">
            <summary>
            cpp
            enum eNMFallType {
                TYPE_FROM_HIGH = 0,
                TYPE_OVER_WALL = 1,
                TYPE_DOWN_STAIRS = 2,
                TYPE_DIE_TYPES = 3,
                TYPE_DIE_FROM_HIGH = 4,
                TYPE_DIE_OVER_WALL = 5,
                TYPE_DIE_DOWN_STAIRS = 6
            }
            ```
            
            ```
            Return variable is never used in R*'s scripts.  
            Not sure what p2 does. It seems like it would be a time judging by it's usage in R*'s scripts, but didn't seem to affect anything in my testings.  
            x, y, and z are coordinates, most likely to where the ped will fall.  
            p7 is probably the force of the fall, but untested, so I left the variable name the same.  
            p8 to p13 are always 0f in R*'s scripts.  
            (Simplified) Example of the usage of the function from R*'s scripts:  
            ped::set_ped_to_ragdoll_with_fall(ped, 1500, 2000, 1, -entity::get_entity_forward_vector(ped), 1f, 0f, 0f, 0f, 0f, 0f, 0f);
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PLAYER_CONTROL">
            <summary>
            Flags:
            SPC_AMBIENT_SCRIPT = (1 &lt;&lt; 1),
            SPC_CLEAR_TASKS = (1 &lt;&lt; 2),
            SPC_REMOVE_FIRES = (1 &lt;&lt; 3),
            SPC_REMOVE_EXPLOSIONS = (1 &lt;&lt; 4),
            SPC_REMOVE_PROJECTILES = (1 &lt;&lt; 5),
            SPC_DEACTIVATE_GADGETS = (1 &lt;&lt; 6),
            SPC_REENABLE_CONTROL_ON_DEATH = (1 &lt;&lt; 7),
            SPC_LEAVE_CAMERA_CONTROL_ON = (1 &lt;&lt; 8),
            SPC_ALLOW_PLAYER_DAMAGE = (1 &lt;&lt; 9),
            SPC_DONT_STOP_OTHER_CARS_AROUND_PLAYER = (1 &lt;&lt; 10),
            SPC_PREVENT_EVERYBODY_BACKOFF = (1 &lt;&lt; 11),
            SPC_ALLOW_PAD_SHAKE = (1 &lt;&lt; 12)
            See: https://alloc8or.re/gta5/doc/enums/eSetPlayerControlFlag.txt
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PLAYER_CULLING_RADIUS">
            <summary>
            Sets the culling radius for the specified player.
            Set to `0.0` to reset.
            
            **WARNING**: Culling natives are deprecated and have known, [unfixable issues](https://forum.cfx.re/t/issue-with-culling-radius-and-server-side-entities/4900677/4)
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PLAYER_INVINCIBLE">
            <summary>
            Make the player impervious to all forms of damage.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PLAYER_MODEL">
            <summary>
            Set the model for a specific Player. Note that this will destroy the current Ped for the Player and create a new one, any reference to the old ped will be invalid after calling this.
            
            As per usual, make sure to request the model first and wait until it has loaded.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PLAYER_ROUTING_BUCKET">
            <summary>
            Sets the routing bucket for the specified player.
            
            Routing buckets are also known as 'dimensions' or 'virtual worlds' in past echoes, however they are population-aware.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PLAYER_WANTED_LEVEL">
            <summary>
            SET_PLAYER_WANTED_LEVEL
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_RESOURCE_KVP">
            <summary>
            A setter for [GET_RESOURCE_KVP_STRING](#\_0x5240DA5A).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_RESOURCE_KVP_FLOAT">
            <summary>
            A setter for [GET_RESOURCE_KVP_FLOAT](#\_0x35BDCEEA).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_RESOURCE_KVP_FLOAT_NO_SYNC">
            <summary>
            Nonsynchronous [SET_RESOURCE_KVP_FLOAT](#\_0x9ADD2938) operation; see [FLUSH_RESOURCE_KVP](#\_0x5240DA5A).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_RESOURCE_KVP_INT">
            <summary>
            A setter for [GET_RESOURCE_KVP_INT](#\_0x557B586A).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_RESOURCE_KVP_INT_NO_SYNC">
            <summary>
            Nonsynchronous [SET_RESOURCE_KVP_INT](#\_0x6A2B1E8) operation; see [FLUSH_RESOURCE_KVP](#\_0x5240DA5A).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_RESOURCE_KVP_NO_SYNC">
            <summary>
            Nonsynchronous [SET_RESOURCE_KVP](#\_0x21C7A35B) operation; see [FLUSH_RESOURCE_KVP](#\_0x5240DA5A).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_ROUTING_BUCKET_ENTITY_LOCKDOWN_MODE">
            <summary>
            Sets the entity lockdown mode for a specific routing bucket.
            
            Lockdown modes are:
            
            | Mode       | Meaning                                                    |
            | ---------- | ---------------------------------------------------------- |
            | `strict`   | No entities can be created by clients at all.              |
            | `relaxed`  | Only script-owned entities created by clients are blocked. |
            | `inactive` | Clients can create any entity they want.                   |
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_ROUTING_BUCKET_POPULATION_ENABLED">
            <summary>
            Sets whether or not the specified routing bucket has automatically-created population enabled.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_STATE_BAG_VALUE">
            <summary>
            Internal function for setting a state bag value.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_VEHICLE_ALARM">
            <summary>
            SET_VEHICLE_ALARM
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_VEHICLE_BODY_HEALTH">
            <summary>
            p2 often set to 1000.0 in the decompiled scripts.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_VEHICLE_COLOUR_COMBINATION">
            <summary>
            Sets the selected vehicle's colors to their default value (specific variant specified using the colorCombination parameter).
            
            Range of possible values for colorCombination is currently unknown, I couldn't find where these values are stored either (Disquse's guess was vehicles.meta but I haven't seen it in there.)
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_VEHICLE_COLOURS">
            <summary>
            colorPrimary &amp; colorSecondary are the paint indexes for the vehicle.
            
            For a list of valid paint indexes, view: pastebin.com/pwHci0xK
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_VEHICLE_CUSTOM_PRIMARY_COLOUR">
            <summary>
            p1, p2, p3 are RGB values for color (255,0,0 for Red, ect)
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_VEHICLE_CUSTOM_SECONDARY_COLOUR">
            <summary>
            p1, p2, p3 are RGB values for color (255,0,0 for Red, ect)
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_VEHICLE_DIRT_LEVEL">
            <summary>
            Sets the dirt level of the passed vehicle.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_VEHICLE_DOOR_BROKEN">
            <summary>
            See eDoorId declared in [`SET_VEHICLE_DOOR_SHUT`](#\_0x93D9BD300D7789E5)
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_VEHICLE_DOORS_LOCKED">
            <summary>
            Locks the doors of a specified vehicle to a defined lock state, affecting how players and NPCs can interact with the vehicle.
            
            ```
            NativeDB Introduced: v323
            ```
            
            ```cpp
            enum eVehicleLockState {
                // No specific lock state, vehicle behaves according to the game's default settings.
                VEHICLELOCK_NONE = 0,
                // Vehicle is fully unlocked, allowing free entry by players and NPCs.
                VEHICLELOCK_UNLOCKED = 1,
                // Vehicle is locked, preventing entry by players and NPCs.
                VEHICLELOCK_LOCKED = 2,
                // Vehicle locks out only players, allowing NPCs to enter.
                VEHICLELOCK_LOCKOUT_PLAYER_ONLY = 3,
                // Vehicle is locked once a player enters, preventing others from entering.
                VEHICLELOCK_LOCKED_PLAYER_INSIDE = 4,
                // Vehicle starts in a locked state, but may be unlocked through game events.
                VEHICLELOCK_LOCKED_INITIALLY = 5,
                // Forces the vehicle's doors to shut and lock.
                VEHICLELOCK_FORCE_SHUT_DOORS = 6,
                // Vehicle is locked but can still be damaged.
                VEHICLELOCK_LOCKED_BUT_CAN_BE_DAMAGED = 7,
                // Vehicle is locked, but its trunk/boot remains unlocked.
                VEHICLELOCK_LOCKED_BUT_BOOT_UNLOCKED = 8,
                // Vehicle is locked and does not allow passengers, except for the driver.
                VEHICLELOCK_LOCKED_NO_PASSENGERS = 9,
                // Vehicle is completely locked, preventing entry entirely, even if previously inside.
                VEHICLELOCK_CANNOT_ENTER = 10 
            };
            
            ```
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_VEHICLE_NUMBER_PLATE_TEXT">
            <summary>
            SET_VEHICLE_NUMBER_PLATE_TEXT
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.START_FIND_KVP">
            <summary>
            START_FIND_KVP
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.START_RESOURCE">
            <summary>
            START_RESOURCE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.STATE_BAG_HAS_KEY">
            <summary>
            STATE_BAG_HAS_KEY
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.STOP_RESOURCE">
            <summary>
            STOP_RESOURCE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TASK_COMBAT_PED">
            <summary>
            Makes the specified ped attack the target ped.  
            p2 should be 0  
            p3 should be 16
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TASK_DRIVE_BY">
            <summary>
            Example:
            TASK::TASK_DRIVE_BY(l_467[1/*22*/], PLAYER::PLAYER_PED_ID(), 0, 0.0, 0.0, 2.0, 300.0, 100, 0, ${firing_pattern_burst_fire_driveby});
            Needs working example. Doesn't seem to do anything.
            I marked p2 as targetVehicle as all these shooting related tasks seem to have that in common.
            I marked p6 as distanceToShoot as if you think of GTA's Logic with the native SET_VEHICLE_SHOOT natives, it won't shoot till it gets within a certain distance of the target.
            I marked p7 as pedAccuracy as it seems it's mostly 100 (Completely Accurate), 75, 90, etc. Although this could be the ammo count within the gun, but I highly doubt it. I will change this comment once I find out if it's ammo count or not.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TASK_ENTER_VEHICLE">
            <summary>
            speed 1.0 = walk, 2.0 = run  
            p5 1 = normal, 3 = teleport to vehicle, 8 = normal/carjack ped from seat, 16 = teleport directly into vehicle  
            p6 is always 0
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TASK_EVERYONE_LEAVE_VEHICLE">
            <summary>
            TASK_EVERYONE_LEAVE_VEHICLE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TASK_GO_STRAIGHT_TO_COORD">
            <summary>
            TASK_GO_STRAIGHT_TO_COORD
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TASK_GO_TO_COORD_ANY_MEANS">
            <summary>
            Tells a ped to go to a coord by any means.
            
            ```cpp
            enum eDrivingMode {
              DF_StopForCars = 1,
              DF_StopForPeds = 2,
              DF_SwerveAroundAllCars = 4,
              DF_SteerAroundStationaryCars = 8,
              DF_SteerAroundPeds = 16,
              DF_SteerAroundObjects = 32,
              DF_DontSteerAroundPlayerPed = 64,
              DF_StopAtLights = 128,
              DF_GoOffRoadWhenAvoiding = 256,
              DF_DriveIntoOncomingTraffic = 512,
              DF_DriveInReverse = 1024,
            
              // If pathfinding fails, cruise randomly instead of going on a straight line
              DF_UseWanderFallbackInsteadOfStraightLine = 2048,
            
              DF_AvoidRestrictedAreas = 4096,
            
              // These only work on MISSION_CRUISE
              DF_PreventBackgroundPathfinding = 8192,
              DF_AdjustCruiseSpeedBasedOnRoadSpeed = 16384,
            
              DF_UseShortCutLinks =  262144,
              DF_ChangeLanesAroundObstructions = 524288,
              // cruise tasks ignore this anyway--only used for goto's
              DF_UseSwitchedOffNodes =  2097152,
              // if you're going to be primarily driving off road
              DF_PreferNavmeshRoute =  4194304,
            
              // Only works for planes using MISSION_GOTO, will cause them to drive along the ground instead of fly
              DF_PlaneTaxiMode =  8388608,
            
              DF_ForceStraightLine = 16777216,
              DF_UseStringPullingAtJunctions = 33554432,
            
              DF_AvoidHighways = *********,
              DF_ForceJoinInRoadDirection = 1073741824,
            
              // Standard driving mode. stops for cars, peds, and lights, goes around stationary obstructions
              DRIVINGMODE_STOPFORCARS = 786603, // DF_StopForCars|DF_StopForPeds|DF_SteerAroundObjects|DF_SteerAroundStationaryCars|DF_StopAtLights|DF_UseShortCutLinks|DF_ChangeLanesAroundObstructions,		// Obey lights too
            
              // Like the above, but doesn't steer around anything in its way - will only wait instead.
              DRIVINGMODE_STOPFORCARS_STRICT = 262275, // DF_StopForCars|DF_StopForPeds|DF_StopAtLights|DF_UseShortCutLinks, // Doesn't deviate an inch.
            
              // Default "alerted" driving mode. drives around everything, doesn't obey lights
              DRIVINGMODE_AVOIDCARS = 786469, // DF_SwerveAroundAllCars|DF_SteerAroundObjects|DF_UseShortCutLinks|DF_ChangeLanesAroundObstructions|DF_StopForCars,
            
              // Very erratic driving. difference between this and AvoidCars is that it doesn't use the brakes at ALL to help with steering
              DRIVINGMODE_AVOIDCARS_RECKLESS = 786468, // DF_SwerveAroundAllCars|DF_SteerAroundObjects|DF_UseShortCutLinks|DF_ChangeLanesAroundObstructions,
            
              // Smashes through everything
              DRIVINGMODE_PLOUGHTHROUGH = 262144, // DF_UseShortCutLinks
            
              // Drives normally except for the fact that it ignores lights
              DRIVINGMODE_STOPFORCARS_IGNORELIGHTS = 786475, // DF_StopForCars|DF_SteerAroundStationaryCars|DF_StopForPeds|DF_SteerAroundObjects|DF_UseShortCutLinks|DF_ChangeLanesAroundObstructions
            
              // Try to swerve around everything, but stop for lights if necessary
              DRIVINGMODE_AVOIDCARS_OBEYLIGHTS = 786597, // DF_SwerveAroundAllCars|DF_StopAtLights|DF_SteerAroundObjects|DF_UseShortCutLinks|DF_ChangeLanesAroundObstructions|DF_StopForCars
            
              // Swerve around cars, be careful around peds, and stop for lights
              DRIVINGMODE_AVOIDCARS_STOPFORPEDS_OBEYLIGHTS = 786599 // DF_SwerveAroundAllCars|DF_StopAtLights|DF_StopForPeds|DF_SteerAroundObjects|DF_UseShortCutLinks|DF_ChangeLanesAroundObstructions|DF_StopForCars
            };
            ```
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TASK_GO_TO_ENTITY">
            <summary>
            The entity will move towards the target until time is over (duration) or get in target's range (distance). p5 and p6 are unknown, but you could leave p5 = 1073741824 or 100 or even 0 (didn't see any difference but on the decompiled scripts, they use 1073741824 mostly) and p6 = 0
            Note: I've only tested it on entity -&gt; ped and target -&gt; vehicle. It could work differently on other entities, didn't try it yet.
            Example: TASK::TASK_GO_TO_ENTITY(pedHandle, vehicleHandle, 5000, 4.0, 100, 1073741824, 0)
            Ped will run towards the vehicle for 5 seconds and stop when time is over or when he gets 4 meters(?) around the vehicle (with duration = -1, the task duration will be ignored).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TASK_HANDS_UP">
            <summary>
            In the scripts, p3 was always -1.  
            p3 seems to be duration or timeout of turn animation.  
            Also facingPed can be 0 or -1 so ped will just raise hands up.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TASK_LEAVE_ANY_VEHICLE">
            <summary>
            Flags are the same flags used in [`TASK_LEAVE_VEHICLE`](#\_0xD3DBCE61A490BE02)
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TASK_LEAVE_VEHICLE">
            <summary>
            Flags from decompiled scripts:  
            0 = normal exit and closes door.  
            1 = normal exit and closes door.  
            16 = teleports outside, door kept closed.  (This flag does not seem to work for the front seats in buses, NPCs continue to exit normally)
            64 = normal exit and closes door, maybe a bit slower animation than 0.  
            256 = normal exit but does not close the door.  
            4160 = ped is throwing himself out, even when the vehicle is still.  
            262144 = ped moves to passenger seat first, then exits normally  
            Others to be tried out: 320, 512, 131072.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TASK_PLAY_ANIM">
            <summary>
            [Animations list](https://alexguirre.github.io/animations-list/)
            
            ```cpp
            enum eScriptedAnimFlags
            {
                AF_LOOPING = 1,
                AF_HOLD_LAST_FRAME = 2,
                AF_REPOSITION_WHEN_FINISHED = 4,
                AF_NOT_INTERRUPTABLE = 8,
                AF_UPPERBODY = 16,
                AF_SECONDARY = 32,
                AF_REORIENT_WHEN_FINISHED = 64,
                AF_ABORT_ON_PED_MOVEMENT = 128,
                AF_ADDITIVE = 256,
                AF_TURN_OFF_COLLISION = 512,
                AF_OVERRIDE_PHYSICS = 1024,
                AF_IGNORE_GRAVITY = 2048,
                AF_EXTRACT_INITIAL_OFFSET = 4096,
                AF_EXIT_AFTER_INTERRUPTED = 8192,
                AF_TAG_SYNC_IN = 16384,
                AF_TAG_SYNC_OUT = 32768,
                AF_TAG_SYNC_CONTINUOUS = 65536,
                AF_FORCE_START = 131072,
                AF_USE_KINEMATIC_PHYSICS = 262144,
                AF_USE_MOVER_EXTRACTION = 524288,
                AF_HIDE_WEAPON = 1048576,
                AF_ENDS_IN_DEAD_POSE = 2097152,
                AF_ACTIVATE_RAGDOLL_ON_COLLISION = 4194304,
                AF_DONT_EXIT_ON_DEATH = 8388608,
                AF_ABORT_ON_WEAPON_DAMAGE = 16777216,
                AF_DISABLE_FORCED_PHYSICS_UPDATE = 33554432,
                AF_PROCESS_ATTACHMENTS_ON_START = 67108864,
                AF_EXPAND_PED_CAPSULE_FROM_SKELETON = 134217728,
                AF_USE_ALTERNATIVE_FP_ANIM = 268435456,
                AF_BLENDOUT_WRT_LAST_FRAME = *********,
                AF_USE_FULL_BLENDING = 1073741824
            }
            ```
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TASK_PLAY_ANIM_ADVANCED">
            <summary>
            Similar in functionality to [`TASK_PLAY_ANIM`](#\_0xEA47FE3719165B94), except the position and rotation parameters let you specify the initial position and rotation of the task. The ped is teleported to the position specified.
            
            [Animations list](https://alexguirre.github.io/animations-list/)
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TASK_REACT_AND_FLEE_PED">
            <summary>
            TASK_REACT_AND_FLEE_PED
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TASK_SHOOT_AT_COORD">
            <summary>
            Firing Pattern Hash Information: https://pastebin.com/Px036isB
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TASK_SHOOT_AT_ENTITY">
            <summary>
            //this part of the code is to determine at which entity the player is aiming, for example if you want to create a mod where you give orders to peds
            Entity aimedentity;
            Player player = PLAYER::PLAYER_ID();
            PLAYER::_GET_AIMED_ENTITY(player, &amp;aimedentity);
            //bg is an array of peds
            TASK::TASK_SHOOT_AT_ENTITY(bg[i], aimedentity, 5000, MISC::GET_HASH_KEY("FIRING_PATTERN_FULL_AUTO"));
            in practical usage, getting the entity the player is aiming at and then task the peds to shoot at the entity, at a button press event would be better.
            Firing Pattern Hash Information: https://pastebin.com/Px036isB
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TASK_WARP_PED_INTO_VEHICLE">
            <summary>
            ```
            NativeDB Introduced: v323
            ```
            
            Warp a ped into a vehicle.
            
            **Note**: It's better to use [`TASK_ENTER_VEHICLE`](#\_0xC20E50AA46D09CA8) with the flag "warp" flag instead of this native.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TEMP_BAN_PLAYER">
            <summary>
            TEMP_BAN_PLAYER
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TRIGGER_CLIENT_EVENT_INTERNAL">
            <summary>
            The backing function for TriggerClientEvent.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TRIGGER_EVENT_INTERNAL">
            <summary>
            The backing function for TriggerEvent.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TRIGGER_LATENT_CLIENT_EVENT_INTERNAL">
            <summary>
            The backing function for TriggerLatentClientEvent.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.VERIFY_PASSWORD_HASH">
            <summary>
            VERIFY_PASSWORD_HASH
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.WAS_EVENT_CANCELED">
            <summary>
            Returns whether or not the currently executing event was canceled.
            </summary>
        </member>
        <member name="P:CitizenFX.Server.Entity.Position">
            <summary>
            Gets or sets the position of this <see cref="T:CitizenFX.Server.Entity"/>.
            </summary>
            <value>
            The position in world space.
            </value>
        </member>
        <member name="P:CitizenFX.Server.Entity.Rotation">
            <summary>
            Gets or sets the rotation of this <see cref="T:CitizenFX.Server.Entity"/>.
            </summary>
            <value>
            The yaw, pitch, roll rotation values.
            </value>
        </member>
        <member name="P:CitizenFX.Server.Entity.Heading">
            <summary>
            Gets or sets the heading of this <see cref="T:CitizenFX.Server.Entity"/>.
            </summary>
            <value>
            The heading in degrees.
            </value>
        </member>
        <member name="P:CitizenFX.Server.Entity.IsPositionFrozen">
            <summary>
            Sets a value indicating whether this <see cref="T:CitizenFX.Server.Entity"/> should be frozen.
            </summary>
            <value>
            <c>true</c> if this <see cref="T:CitizenFX.Server.Entity"/> position should be frozen; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:CitizenFX.Server.Entity.Velocity">
            <summary>
            Gets or sets the velocity of this <see cref="T:CitizenFX.Server.Entity"/>.
            </summary>
        </member>
        <member name="P:CitizenFX.Server.Entity.RotationVelocity">
            <summary>
            Gets the rotation velocity of this <see cref="T:CitizenFX.Server.Entity"/>.
            </summary>
        </member>
        <member name="P:CitizenFX.Server.Entity.Model">
            <summary>
            Gets the model of the this <see cref="T:CitizenFX.Server.Entity"/>.
            </summary>
        </member>
        <member name="P:CitizenFX.Server.Entity.Owner">
            <summary>
            Gets the network owner of the this <see cref="T:CitizenFX.Server.Entity"/>.
            </summary>
            <returns>Returns the <see cref="T:CitizenFX.Server.Player"/> of the network owner.
            Returns <c>null</c> if this <see cref="T:CitizenFX.Server.Entity"/> is in an unowned state.</returns>
        </member>
        <member name="P:CitizenFX.Server.Entity.NetworkId">
            <summary>
            Gets the network ID of the this <see cref="T:CitizenFX.Server.Entity"/>.
            </summary>
        </member>
        <member name="P:CitizenFX.Server.Entity.Type">
            <summary>
            Gets the type of this <see cref="T:CitizenFX.Server.Entity"/>.
            </summary>
            <returns>Returns 1 if this <see cref="T:CitizenFX.Server.Entity"/> is a Ped.
            Returns 2 if this <see cref="T:CitizenFX.Server.Entity"/> is a Vehicle.
            Returns 3 if this <see cref="T:CitizenFX.Server.Entity"/> is a Prop.</returns>
        </member>
        <member name="P:CitizenFX.Server.Entity.State">
            <summary>
            Gets the <see cref="T:CitizenFX.Core.StateBag"/> of this <see cref="T:CitizenFX.Server.Entity"/>
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Entity.FromHandle(System.Int32)">
            <summary>
            Creates a new instance of an <see cref="T:CitizenFX.Server.Entity"/> from the given handle.
            </summary>
            <param name="handle">The entity handle.</param>
            <returns>Returns a <see cref="T:CitizenFX.Server.Ped"/> if this handle corresponds to a Ped.
            Returns a <see cref="T:CitizenFX.Server.Vehicle"/> if this handle corresponds to a Vehicle.
            Returns a <see cref="T:CitizenFX.Server.Prop"/> if this handle corresponds to a Prop.
            Returns <c>null</c> if no <see cref="T:CitizenFX.Server.Entity"/> exists this the specified <paramref name="handle"/></returns>
        </member>
        <member name="M:CitizenFX.Server.Entity.FromNetworkId(System.Int32)">
            <summary>
            Creates a new instance of an <see cref="T:CitizenFX.Server.Entity"/> from the given network ID.
            </summary>
            <param name="networkId">The entity network ID.</param>
            <returns>Returns a <see cref="T:CitizenFX.Server.Ped"/> if this network ID corresponds to a Ped.
            Returns a <see cref="T:CitizenFX.Server.Vehicle"/> if this network ID corresponds to a Vehicle.
            Returns a <see cref="T:CitizenFX.Server.Prop"/> if this network ID corresponds to a Prop.
            Returns <c>null</c> if no <see cref="T:CitizenFX.Server.Entity"/> exists this the specified <paramref name="networkId"/></returns>
        </member>
        <member name="M:CitizenFX.Server.Entity.Equals(CitizenFX.Server.Entity)">
            <summary>
            Checks if two <see cref="T:CitizenFX.Server.Entity"/>s refer to the same <see cref="T:CitizenFX.Server.Entity"/>
            </summary>
            <param name="entity">The other <see cref="T:CitizenFX.Server.Entity"/>.</param>
            <returns><c>true</c> if they are the same <see cref="T:CitizenFX.Server.Entity"/>; otherwise, false</returns>
        </member>
        <member name="M:CitizenFX.Server.Ped.FromPlayerHandle(System.String)">
            <summary>
            Creates a new instance of an <see cref="T:CitizenFX.Server.Ped"/> from the given player handle.
            </summary>
            <param name="handle">The players handle.</param>
            <returns>Returns the <see cref="T:CitizenFX.Server.Ped"/> of the player.
            Returns <c>null</c> if no <see cref="T:CitizenFX.Server.Ped"/> exists for the specified player</returns>
        </member>
        <member name="M:CitizenFX.Server.Player.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:CitizenFX.Server.Player.GetHashCode">
            <inheritdoc />
        </member>
        <member name="P:CitizenFX.Server.IdentifierCollection.Item(System.String)">
            <summary>
            Gets the identifier value of a particular type.
            </summary>
            <example>
            string steamId = player.Identifiers["steam"];
            </example>
            <param name="type">The identifier type to return.</param>
            <returns>The identifier value (without prefix), or null if it could not be found.</returns>
        </member>
    </members>
</doc>
