[{"n": "G:/[BACKUP B13]/TESTES/resources//[scripts]/showme/@vrp/config/Native.lua", "mt": 0, "s": 18446744073709551615, "i": "AAAAAAAAAAAAAAAAAAAAAA=="}, {"n": "G:/[BACKUP B13]/TESTES/resources//[scripts]/showme/@vrp/lib/Utils.lua", "mt": 0, "s": 18446744073709551615, "i": "AAAAAAAAAAAAAAAAAAAAAA=="}, {"n": "G:/[BACKUP B13]/TESTES/resources//[scripts]/showme/client-side/core.lua", "mt": 1686344099, "s": 3217, "i": "NxUCAAAAAgAAAAAAAAAAAA=="}, {"n": "G:/[BACKUP B13]/TESTES/resources//[scripts]/showme/fxmanifest.lua", "mt": 1686344099, "s": 207, "i": "NRUCAAAAAgAAAAAAAAAAAA=="}, {"n": "G:/[BACKUP B13]/TESTES/resources//[scripts]/showme/web-side/images/1.png", "mt": 1686344099, "s": 4869, "i": "RBUCAAAAAgAAAAAAAAAAAA=="}, {"n": "G:/[BACKUP B13]/TESTES/resources//[scripts]/showme/web-side/images/2.png", "mt": 1686344099, "s": 5184, "i": "RRUCAAAAAgAAAAAAAAAAAA=="}, {"n": "G:/[BACKUP B13]/TESTES/resources//[scripts]/showme/web-side/images/3.png", "mt": 1686344099, "s": 5953, "i": "RxUCAAAAAgAAAAAAAAAAAA=="}, {"n": "G:/[BACKUP B13]/TESTES/resources//[scripts]/showme/web-side/images/4.png", "mt": 1686344099, "s": 6307, "i": "SBUCAAAAAgAAAAAAAAAAAA=="}, {"n": "G:/[BACKUP B13]/TESTES/resources//[scripts]/showme/web-side/images/5.png", "mt": 1686344099, "s": 6832, "i": "ShUCAAAAAgAAAAAAAAAAAA=="}, {"n": "G:/[BACKUP B13]/TESTES/resources//[scripts]/showme/web-side/images/6.png", "mt": 1686344099, "s": 7069, "i": "TBUCAAAAAgAAAAAAAAAAAA=="}, {"n": "G:/[BACKUP B13]/TESTES/resources//[scripts]/showme/web-side/index.html", "mt": 1686344099, "s": 353, "i": "ORUCAAAAAgAAAAAAAAAAAA=="}, {"n": "G:/[BACKUP B13]/TESTES/resources//[scripts]/showme/web-side/main.css", "mt": 1686344099, "s": 378, "i": "OxUCAAAAAgAAAAAAAAAAAA=="}, {"n": "G:/[BACKUP B13]/TESTES/resources//[scripts]/showme/web-side/script.js", "mt": 1686344099, "s": 1014, "i": "PhUCAAAAAgAAAAAAAAAAAA=="}]