@import url("https://fonts.googleapis.com/css2?family=Roboto&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Jura:wght@600&display=swap");

html,body {
	margin: 0;
	padding: 0;
	color: #fff;
	cursor: default;
	font-size: 11px;
	overflow: hidden;
	font-family: "Roboto";
	box-sizing: border-box;
	background: transparent;
}

:focus { outline: 0; }
u { text-decoration: none; }
::-webkit-scrollbar { width: 0; }
::-webkit-scrollbar-track { background: transparent; }
::-webkit-scrollbar-thumb { background: transparent; }
::-webkit-scrollbar-thumb:hover { background: transparent; }
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
	-webkit-appearance: none;
	margin: 0;
}

#Doors {
	right: 50px;
	bottom: 170px;
	display: none;
	line-height: 16px;
	position: absolute;
	padding: 15px 20px 13px 15px;
	text-shadow: 1px 1px #232630;
	background: rgba(15,15,15,.75);
}

#Doors::before {
	top: 20%;
	left: -2px;
	width: 4px;
	height: 60%;
	bottom: 20%;
	content: "";
	position: absolute;
	background: #fec026;
}

#Doors > .Button {
	float: left;
	color: #ccc;
	font-size: 35px;
	text-shadow: none;
	text-align: center;
	font-family: "Jura";
	padding: 6px 14px 0 0;
}

#Doors > .Text {
	float: left;
	color: #666666;
	font-size: 11px;
	letter-spacing: 1px;
}

#Doors > .Text > b {
	color: #c0c0c0;
	display: block;
	font-size: 12px;
	font-weight: 300;
}