["net:base", "net:packet", "net:tcp-server", "net:http-server", "citizen:server:net", "citizen:scripting:core", "citizen:scripting:lua", "conhost:server", "citizen:server:main", "citizen:server:instance", "citizen:server:monitor", "citizen:server:impl", "citizen:server:state:fivesv", "citizen:server:state:rdr3sv", "citizen:server:fxdk", "citizen:dev<PERSON><PERSON>", "citizen:resources:core", "citizen:resources:metadata:lua", "vfs:core", "vfs:impl:server", "scripting:server", "s<PERSON><PERSON><PERSON><PERSON>", "citizen:scripting:mono", "citizen:scripting:mono-v2", "citizen:scripting:v8node", "citizen:scripting:node", "voip-server:mumble", "http-client", "pool-sizes-state", "citizen:server:gui"]