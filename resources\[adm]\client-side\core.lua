-----------------------------------------------------------------------------------------------------------------------------------------
-- VRP
-----------------------------------------------------------------------------------------------------------------------------------------
local Tunnel = module("vrp","lib/Tunnel")
local Proxy = module("vrp","lib/Proxy")
vRP = Proxy.getInterface("vRP")
-----------------------------------------------------------------------------------------------------------------------------------------
-- CONNECTION
-----------------------------------------------------------------------------------------------------------------------------------------
vSERVER = Tunnel.getInterface("admin")
-----------------------------------------------------------------------------------------------------------------------------------------
-- VARIABLES
-----------------------------------------------------------------------------------------------------------------------------------------
local adminOpen = false
local adminData = {}
-----------------------------------------------------------------------------------------------------------------------------------------
-- ADMIN:TOGGLE
-----------------------------------------------------------------------------------------------------------------------------------------
RegisterNetEvent("admin:Toggle")
AddEventHandler("admin:Toggle", function(data)
    adminOpen = not adminOpen
    adminData = data or {}
    
    SetNuiFocus(adminOpen, adminOpen)
    SendNUIMessage({
        action = "toggle",
        status = adminOpen,
        data = adminData
    })
end)
-----------------------------------------------------------------------------------------------------------------------------------------
-- ADMIN:UPDATE
-----------------------------------------------------------------------------------------------------------------------------------------
RegisterNetEvent("admin:Update")
AddEventHandler("admin:Update", function(data)
    adminData = data
    SendNUIMessage({
        action = "update",
        data = adminData
    })
end)
-----------------------------------------------------------------------------------------------------------------------------------------
-- NUI CALLBACKS
-----------------------------------------------------------------------------------------------------------------------------------------
RegisterNUICallback("close", function(data, cb)
    adminOpen = false
    SetNuiFocus(false, false)
    cb("ok")
end)

RegisterNUICallback("getPlayers", function(data, cb)
    local result = vSERVER.getPlayers()
    cb(result)
end)

RegisterNUICallback("teleportToPlayer", function(data, cb)
    vSERVER.teleportToPlayer(data.id)
    cb("ok")
end)

RegisterNUICallback("teleportPlayerToMe", function(data, cb)
    vSERVER.teleportPlayerToMe(data.id)
    cb("ok")
end)

RegisterNUICallback("kickPlayer", function(data, cb)
    vSERVER.kickPlayer(data.id, data.reason)
    cb("ok")
end)

RegisterNUICallback("banPlayer", function(data, cb)
    vSERVER.banPlayer(data.id, data.time, data.reason)
    cb("ok")
end)

RegisterNUICallback("giveItem", function(data, cb)
    vSERVER.giveItem(data.id, data.item, data.amount)
    cb("ok")
end)

RegisterNUICallback("giveMoney", function(data, cb)
    vSERVER.giveMoney(data.id, data.amount)
    cb("ok")
end)

RegisterNUICallback("setGroup", function(data, cb)
    vSERVER.setGroup(data.id, data.group, data.level)
    cb("ok")
end)

RegisterNUICallback("removeGroup", function(data, cb)
    vSERVER.removeGroup(data.id, data.group)
    cb("ok")
end)

RegisterNUICallback("revivePlayer", function(data, cb)
    vSERVER.revivePlayer(data.id)
    cb("ok")
end)

RegisterNUICallback("sendAnnouncement", function(data, cb)
    vSERVER.sendAnnouncement(data.message, data.time)
    cb("ok")
end)

RegisterNUICallback("sendWarning", function(data, cb)
    vSERVER.sendWarning(data.id, data.message)
    cb("ok")
end)

RegisterNUICallback("clearInventory", function(data, cb)
    vSERVER.clearInventory(data.id)
    cb("ok")
end)

RegisterNUICallback("spectatePlayer", function(data, cb)
    vSERVER.spectatePlayer(data.id)
    cb("ok")
end)

RegisterNUICallback("stopSpectate", function(data, cb)
    vSERVER.stopSpectate()
    cb("ok")
end)

RegisterNUICallback("freezePlayer", function(data, cb)
    vSERVER.freezePlayer(data.id, data.freeze)
    cb("ok")
end)

RegisterNUICallback("getVehicles", function(data, cb)
    local result = vSERVER.getVehicles()
    cb(result)
end)

RegisterNUICallback("spawnVehicle", function(data, cb)
    vSERVER.spawnVehicle(data.vehicle)
    cb("ok")
end)

RegisterNUICallback("deleteVehicle", function(data, cb)
    vSERVER.deleteVehicle()
    cb("ok")
end)

RegisterNUICallback("repairVehicle", function(data, cb)
    vSERVER.repairVehicle()
    cb("ok")
end)

RegisterNUICallback("getChests", function(data, cb)
    local result = vSERVER.getChests()
    cb(result)
end)

RegisterNUICallback("openChest", function(data, cb)
    vSERVER.openChest(data.name)
    cb("ok")
end)

RegisterNUICallback("getWarnings", function(data, cb)
    local result = vSERVER.getWarnings()
    cb(result)
end)

RegisterNUICallback("deleteWarning", function(data, cb)
    vSERVER.deleteWarning(data.id)
    cb("ok")
end)
-----------------------------------------------------------------------------------------------------------------------------------------
-- KEYBIND
-----------------------------------------------------------------------------------------------------------------------------------------
RegisterKeyMapping("adminpanel", "Abrir Painel Admin", "keyboard", "F10")

RegisterCommand("adminpanel", function()
    vSERVER.checkPermission()
end)
-----------------------------------------------------------------------------------------------------------------------------------------
-- ADMIN:SPECTATE
-----------------------------------------------------------------------------------------------------------------------------------------
local spectating = false
local spectateTarget = nil

RegisterNetEvent("admin:StartSpectate")
AddEventHandler("admin:StartSpectate", function(target)
    spectating = true
    spectateTarget = target
    
    local targetPed = GetPlayerPed(GetPlayerFromServerId(target))
    if targetPed then
        NetworkSetInSpectatorMode(true, targetPed)
    end
end)

RegisterNetEvent("admin:StopSpectate")
AddEventHandler("admin:StopSpectate", function()
    spectating = false
    spectateTarget = nil
    NetworkSetInSpectatorMode(false, PlayerPedId())
end)
-----------------------------------------------------------------------------------------------------------------------------------------
-- ADMIN:FREEZE
-----------------------------------------------------------------------------------------------------------------------------------------
RegisterNetEvent("admin:Freeze")
AddEventHandler("admin:Freeze", function(freeze)
    FreezeEntityPosition(PlayerPedId(), freeze)
end)
