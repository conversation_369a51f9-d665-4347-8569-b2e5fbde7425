// Global Variables
let currentPlayers = [];
let currentVehicles = [];
let currentWarnings = [];
let currentChests = [];
let selectedPlayer = null;
let isSpectating = false;

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
    setupModalTabs();
});

// Setup Event Listeners
function setupEventListeners() {
    // Navigation
    document.querySelectorAll('.nav-item').forEach(item => {
        item.addEventListener('click', function() {
            const tab = this.dataset.tab;
            switchTab(tab);
        });
    });

    // Close panel on ESC
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closePanel();
        }
    });

    // Modal close
    document.querySelector('.modal').addEventListener('click', function(e) {
        if (e.target === this) {
            closePlayerModal();
        }
    });
}

// Setup Modal Tabs
function setupModalTabs() {
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const tab = this.dataset.modalTab;
            switchModalTab(tab);
        });
    });
}

// NUI Message Handler
window.addEventListener('message', function(event) {
    const data = event.data;
    
    switch(data.action) {
        case 'toggle':
            if (data.status) {
                openPanel(data.data);
            } else {
                closePanel();
            }
            break;
        case 'update':
            updateData(data.data);
            break;
    }
});

// Open Panel
function openPanel(data) {
    document.getElementById('painelPanel').classList.add('active');
    document.querySelector('.main-content').style.display = 'flex';
    
    if (data) {
        document.getElementById('adminName').textContent = data.name || 'Admin';
    }
    
    // Load initial data
    refreshPlayers();
    refreshVehicles();
    refreshWarnings();
    refreshChests();
    updateDashboard();
}

// Close Panel
function closePanel() {
    document.getElementById('painelPanel').classList.remove('active');
    closePlayerModal();
    
    fetch(`https://${GetParentResourceName()}/close`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({})
    });
}

// Switch Tab
function switchTab(tabName) {
    // Update navigation
    document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
    
    // Update content
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    document.getElementById(tabName).classList.add('active');
    
    // Load tab-specific data
    switch(tabName) {
        case 'players':
            refreshPlayers();
            break;
        case 'vehicles':
            refreshVehicles();
            break;
        case 'warnings':
            refreshWarnings();
            break;
        case 'chests':
            refreshChests();
            break;
        case 'dashboard':
            updateDashboard();
            break;
    }
}

// Switch Modal Tab
function switchModalTab(tabName) {
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-modal-tab="${tabName}"]`).classList.add('active');
    
    document.querySelectorAll('.modal-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    document.getElementById(tabName).classList.add('active');
}

// Update Dashboard
function updateDashboard() {
    document.getElementById('onlinePlayers').textContent = currentPlayers ? currentPlayers.length : 0;
    document.getElementById('totalVehicles').textContent = currentVehicles ? currentVehicles.length : 0;
    document.getElementById('totalWarnings').textContent = currentWarnings ? currentWarnings.length : 0;
    document.getElementById('totalChests').textContent = currentChests ? currentChests.length : 0;
}

// Refresh Players
async function refreshPlayers() {
    try {
        const response = await fetch(`https://${GetParentResourceName()}/getPlayers`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({})
        });
        
        const players = await response.json();
        currentPlayers = players || [];
        displayPlayers(currentPlayers);
        updateDashboard();
    } catch (error) {
        console.error('Error fetching players:', error);
    }
}

// Display Players
function displayPlayers(players) {
    const grid = document.getElementById('playersGrid');
    grid.innerHTML = '';
    
    if (players.length === 0) {
        grid.innerHTML = '<div class="text-center">Nenhum jogador online</div>';
        return;
    }
    
    players.forEach(player => {
        const card = document.createElement('div');
        card.className = 'player-card';
        card.onclick = () => openPlayerModal(player);
        
        card.innerHTML = `
            <div class="player-header">
                <div class="player-name">${player.name}</div>
                <div class="player-id">ID: ${player.id}</div>
            </div>
            <div class="player-info">
                <div class="player-info-item">
                    <span class="player-info-label">Telefone:</span>
                    <span class="player-info-value">${player.phone}</span>
                </div>
                <div class="player-info-item">
                    <span class="player-info-label">Banco:</span>
                    <span class="player-info-value">$${formatNumber(player.bank)}</span>
                </div>
                <div class="player-info-item">
                    <span class="player-info-label">Source:</span>
                    <span class="player-info-value">${player.source}</span>
                </div>
            </div>
        `;
        
        grid.appendChild(card);
    });
}

// Filter Players
function filterPlayers() {
    const search = document.getElementById('playerSearch').value.toLowerCase();
    const filtered = currentPlayers.filter(player => 
        player.name.toLowerCase().includes(search) ||
        player.id.toString().includes(search) ||
        player.phone.includes(search)
    );
    displayPlayers(filtered);
}

// Open Player Modal
function openPlayerModal(player) {
    selectedPlayer = player;
    
    document.getElementById('modalPlayerName').textContent = player.name;
    document.getElementById('modalPlayerId').textContent = player.id;
    document.getElementById('modalPlayerPhone').textContent = player.phone;
    document.getElementById('modalPlayerBank').textContent = formatNumber(player.bank);
    
    document.getElementById('playerModal').classList.add('active');
}

// Close Player Modal
function closePlayerModal() {
    document.getElementById('playerModal').classList.remove('active');
    selectedPlayer = null;
}

// Player Actions
async function teleportToPlayer() {
    if (!selectedPlayer) return;
    
    await sendAction('teleportToPlayer', { id: selectedPlayer.id });
    closePlayerModal();
}

async function teleportPlayerToMe() {
    if (!selectedPlayer) return;
    
    await sendAction('teleportPlayerToMe', { id: selectedPlayer.id });
    closePlayerModal();
}

async function kickPlayer() {
    if (!selectedPlayer) return;
    
    const reason = document.getElementById('kickReason').value || 'Sem motivo especificado';
    await sendAction('kickPlayer', { id: selectedPlayer.id, reason: reason });
    closePlayerModal();
    refreshPlayers();
}

async function banPlayer() {
    if (!selectedPlayer) return;
    
    const time = document.getElementById('banTime').value || 1;
    const reason = document.getElementById('banReason').value || 'Sem motivo especificado';
    
    await sendAction('banPlayer', { id: selectedPlayer.id, time: time, reason: reason });
    closePlayerModal();
    refreshPlayers();
}

async function giveItem() {
    if (!selectedPlayer) return;
    
    const item = document.getElementById('itemName').value;
    const amount = document.getElementById('itemAmount').value || 1;
    
    if (!item) {
        alert('Digite o nome do item');
        return;
    }
    
    await sendAction('giveItem', { id: selectedPlayer.id, item: item, amount: amount });
    
    // Clear inputs
    document.getElementById('itemName').value = '';
    document.getElementById('itemAmount').value = '1';
}

async function giveMoney() {
    if (!selectedPlayer) return;
    
    const amount = document.getElementById('moneyAmount').value;
    
    if (!amount || amount <= 0) {
        alert('Digite um valor válido');
        return;
    }
    
    await sendAction('giveMoney', { id: selectedPlayer.id, amount: amount });
    
    // Clear input
    document.getElementById('moneyAmount').value = '';
    refreshPlayers();
}

async function setGroup() {
    if (!selectedPlayer) return;
    
    const group = document.getElementById('groupSelect').value;
    const level = document.getElementById('groupLevel').value || 1;
    
    await sendAction('setGroup', { id: selectedPlayer.id, group: group, level: level });
}

async function removeGroup() {
    if (!selectedPlayer) return;
    
    const group = document.getElementById('removeGroupSelect').value;
    await sendAction('removeGroup', { id: selectedPlayer.id, group: group });
}

async function revivePlayer() {
    if (!selectedPlayer) return;
    
    await sendAction('revivePlayer', { id: selectedPlayer.id });
}

async function sendWarning() {
    if (!selectedPlayer) return;
    
    const message = document.getElementById('warningMessage').value;
    
    if (!message) {
        alert('Digite a mensagem da advertência');
        return;
    }
    
    await sendAction('sendWarning', { id: selectedPlayer.id, message: message });
    
    // Clear input
    document.getElementById('warningMessage').value = '';
    refreshWarnings();
}

async function clearInventory() {
    if (!selectedPlayer) return;
    
    if (confirm('Tem certeza que deseja limpar o inventário deste jogador?')) {
        await sendAction('clearInventory', { id: selectedPlayer.id });
    }
}

async function spectatePlayer() {
    if (!selectedPlayer) return;
    
    await sendAction('spectatePlayer', { id: selectedPlayer.id });
    isSpectating = true;
    closePlayerModal();
}

async function freezePlayer(freeze) {
    if (!selectedPlayer) return;

    await sendAction('freezePlayer', { id: selectedPlayer.id, freeze: freeze });
}

// Stop Spectate
async function stopSpectate() {
    if (isSpectating) {
        await sendAction('stopSpectate', {});
        isSpectating = false;
    }
}

// Vehicle Functions
async function refreshVehicles() {
    try {
        const response = await fetch(`https://${GetParentResourceName()}/getVehicles`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({})
        });

        const vehicles = await response.json();
        currentVehicles = vehicles || [];
        displayVehicles(currentVehicles);
        updateDashboard();
    } catch (error) {
        console.error('Error fetching vehicles:', error);
    }
}

function displayVehicles(vehicles) {
    const grid = document.getElementById('vehiclesGrid');
    grid.innerHTML = '';

    if (vehicles.length === 0) {
        grid.innerHTML = '<div class="text-center">Nenhum veículo disponível</div>';
        return;
    }

    vehicles.forEach(vehicle => {
        const item = document.createElement('div');
        item.className = 'vehicle-item';
        item.onclick = () => spawnVehicleFromList(vehicle.spawn);

        item.innerHTML = `
            <div class="vehicle-name">${vehicle.name}</div>
            <div class="vehicle-spawn">${vehicle.spawn}</div>
        `;

        grid.appendChild(item);
    });
}

function filterVehicles() {
    const search = document.getElementById('vehicleSearch').value.toLowerCase();
    const filtered = currentVehicles.filter(vehicle =>
        vehicle.name.toLowerCase().includes(search) ||
        vehicle.spawn.toLowerCase().includes(search)
    );
    displayVehicles(filtered);
}

async function spawnVehicle() {
    const vehicle = document.getElementById('vehicleSpawn').value;

    if (!vehicle) {
        alert('Digite o nome do veículo');
        return;
    }

    await sendAction('spawnVehicle', { vehicle: vehicle });

    // Clear input
    document.getElementById('vehicleSpawn').value = '';
}

async function spawnVehicleFromList(vehicle) {
    await sendAction('spawnVehicle', { vehicle: vehicle });
}

async function deleteVehicle() {
    if (confirm('Tem certeza que deseja deletar o veículo atual?')) {
        await sendAction('deleteVehicle', {});
    }
}

async function repairVehicle() {
    await sendAction('repairVehicle', {});
}

// Announcement Functions
async function sendAnnouncement() {
    const message = document.getElementById('announcementMessage').value;
    const time = document.getElementById('announcementTime').value || 10;

    if (!message) {
        alert('Digite a mensagem do anúncio');
        return;
    }

    await sendAction('sendAnnouncement', { message: message, time: time });

    // Clear inputs
    document.getElementById('announcementMessage').value = '';
    document.getElementById('announcementTime').value = '10';
}

// Warning Functions
async function refreshWarnings() {
    try {
        const response = await fetch(`https://${GetParentResourceName()}/getWarnings`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({})
        });

        const warnings = await response.json();
        currentWarnings = warnings || [];
        displayWarnings(currentWarnings);
        updateDashboard();
    } catch (error) {
        console.error('Error fetching warnings:', error);
    }
}

function displayWarnings(warnings) {
    const list = document.getElementById('warningsList');
    list.innerHTML = '';

    if (warnings.length === 0) {
        list.innerHTML = '<div class="text-center">Nenhuma advertência ativa</div>';
        return;
    }

    warnings.forEach(warning => {
        const item = document.createElement('div');
        item.className = 'warning-item';

        item.innerHTML = `
            <div class="warning-header">
                <div class="warning-info">
                    <span><strong>Admin:</strong> ${warning.admin}</span>
                    <span><strong>Jogador:</strong> ${warning.target}</span>
                    <span><strong>Data:</strong> ${warning.date}</span>
                </div>
                <button class="delete-warning-btn" onclick="deleteWarning(${warning.id})">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            <div class="warning-message">${warning.message}</div>
        `;

        list.appendChild(item);
    });
}

async function deleteWarning(id) {
    if (confirm('Tem certeza que deseja deletar esta advertência?')) {
        await sendAction('deleteWarning', { id: id });
        refreshWarnings();
    }
}

// Chest Functions
async function refreshChests() {
    try {
        const response = await fetch(`https://${GetParentResourceName()}/getChests`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({})
        });

        const chests = await response.json();
        currentChests = chests || [];
        displayChests(currentChests);
        updateDashboard();
    } catch (error) {
        console.error('Error fetching chests:', error);
    }
}

function displayChests(chests) {
    const grid = document.getElementById('chestsGrid');
    grid.innerHTML = '';

    if (chests.length === 0) {
        grid.innerHTML = '<div class="text-center">Nenhum baú disponível</div>';
        return;
    }

    chests.forEach(chest => {
        const card = document.createElement('div');
        card.className = 'chest-card';

        card.innerHTML = `
            <div class="chest-icon">
                <i class="fas fa-box"></i>
            </div>
            <div class="chest-name">${chest.name}</div>
            <div class="chest-info">
                <span>Peso: ${chest.weight}kg</span>
                <span>Perm: ${chest.perm}</span>
            </div>
            <button class="open-chest-btn" onclick="openChest('${chest.name}')">
                Abrir Baú
            </button>
        `;

        grid.appendChild(card);
    });
}

async function openChest(name) {
    await sendAction('openChest', { name: name });
}

// Utility Functions
async function sendAction(action, data) {
    try {
        const response = await fetch(`https://${GetParentResourceName()}/${action}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });

        return await response.json();
    } catch (error) {
        console.error(`Error sending action ${action}:`, error);
    }
}

function formatNumber(num) {
    return new Intl.NumberFormat('pt-BR').format(num);
}

function updateData(data) {
    if (data.players) {
        currentPlayers = data.players;
        displayPlayers(data.players);
    }

    if (data.vehicles) {
        currentVehicles = data.vehicles;
        displayVehicles(data.vehicles);
    }

    if (data.warnings) {
        currentWarnings = data.warnings;
        displayWarnings(data.warnings);
    }

    if (data.chests) {
        currentChests = data.chests;
        displayChests(data.chests);
    }

    updateDashboard();
}

// Get Parent Resource Name
function GetParentResourceName() {
    return window.location.hostname === 'nui-img' ? 'painel' : window.location.hostname;
}
