@import url("https://fonts.googleapis.com/css2?family=Roboto&display=swap");

html,body {
	margin: 0;
	padding: 0;
	color: #fff;
	cursor: default;
	font-size: 11px;
	overflow: hidden;
	font-family: "Roboto";
	box-sizing: border-box;
	background: transparent;
}

:focus { outline: 0; }
u { text-decoration: none; }
::-webkit-scrollbar { width: 0; }
::-webkit-scrollbar-track { background: transparent; }
::-webkit-scrollbar-thumb { background: transparent; }
::-webkit-scrollbar-thumb:hover { background: transparent; }
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
	-webkit-appearance: none;
	margin: 0;
}

#Notify {
	top: 50px;
	right: 400px;
	width: 500px;
	font-size: 13px;
	position: absolute;
	letter-spacing: .5px;
}

#Notify > div {
	float: left;
	color: #c6c6c6;
	margin-right: 5px;
	line-height: 20px;
	margin-bottom: 5px;
	text-shadow: 1px 1px #000;
	padding: 15px 20px 15px 45px;
}

#Notify > div > b {
	color: #fff;
	font-weight: 300;
}

#Notify-verde {
	background: rgba(15,15,15,.75) url("images/Check.png") no-repeat center left 11px;
}

#Notify-vermelho {
	background: rgba(15,15,15,.75) url("images/Cancel.png") no-repeat center left 11px;
}

#Notify-blood {
	background: rgba(15,15,15,.75) url("images/Blood.png") no-repeat center left 11px;
}

#Notify-hunger {
	background: rgba(15,15,15,.75) url("images/Hunger.png") no-repeat center left 11px;
}

#Notify-thirst {
	background: rgba(15,15,15,.75) url("images/Thirst.png") no-repeat center left 11px;
}

#Notify-azul {
	background: rgba(15,15,15,.75) url("images/Ring.png") no-repeat center left 11px;
}

#Notify-amarelo {
	background: rgba(15,15,15,.75) url("images/Important.png") no-repeat center left 11px;
}

#Notify-locked {
	background: rgba(15,15,15,.75) url("images/Locked.png") no-repeat center left 11px;
}

#Notify-unlocked {
	background: rgba(15,15,15,.75) url("images/Unlocked.png") no-repeat center left 11px;
}

#Notify-default {
	padding: 15px 20px !important;
	line-height: 20px !important;
	background: rgba(15,15,15,.75);
}