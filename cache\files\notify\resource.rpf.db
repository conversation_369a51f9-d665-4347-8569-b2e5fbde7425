[{"n": "G:/[BACKUP B13]/TESTES/resources//[scripts]/notify/client-side/coe.lua", "mt": 1686344099, "s": 454, "i": "phQCAAAAAgAAAAAAAAAAAA=="}, {"n": "G:/[BACKUP B13]/TESTES/resources//[scripts]/notify/fxmanifest.lua", "mt": 1686344099, "s": 157, "i": "pBQCAAAAAgAAAAAAAAAAAA=="}, {"n": "G:/[BACKUP B13]/TESTES/resources//[scripts]/notify/web-side/images/Blood.png", "mt": 1686344099, "s": 2392, "i": "rBQCAAAAAgAAAAAAAAAAAA=="}, {"n": "G:/[BACKUP B13]/TESTES/resources//[scripts]/notify/web-side/images/Cancel.png", "mt": 1686344099, "s": 284, "i": "rRQCAAAAAgAAAAAAAAAAAA=="}, {"n": "G:/[BACKUP B13]/TESTES/resources//[scripts]/notify/web-side/images/Check.png", "mt": 1686344099, "s": 271, "i": "rhQCAAAAAgAAAAAAAAAAAA=="}, {"n": "G:/[BACKUP B13]/TESTES/resources//[scripts]/notify/web-side/images/Hunger.png", "mt": 1686344099, "s": 3032, "i": "rxQCAAAAAgAAAAAAAAAAAA=="}, {"n": "G:/[BACKUP B13]/TESTES/resources//[scripts]/notify/web-side/images/Important.png", "mt": 1686344099, "s": 1983, "i": "sBQCAAAAAgAAAAAAAAAAAA=="}, {"n": "G:/[BACKUP B13]/TESTES/resources//[scripts]/notify/web-side/images/Locked.png", "mt": 1686344099, "s": 2179, "i": "sRQCAAAAAgAAAAAAAAAAAA=="}, {"n": "G:/[BACKUP B13]/TESTES/resources//[scripts]/notify/web-side/images/Ring.png", "mt": 1686344099, "s": 2094, "i": "shQCAAAAAgAAAAAAAAAAAA=="}, {"n": "G:/[BACKUP B13]/TESTES/resources//[scripts]/notify/web-side/images/Thirst.png", "mt": 1686344099, "s": 3023, "i": "sxQCAAAAAgAAAAAAAAAAAA=="}, {"n": "G:/[BACKUP B13]/TESTES/resources//[scripts]/notify/web-side/images/Unlocked.png", "mt": 1686344099, "s": 2181, "i": "tBQCAAAAAgAAAAAAAAAAAA=="}, {"n": "G:/[BACKUP B13]/TESTES/resources//[scripts]/notify/web-side/index.html", "mt": 1686344099, "s": 496, "i": "qBQCAAAAAgAAAAAAAAAAAA=="}, {"n": "G:/[BACKUP B13]/TESTES/resources//[scripts]/notify/web-side/jquery.js", "mt": 1686344099, "s": 330, "i": "qRQCAAAAAgAAAAAAAAAAAA=="}, {"n": "G:/[BACKUP B13]/TESTES/resources//[scripts]/notify/web-side/style.css", "mt": 1686344099, "s": 2035, "i": "qhQCAAAAAgAAAAAAAAAAAA=="}]