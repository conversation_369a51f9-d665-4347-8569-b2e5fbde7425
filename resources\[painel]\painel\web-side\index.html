<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Painel Administrativo - Creative Network</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div id="painelPanel" class="painel-panel">
        <!-- Header -->
        <div class="header">
            <div class="header-left">
                <img src="img/dashboard.png" alt="Logo" class="logo">
                <h1>Painel Administrativo</h1>
            </div>
            <div class="header-right">
                <span class="admin-info">
                    <i class="fas fa-user-shield"></i>
                    <span id="adminName">Admin</span>
                </span>
                <button class="close-btn" onclick="closePanel()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <!-- Main Layout -->
        <div class="main-layout">
            <!-- Sidebar -->
            <div class="sidebar">
            <nav class="nav-menu">
                <div class="nav-item active" data-tab="dashboard">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </div>
                <div class="nav-item" data-tab="players">
                    <i class="fas fa-users"></i>
                    <span>Jogadores</span>
                </div>
                <div class="nav-item" data-tab="vehicles">
                    <i class="fas fa-car"></i>
                    <span>Veículos</span>
                </div>
                <div class="nav-item" data-tab="announcements">
                    <i class="fas fa-bullhorn"></i>
                    <span>Anúncios</span>
                </div>
                <div class="nav-item" data-tab="warnings">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>Advertências</span>
                </div>
                <div class="nav-item" data-tab="chests">
                    <i class="fas fa-box"></i>
                    <span>Baús</span>
                </div>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Dashboard Tab -->
            <div id="dashboard" class="tab-content active">
                <div class="dashboard-header">
                    <h2>Dashboard</h2>
                    <p>Bem-vindo ao painel administrativo do Creative Network</p>
                </div>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="onlinePlayers">0</h3>
                            <p>Jogadores Online</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-car"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalVehicles">0</h3>
                            <p>Veículos Spawned</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalWarnings">0</h3>
                            <p>Advertências Ativas</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-box"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalChests">0</h3>
                            <p>Baús Disponíveis</p>
                        </div>
                    </div>
                </div>

                <div class="quick-actions">
                    <h3>Ações Rápidas</h3>
                    <div class="action-buttons">
                        <button class="action-btn" onclick="switchTab('players')">
                            <i class="fas fa-users"></i>
                            Gerenciar Jogadores
                        </button>
                        <button class="action-btn" onclick="switchTab('announcements')">
                            <i class="fas fa-bullhorn"></i>
                            Enviar Anúncio
                        </button>
                        <button class="action-btn" onclick="switchTab('vehicles')">
                            <i class="fas fa-car"></i>
                            Spawnar Veículo
                        </button>
                        <button class="action-btn" onclick="switchTab('chests')">
                            <i class="fas fa-box"></i>
                            Abrir Baús
                        </button>
                    </div>
                </div>
            </div>

            <!-- Players Tab -->
            <div id="players" class="tab-content">
                <div class="tab-header">
                    <h2>Gerenciamento de Jogadores</h2>
                    <button class="refresh-btn" onclick="refreshPlayers()">
                        <i class="fas fa-sync-alt"></i>
                        Atualizar
                    </button>
                </div>
                
                <div class="search-bar">
                    <input type="text" id="playerSearch" placeholder="Buscar jogador..." onkeyup="filterPlayers()">
                    <i class="fas fa-search"></i>
                </div>
                
                <div class="players-grid" id="playersGrid">
                    <!-- Players will be loaded here -->
                </div>
            </div>

            <!-- Vehicles Tab -->
            <div id="vehicles" class="tab-content">
                <div class="tab-header">
                    <h2>Gerenciamento de Veículos</h2>
                </div>
                
                <div class="vehicle-actions">
                    <div class="action-group">
                        <h3>Spawnar Veículo</h3>
                        <div class="input-group">
                            <input type="text" id="vehicleSpawn" placeholder="Nome do veículo (ex: adder)">
                            <button onclick="spawnVehicle()">Spawnar</button>
                        </div>
                    </div>
                    
                    <div class="action-group">
                        <h3>Ações do Veículo Atual</h3>
                        <div class="button-group">
                            <button onclick="repairVehicle()">Reparar</button>
                            <button onclick="deleteVehicle()" class="danger">Deletar</button>
                        </div>
                    </div>
                </div>
                
                <div class="vehicle-list">
                    <h3>Veículos Disponíveis</h3>
                    <div class="search-bar">
                        <input type="text" id="vehicleSearch" placeholder="Buscar veículo..." onkeyup="filterVehicles()">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="vehicles-grid" id="vehiclesGrid">
                        <!-- Vehicles will be loaded here -->
                    </div>
                </div>
            </div>

            <!-- Announcements Tab -->
            <div id="announcements" class="tab-content">
                <div class="tab-header">
                    <h2>Sistema de Anúncios</h2>
                </div>
                
                <div class="announcement-form">
                    <div class="form-group">
                        <label for="announcementMessage">Mensagem do Anúncio</label>
                        <textarea id="announcementMessage" placeholder="Digite sua mensagem aqui..." rows="4"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="announcementTime">Tempo de Exibição (segundos)</label>
                        <input type="number" id="announcementTime" value="10" min="1" max="60">
                    </div>
                    
                    <button class="send-btn" onclick="sendAnnouncement()">
                        <i class="fas fa-bullhorn"></i>
                        Enviar Anúncio
                    </button>
                </div>
            </div>

            <!-- Warnings Tab -->
            <div id="warnings" class="tab-content">
                <div class="tab-header">
                    <h2>Sistema de Advertências</h2>
                    <button class="refresh-btn" onclick="refreshWarnings()">
                        <i class="fas fa-sync-alt"></i>
                        Atualizar
                    </button>
                </div>
                
                <div class="warnings-list" id="warningsList">
                    <!-- Warnings will be loaded here -->
                </div>
            </div>

            <!-- Chests Tab -->
            <div id="chests" class="tab-content">
                <div class="tab-header">
                    <h2>Gerenciamento de Baús</h2>
                    <button class="refresh-btn" onclick="refreshChests()">
                        <i class="fas fa-sync-alt"></i>
                        Atualizar
                    </button>
                </div>
                
                <div class="chests-grid" id="chestsGrid">
                    <!-- Chests will be loaded here -->
                </div>
            </div>
        </div>
        </div>
    </div>

    <!-- Player Modal -->
    <div id="playerModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Gerenciar Jogador</h3>
                <span class="close" onclick="closePlayerModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="player-info">
                    <h4 id="modalPlayerName">Nome do Jogador</h4>
                    <p>ID: <span id="modalPlayerId"></span></p>
                    <p>Telefone: <span id="modalPlayerPhone"></span></p>
                    <p>Banco: $<span id="modalPlayerBank"></span></p>
                </div>
                
                <div class="action-tabs">
                    <div class="tab-buttons">
                        <button class="tab-btn active" data-modal-tab="teleport">Teleporte</button>
                        <button class="tab-btn" data-modal-tab="items">Itens</button>
                        <button class="tab-btn" data-modal-tab="money">Dinheiro</button>
                        <button class="tab-btn" data-modal-tab="groups">Grupos</button>
                        <button class="tab-btn" data-modal-tab="punish">Punições</button>
                        <button class="tab-btn" data-modal-tab="other">Outros</button>
                    </div>
                    
                    <div class="modal-tab-content">
                        <!-- Teleport Tab -->
                        <div id="teleport" class="modal-tab active">
                            <button onclick="teleportToPlayer()">Ir até o jogador</button>
                            <button onclick="teleportPlayerToMe()">Trazer jogador</button>
                        </div>
                        
                        <!-- Items Tab -->
                        <div id="items" class="modal-tab">
                            <div class="input-group">
                                <input type="text" id="itemName" placeholder="Nome do item">
                                <input type="number" id="itemAmount" placeholder="Quantidade" value="1">
                                <button onclick="giveItem()">Dar Item</button>
                            </div>
                            <button onclick="clearInventory()" class="danger">Limpar Inventário</button>
                        </div>
                        
                        <!-- Money Tab -->
                        <div id="money" class="modal-tab">
                            <div class="input-group">
                                <input type="number" id="moneyAmount" placeholder="Valor">
                                <button onclick="giveMoney()">Dar Dinheiro</button>
                            </div>
                        </div>
                        
                        <!-- Groups Tab -->
                        <div id="groups" class="modal-tab">
                            <div class="input-group">
                                <select id="groupSelect">
                                    <option value="Admin">Admin</option>
                                    <option value="Police">Police</option>
                                    <option value="Paramedic">Paramedic</option>
                                    <option value="Mechanic">Mechanic</option>
                                    <option value="Premium">Premium</option>
                                </select>
                                <input type="number" id="groupLevel" placeholder="Nível" value="1">
                                <button onclick="setGroup()">Definir Grupo</button>
                            </div>
                            <div class="input-group">
                                <select id="removeGroupSelect">
                                    <option value="Admin">Admin</option>
                                    <option value="Police">Police</option>
                                    <option value="Paramedic">Paramedic</option>
                                    <option value="Mechanic">Mechanic</option>
                                    <option value="Premium">Premium</option>
                                </select>
                                <button onclick="removeGroup()" class="danger">Remover Grupo</button>
                            </div>
                        </div>
                        
                        <!-- Punish Tab -->
                        <div id="punish" class="modal-tab">
                            <div class="input-group">
                                <input type="text" id="kickReason" placeholder="Motivo do kick">
                                <button onclick="kickPlayer()" class="warning">Expulsar</button>
                            </div>
                            <div class="input-group">
                                <input type="number" id="banTime" placeholder="Dias" value="1">
                                <input type="text" id="banReason" placeholder="Motivo do ban">
                                <button onclick="banPlayer()" class="danger">Banir</button>
                            </div>
                            <div class="input-group">
                                <input type="text" id="warningMessage" placeholder="Mensagem da advertência">
                                <button onclick="sendWarning()" class="warning">Advertir</button>
                            </div>
                        </div>
                        
                        <!-- Other Tab -->
                        <div id="other" class="modal-tab">
                            <button onclick="revivePlayer()">Reviver</button>
                            <button onclick="spectatePlayer()">Espectatar</button>
                            <button onclick="freezePlayer(true)" class="warning">Congelar</button>
                            <button onclick="freezePlayer(false)">Descongelar</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
