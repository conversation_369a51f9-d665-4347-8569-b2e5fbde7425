<?xml version="1.0" encoding="UTF - 8"?>

<CWeaponAnimationsSets>
	<WeaponAnimationsSets>
		<Item key="Default">
			<WeaponAnimations>
				<Item key="WEAPON_KARAMBIT">
					<CoverMovementClipSetHash/>
					<CoverMovementExtraClipSetHash/>
					<CoverAlternateMovementClipSetHash/>
					<CoverWeaponClipSetHash>Cover_Wpn_Melee1h</CoverWeaponClipSetHash>
					<MotionClipSetHash>weapons@melee_1h</MotionClipSetHash>
					<MotionFilterHash>BothArms_filter</MotionFilterHash>
					<MotionCrouchClipSetHash/>
					<MotionStrafingClipSetHash>move_strafe_melee_unarmed</MotionStrafingClipSetHash>
					<MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
					<MotionStrafingUpperBodyClipSetHash>move_strafe@knife</MotionStrafingUpperBodyClipSetHash>
					<WeaponClipSetHash>melee@holster</WeaponClipSetHash>
					<WeaponClipSetStreamedHash/>
					<WeaponClipSetHashInjured/>
					<WeaponClipSetHashStealth/>
					<WeaponClipSetHashHiCover/>
					<AlternativeClipSetWhenBlocked/>
					<ScopeWeaponClipSet/>
					<AlternateAimingStandingClipSetHash/>
					<AlternateAimingCrouchingClipSetHash/>
					<FiringVariationsStandingClipSetHash/>
					<FiringVariationsCrouchingClipSetHash/>
					<AimTurnStandingClipSetHash/>
					<AimTurnCrouchingClipSetHash/>
					<MeleeClipSetHash>melee@knife@streamed_core</MeleeClipSetHash>
					<MeleeVariationClipSetHash>melee@knife@streamed_variations</MeleeVariationClipSetHash>
					<MeleeTauntClipSetHash/>
					<MeleeSupportTauntClipSetHash/>
					<MeleeStealthClipSetHash/>
					<ShellShockedClipSetHash>reaction@shellshock@unarmed</ShellShockedClipSetHash>
					<JumpUpperbodyClipSetHash/>
					<FallUpperbodyClipSetHash/>
					<FromStrafeTransitionUpperBodyClipSetHash/>
					<SwapWeaponFilterHash>RightArm_NoSpine_filter</SwapWeaponFilterHash>
					<SwapWeaponInLowCoverFilterHash>RightArm_NoSpine_filter</SwapWeaponInLowCoverFilterHash>
					<AnimFireRateModifier value="1.000000"/>
					<AnimBlindFireRateModifier value="0.000000"/>
					<AnimWantingToShootFireRateModifier value="-1.000000"/>
					<UseFromStrafeUpperBodyAimNetwork value="false"/>
					<AimingDownTheBarrel value="true"/>
					<WeaponSwapData ref="SWAP_DEFAULT"/>
					<AimGrenadeThrowNormalClipsetHash/>
					<AimGrenadeThrowAlternateClipsetHash/>
				</Item>
			</WeaponAnimations>
		</Item>
		<Item key="FirstPerson">
			<Fallback>Default</Fallback>
			<WeaponAnimations>
				<Item key="WEAPON_KARAMBIT">
					<MovementOverrideClipSetHash>move_m@generic</MovementOverrideClipSetHash>
					<CoverMovementClipSetHash/>
					<CoverMovementExtraClipSetHash/>
					<CoverAlternateMovementClipSetHash/>
					<CoverWeaponClipSetHash>Cover_Wpn_Melee1h</CoverWeaponClipSetHash>
					<MotionClipSetHash>weapons@first_person@aim_idle@generic@melee@knife@shared@core</MotionClipSetHash>
					<MotionFilterHash>BothArms_filter</MotionFilterHash>
					<MotionCrouchClipSetHash/>
					<MotionStrafingClipSetHash>move_strafe_melee_unarmed_fps</MotionStrafingClipSetHash>
					<MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
					<MotionStrafingUpperBodyClipSetHash>move_strafe@melee_knife_fps</MotionStrafingUpperBodyClipSetHash>
					<WeaponClipSetHash>weapons@first_person@aim_idle@generic@melee@knife@shared@core</WeaponClipSetHash>
					<WeaponClipSetStreamedHash>weapons@first_person@aim_rng@generic@pistol@pistol_str</WeaponClipSetStreamedHash>
					<WeaponClipSetHashInjured/>
					<WeaponClipSetHashStealth/>
					<WeaponClipSetHashHiCover/>
					<AlternativeClipSetWhenBlocked/>
					<ScopeWeaponClipSet/>
					<AlternateAimingStandingClipSetHash/>
					<AlternateAimingCrouchingClipSetHash/>
					<FiringVariationsStandingClipSetHash/>
					<FiringVariationsCrouchingClipSetHash/>
					<AimTurnStandingClipSetHash/>
					<AimTurnCrouchingClipSetHash/>
					<MeleeClipSetHash>melee@knife@streamed_core_fps</MeleeClipSetHash>
					<MeleeVariationClipSetHash>melee@knife@streamed_variations</MeleeVariationClipSetHash>
					<MeleeTauntClipSetHash/>
					<MeleeSupportTauntClipSetHash/>
					<MeleeStealthClipSetHash/>
					<ShellShockedClipSetHash>reaction@shellshock@unarmed</ShellShockedClipSetHash>
					<JumpUpperbodyClipSetHash/>
					<FallUpperbodyClipSetHash/>
					<FromStrafeTransitionUpperBodyClipSetHash/>
					<SwapWeaponFilterHash>RightArm_NoSpine_filter</SwapWeaponFilterHash>
					<SwapWeaponInLowCoverFilterHash>RightArm_NoSpine_filter</SwapWeaponInLowCoverFilterHash>
					<AnimFireRateModifier value="1.000000"/>
					<AnimBlindFireRateModifier value="0.000000"/>
					<AnimWantingToShootFireRateModifier value="-1.000000"/>
					<UseFromStrafeUpperBodyAimNetwork value="false"/>
					<AimingDownTheBarrel value="true"/>
					<WeaponSwapData ref="SWAP_DEFAULT"/>
					<AimGrenadeThrowNormalClipsetHash/>
					<AimGrenadeThrowAlternateClipsetHash/>
					<FPSTransitionFromUnholsterHash>weapons@first_person@aim_idle@p_m_zero@melee@knife@aim_trans@unholster_to_idle</FPSTransitionFromUnholsterHash>
					<WeaponClipSetHashForClone>weapons@first_person@aim_idle@remote_clone@melee@one_handed@shared@core</WeaponClipSetHashForClone>
					<FPSFidgetClipsetHashes>
						<Item>weapons@first_person@aim_idle@p_m_zero@melee@knife@fidgets@a</Item>
						<Item>weapons@first_person@aim_idle@p_m_zero@melee@knife@fidgets@b</Item>
						<Item>weapons@first_person@aim_idle@p_m_zero@melee@knife@fidgets@c</Item>
					</FPSFidgetClipsetHashes>
				</Item>
			</WeaponAnimations>
		</Item>
	</WeaponAnimationsSets>
</CWeaponAnimationsSets>

