* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    overflow: hidden;
}

.painel-panel {
    display: none;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.95);
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
}

.painel-panel.active {
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    border-bottom: 3px solid #4a90e2;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    object-fit: cover;
}

.header h1 {
    color: white;
    font-size: 24px;
    font-weight: 600;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.admin-info {
    display: flex;
    align-items: center;
    gap: 10px;
    color: white;
    font-weight: 500;
}

.close-btn {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: white;
    padding: 10px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

/* Main Layout */
.main-layout {
    display: flex;
    flex: 1;
    height: calc(100vh - 90px);
}

.painel-panel.active .main-content {
    display: flex;
    flex: 1;
}

/* Sidebar */
.sidebar {
    width: 250px;
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    border-right: 3px solid #4a90e2;
    padding: 20px 0;
}

.nav-menu {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 25px;
    color: #bdc3c7;
    cursor: pointer;
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.nav-item:hover {
    background: rgba(74, 144, 226, 0.1);
    color: white;
    border-left-color: #4a90e2;
}

.nav-item.active {
    background: rgba(74, 144, 226, 0.2);
    color: white;
    border-left-color: #4a90e2;
}

.nav-item i {
    font-size: 18px;
    width: 20px;
}

/* Main Content */
.main-content {
    flex: 1;
    background: #f8f9fa;
    overflow-y: auto;
    position: relative;
}

.tab-content {
    display: none;
    padding: 30px;
    height: 100%;
}

.tab-content.active {
    display: block;
}

/* Dashboard */
.dashboard-header {
    margin-bottom: 30px;
}

.dashboard-header h2 {
    font-size: 28px;
    color: #2c3e50;
    margin-bottom: 10px;
}

.dashboard-header p {
    color: #7f8c8d;
    font-size: 16px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    background: linear-gradient(135deg, #4a90e2, #357abd);
}

.stat-info h3 {
    font-size: 32px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
}

.stat-info p {
    color: #7f8c8d;
    font-size: 14px;
}

.quick-actions {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.quick-actions h3 {
    margin-bottom: 20px;
    color: #2c3e50;
    font-size: 20px;
}

.action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px 20px;
    background: linear-gradient(135deg, #4a90e2, #357abd);
    color: white;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(74, 144, 226, 0.4);
}

/* Tab Headers */
.tab-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
}

.tab-header h2 {
    font-size: 24px;
    color: #2c3e50;
}

.refresh-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    background: #4a90e2;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.refresh-btn:hover {
    background: #357abd;
    transform: scale(1.05);
}

/* Search Bar */
.search-bar {
    position: relative;
    margin-bottom: 25px;
}

.search-bar input {
    width: 100%;
    padding: 15px 50px 15px 20px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.search-bar input:focus {
    outline: none;
    border-color: #4a90e2;
}

.search-bar i {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    color: #7f8c8d;
}

/* Players Grid */
.players-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.player-card {
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    cursor: pointer;
}

.player-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.15);
}

.player-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.player-name {
    font-weight: 600;
    color: #2c3e50;
    font-size: 16px;
}

.player-id {
    background: #4a90e2;
    color: white;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
}

.player-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.player-info-item {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
}

.player-info-label {
    color: #7f8c8d;
}

.player-info-value {
    color: #2c3e50;
    font-weight: 500;
}

/* Forms */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #2c3e50;
    font-weight: 500;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #4a90e2;
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* Buttons */
button {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    font-size: 14px;
}

.send-btn {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px 30px;
    font-size: 16px;
}

.send-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
}

.danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
}

.warning {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
}

.warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(243, 156, 18, 0.4);
}

/* Input Groups */
.input-group {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-bottom: 15px;
}

.input-group input,
.input-group select {
    flex: 1;
}

.input-group button {
    white-space: nowrap;
}

/* Action Groups */
.action-group {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.action-group h3 {
    margin-bottom: 15px;
    color: #2c3e50;
    font-size: 18px;
}

.button-group {
    display: flex;
    gap: 10px;
}

/* Vehicle Actions */
.vehicle-actions {
    margin-bottom: 30px;
}

.vehicle-list {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.1);
}

.vehicle-list h3 {
    margin-bottom: 20px;
    color: #2c3e50;
    font-size: 18px;
}

.vehicles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    max-height: 400px;
    overflow-y: auto;
}

.vehicle-item {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.vehicle-item:hover {
    background: #e9ecef;
    border-color: #4a90e2;
    transform: scale(1.02);
}

.vehicle-name {
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 5px;
}

.vehicle-spawn {
    font-size: 12px;
    color: #7f8c8d;
}

/* Announcement Form */
.announcement-form {
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.1);
    max-width: 600px;
}

/* Warnings List */
.warnings-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.warning-item {
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #f39c12;
}

.warning-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.warning-info {
    display: flex;
    gap: 20px;
    font-size: 14px;
    color: #7f8c8d;
}

.warning-message {
    color: #2c3e50;
    font-weight: 500;
    margin-bottom: 10px;
}

.delete-warning-btn {
    background: #e74c3c;
    color: white;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
}

/* Chests Grid */
.chests-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
}

.chest-card {
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.1);
    text-align: center;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.chest-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.15);
}

.chest-icon {
    font-size: 48px;
    color: #4a90e2;
    margin-bottom: 15px;
}

.chest-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 16px;
}

.chest-info {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #7f8c8d;
    margin-bottom: 15px;
}

.open-chest-btn {
    background: #4a90e2;
    color: white;
    padding: 10px 20px;
    border-radius: 8px;
    width: 100%;
}

.open-chest-btn:hover {
    background: #357abd;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 15px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    border-bottom: 2px solid #e9ecef;
    background: linear-gradient(135deg, #4a90e2, #357abd);
    color: white;
    border-radius: 15px 15px 0 0;
}

.modal-header h3 {
    font-size: 20px;
    font-weight: 600;
}

.close {
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: white;
    transition: color 0.3s ease;
}

.close:hover {
    color: #f1c40f;
}

.modal-body {
    padding: 30px;
}

.player-info {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 25px;
}

.player-info h4 {
    color: #2c3e50;
    font-size: 18px;
    margin-bottom: 10px;
}

.player-info p {
    color: #7f8c8d;
    margin-bottom: 5px;
}

/* Action Tabs */
.action-tabs {
    margin-top: 20px;
}

.tab-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 15px;
}

.tab-btn {
    padding: 10px 20px;
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    color: #7f8c8d;
}

.tab-btn.active {
    background: #4a90e2;
    color: white;
    border-color: #4a90e2;
}

.tab-btn:hover:not(.active) {
    background: #e9ecef;
    color: #2c3e50;
}

.modal-tab-content {
    min-height: 200px;
}

.modal-tab {
    display: none;
}

.modal-tab.active {
    display: block;
}

.modal-tab button {
    margin-right: 10px;
    margin-bottom: 10px;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .sidebar {
        width: 200px;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .painel-panel {
        flex-direction: column;
    }

    .main-layout {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        height: auto;
        order: 2;
    }

    .nav-menu {
        flex-direction: row;
        overflow-x: auto;
        padding: 0 20px;
    }

    .nav-item {
        white-space: nowrap;
        min-width: 120px;
        justify-content: center;
    }

    .main-content {
        order: 1;
        height: auto;
        min-height: 60vh;
    }

    .header {
        padding: 15px 20px;
    }

    .header h1 {
        font-size: 20px;
    }

    .tab-content {
        padding: 20px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .action-buttons {
        grid-template-columns: 1fr;
    }

    .players-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        margin: 20px;
    }

    .modal-body {
        padding: 20px;
    }

    .tab-buttons {
        flex-direction: column;
    }

    .input-group {
        flex-direction: column;
    }

    .button-group {
        flex-direction: column;
    }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #4a90e2;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #357abd;
}

/* Loading Animation */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
}

.loading::after {
    content: '';
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #4a90e2;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.text-success {
    color: #27ae60;
}

.text-danger {
    color: #e74c3c;
}

.text-warning {
    color: #f39c12;
}

.text-info {
    color: #4a90e2;
}

.mb-10 {
    margin-bottom: 10px;
}

.mb-20 {
    margin-bottom: 20px;
}

.mt-10 {
    margin-top: 10px;
}

.mt-20 {
    margin-top: 20px;
}
