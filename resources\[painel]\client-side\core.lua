-----------------------------------------------------------------------------------------------------------------------------------------
-- VRP
-----------------------------------------------------------------------------------------------------------------------------------------
local Tunnel = module("vrp","lib/Tunnel")
local Proxy = module("vrp","lib/Proxy")
vRP = Proxy.getInterface("vRP")
-----------------------------------------------------------------------------------------------------------------------------------------
-- CONNECTION
-----------------------------------------------------------------------------------------------------------------------------------------
vSERVER = Tunnel.getInterface("painel")
-----------------------------------------------------------------------------------------------------------------------------------------
-- VARIABLES
-----------------------------------------------------------------------------------------------------------------------------------------
local painelOpen = false
local painelData = {}
-----------------------------------------------------------------------------------------------------------------------------------------
-- PAINEL:TOGGLE
-----------------------------------------------------------------------------------------------------------------------------------------
RegisterNetEvent("painel:Toggle")
AddEventHandler("painel:Toggle", function(data)
    painelOpen = not painelOpen
    painelData = data or {}
    
    SetNuiFocus(painelOpen, painelOpen)
    SendNUIMessage({
        action = "toggle",
        status = painelOpen,
        data = painelData
    })
end)
-----------------------------------------------------------------------------------------------------------------------------------------
-- PAINEL:UPDATE
-----------------------------------------------------------------------------------------------------------------------------------------
RegisterNetEvent("painel:Update")
AddEventHandler("painel:Update", function(data)
    painelData = data
    SendNUIMessage({
        action = "update",
        data = painelData
    })
end)
-----------------------------------------------------------------------------------------------------------------------------------------
-- NUI CALLBACKS
-----------------------------------------------------------------------------------------------------------------------------------------
RegisterNUICallback("close", function(data, cb)
    print("Callback close recebido")
    painelOpen = false
    SetNuiFocus(false, false)
    print("NUI Focus desativado")
    cb("ok")
end)

RegisterNUICallback("getPlayers", function(data, cb)
    local result = vSERVER.getPlayers()
    cb(result)
end)

RegisterNUICallback("teleportToPlayer", function(data, cb)
    vSERVER.teleportToPlayer(data.id)
    cb("ok")
end)

RegisterNUICallback("teleportPlayerToMe", function(data, cb)
    vSERVER.teleportPlayerToMe(data.id)
    cb("ok")
end)

RegisterNUICallback("kickPlayer", function(data, cb)
    vSERVER.kickPlayer(data.id, data.reason)
    cb("ok")
end)

RegisterNUICallback("banPlayer", function(data, cb)
    vSERVER.banPlayer(data.id, data.time, data.reason)
    cb("ok")
end)

RegisterNUICallback("giveItem", function(data, cb)
    vSERVER.giveItem(data.id, data.item, data.amount)
    cb("ok")
end)

RegisterNUICallback("giveMoney", function(data, cb)
    vSERVER.giveMoney(data.id, data.amount)
    cb("ok")
end)

RegisterNUICallback("setGroup", function(data, cb)
    vSERVER.setGroup(data.id, data.group, data.level)
    cb("ok")
end)

RegisterNUICallback("removeGroup", function(data, cb)
    vSERVER.removeGroup(data.id, data.group)
    cb("ok")
end)

RegisterNUICallback("revivePlayer", function(data, cb)
    vSERVER.revivePlayer(data.id)
    cb("ok")
end)

RegisterNUICallback("sendAnnouncement", function(data, cb)
    vSERVER.sendAnnouncement(data.message, data.time)
    cb("ok")
end)

RegisterNUICallback("sendWarning", function(data, cb)
    vSERVER.sendWarning(data.id, data.message)
    cb("ok")
end)

RegisterNUICallback("clearInventory", function(data, cb)
    vSERVER.clearInventory(data.id)
    cb("ok")
end)

RegisterNUICallback("spectatePlayer", function(data, cb)
    vSERVER.spectatePlayer(data.id)
    cb("ok")
end)

RegisterNUICallback("stopSpectate", function(data, cb)
    vSERVER.stopSpectate()
    cb("ok")
end)

RegisterNUICallback("freezePlayer", function(data, cb)
    vSERVER.freezePlayer(data.id, data.freeze)
    cb("ok")
end)

RegisterNUICallback("getVehicles", function(data, cb)
    local result = vSERVER.getVehicles()
    cb(result)
end)

RegisterNUICallback("spawnVehicle", function(data, cb)
    vSERVER.spawnVehicle(data.vehicle)
    cb("ok")
end)

RegisterNUICallback("deleteVehicle", function(data, cb)
    vSERVER.deleteVehicle()
    cb("ok")
end)

RegisterNUICallback("repairVehicle", function(data, cb)
    vSERVER.repairVehicle()
    cb("ok")
end)

RegisterNUICallback("getChests", function(data, cb)
    local result = vSERVER.getChests()
    cb(result)
end)

RegisterNUICallback("openChest", function(data, cb)
    vSERVER.openChest(data.name)
    cb("ok")
end)

RegisterNUICallback("getWarnings", function(data, cb)
    local result = vSERVER.getWarnings()
    cb(result)
end)

RegisterNUICallback("deleteWarning", function(data, cb)
    vSERVER.deleteWarning(data.id)
    cb("ok")
end)
-----------------------------------------------------------------------------------------------------------------------------------------
-- KEYBIND
-----------------------------------------------------------------------------------------------------------------------------------------
RegisterKeyMapping("painel", "Abrir Painel Admin", "keyboard", "F10")

RegisterCommand("painel", function()
    vSERVER.checkPermission()
end)
-----------------------------------------------------------------------------------------------------------------------------------------
-- PAINEL:SPECTATE
-----------------------------------------------------------------------------------------------------------------------------------------
local spectating = false
local spectateTarget = nil

RegisterNetEvent("painel:StartSpectate")
AddEventHandler("painel:StartSpectate", function(target)
    spectating = true
    spectateTarget = target
    
    local targetPed = GetPlayerPed(GetPlayerFromServerId(target))
    if targetPed then
        NetworkSetInSpectatorMode(true, targetPed)
    end
end)

RegisterNetEvent("painel:StopSpectate")
AddEventHandler("painel:StopSpectate", function()
    spectating = false
    spectateTarget = nil
    NetworkSetInSpectatorMode(false, PlayerPedId())
end)
-----------------------------------------------------------------------------------------------------------------------------------------
-- PAINEL:FREEZE
-----------------------------------------------------------------------------------------------------------------------------------------
RegisterNetEvent("painel:Freeze")
AddEventHandler("painel:Freeze", function(freeze)
    FreezeEntityPosition(PlayerPedId(), freeze)
end)
