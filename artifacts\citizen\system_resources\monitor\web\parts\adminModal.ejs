<!-- Field: Username -->
<div class="form-group row">
    <label for="modAdmin-name" class="col-sm-3 col-form-label">
        Username
        <% if (isNewAdmin) { %>
            <small class="text-danger">(required)</small>
        <% } %>
    </label>
    <div class="col-sm-9">
        <div class="input-group">
            <input type="text" class="form-control" id="modAdmin-name" 
                value="<%= username %>" <%= isNewAdmin ? 'required' : 'readonly' %>>
        </div>
    </div>
</div>

<!-- Field: CitizenFX -->
<div class="form-group row">
    <label for="modAdmin-citizenfxID" class="col-sm-3 col-form-label">
        Cfx.re ID <br>
        <small class="text-muted">(optional)</small>
    </label>
    <div class="col-sm-9">
        <div class="input-group">
            <input type="text" class="form-control" id="modAdmin-citizenfxID" maxlength="20"
                autocomplete="off" value="<%= citizenfx_id %>">
        </div>
        <span class="form-text text-muted">
            The admin's <a href="https://forum.cfx.re/" target="_blank" rel="noopener noreferrer">https://forum.cfx.re/</a>  username. This is required if you want to login using the Cfx.re button.
        </span>
    </div>
</div>

<!-- Field: Discord -->
<div class="form-group row">
    <label for="modAdmin-discordID" class="col-sm-3 col-form-label">
        Discord ID <br>
        <small class="text-muted">(optional)</small>
    </label>
    <div class="col-sm-9">
        <div class="input-group">
            <input type="number" class="form-control" id="modAdmin-discordID" autocomplete="off" value="<%= discord_id %>">
        </div>
        <span class="form-text text-muted">
            The admin's Discord User ID. Follow <a href="https://support.discordapp.com/hc/en-us/articles/206346498-Where-can-I-find-my-User-Server-Message-ID" target="_blank">this guide</a> if you don't know how to get the User ID.
        </span>
    </div>
</div>

<!-- Permissions -->
<hr>
<div class="form-group justify-content-center row">
    <h5>Permissions</h5>
</div>
<div class="form-group row">
    <div class="col-sm-6">
        <% for (const [key, perm] of permsMenu.entries()) { %>
        <div class="form-check checkbox">
            <input class="form-check-input permsCheckbox" id="modAdmin-permsMenu-<%= perm.id %>-<%= key %>"
                type="checkbox" name="modAdmin-permissions[]" value="<%= perm.id %>" <%= perm.checked %>>
            <label class="form-check-label <%= perm.dangerous ? 'text-danger' : '' %>" 
                for="modAdmin-permsMenu-<%= perm.id %>-<%= key %>" alt="sdfsdf sdf sdfsdfs">
                <%= perm.desc %>
            </label>
        </div>
        <% } %>
    </div>
    <div class="col-sm-6">
        <% for (const [key, perm] of permsGeneral.entries()) { %>
        <div class="form-check checkbox">
            <input class="form-check-input permsCheckbox" id="modAdmin-permsGeneral-<%= perm.id %>-<%= key %>" type="checkbox" name="modAdmin-permissions[]"
                value="<%= perm.id %>" <%= perm.checked %>>
            <label class="form-check-label <%= perm.dangerous ? 'text-danger' : '' %>" for="modAdmin-permsGeneral-<%= perm.id %>-<%= key %>"><%= perm.desc %></label>
        </div>
        <% } %>
    </div>
</div>

<!-- Just signaling for the save action -->
<input type="hidden" id="modAdmin-isNewAdmin" value="<%= isNewAdmin %>">
