[{"n": "G:/[BACKUP B13]/TESTES/resources//[scripts]/robberys/@vrp/config/Native.lua", "mt": 0, "s": 18446744073709551615, "i": "AAAAAAAAAAAAAAAAAAAAAA=="}, {"n": "G:/[BACKUP B13]/TESTES/resources//[scripts]/robberys/@vrp/lib/Utils.lua", "mt": 0, "s": 18446744073709551615, "i": "AAAAAAAAAAAAAAAAAAAAAA=="}, {"n": "G:/[BACKUP B13]/TESTES/resources//[scripts]/robberys/client-side/core.lua", "mt": 1686344099, "s": 703, "i": "GRUCAAAAAgAAAAAAAAAAAA=="}, {"n": "G:/[BACKUP B13]/TESTES/resources//[scripts]/robberys/fxmanifest.lua", "mt": 1686344099, "s": 218, "i": "FxUCAAAAAgAAAAAAAAAAAA=="}]