-----------------------------------------------------------------------------------------------------------------------------------------
-- PAINEL:CLEARINVENTORY
-----------------------------------------------------------------------------------------------------------------------------------------
function painel.clearInventory(targetId)
    local source = source
    local Passport = vRP.Passport(source)
    
    if not vRP.HasPermission(Passport, "Admin") then
        return
    end
    
    local targetSource = vRP.Source(targetId)
    if targetSource then
        local Datatable = vRP.Datatable(targetId)
        if Datatable then
            Datatable.Inventory = {}
            TriggerClientEvent("inventory:Update", targetSource, "Backpack")
            TriggerClientEvent("Notify", source, "sucesso", "Inventário limpo.", 5000)
            TriggerClientEvent("Notify", targetSource, "aviso", "Seu inventário foi limpo por um administrador.", 5000)
            
            -- Log
            TriggerEvent("Discord", "Painel", "**Admin:** "..vRP.Identity(Passport).name.." "..vRP.Identity(Passport).name2.."\n**Ação:** Clear Inventory\n**Jogador:** "..vRP.Identity(targetId).name.." "..vRP.Identity(targetId).name2, 15105570)
        end
    else
        TriggerClientEvent("Notify", source, "negado", "Jogador não encontrado.", 5000)
    end
end
-----------------------------------------------------------------------------------------------------------------------------------------
-- PAINEL:SPECTATEPLAYER
-----------------------------------------------------------------------------------------------------------------------------------------
function painel.spectatePlayer(targetId)
    local source = source
    local Passport = vRP.Passport(source)
    
    if not vRP.HasPermission(Passport, "Admin") then
        return
    end
    
    local targetSource = vRP.Source(targetId)
    if targetSource then
        spectating[source] = targetId
        TriggerClientEvent("painel:StartSpectate", source, targetId)
        TriggerClientEvent("Notify", source, "sucesso", "Espectatando jogador.", 5000)
        
        -- Log
        TriggerEvent("Discord", "Painel", "**Admin:** "..vRP.Identity(Passport).name.." "..vRP.Identity(Passport).name2.."\n**Ação:** Spectate\n**Jogador:** "..vRP.Identity(targetId).name.." "..vRP.Identity(targetId).name2, 3447003)
    else
        TriggerClientEvent("Notify", source, "negado", "Jogador não encontrado.", 5000)
    end
end
-----------------------------------------------------------------------------------------------------------------------------------------
-- PAINEL:STOPSPECTATE
-----------------------------------------------------------------------------------------------------------------------------------------
function painel.stopSpectate()
    local source = source
    local Passport = vRP.Passport(source)
    
    if not vRP.HasPermission(Passport, "Admin") then
        return
    end
    
    if spectating[source] then
        spectating[source] = nil
        TriggerClientEvent("painel:StopSpectate", source)
        TriggerClientEvent("Notify", source, "sucesso", "Parou de espectatar.", 5000)
    end
end
-----------------------------------------------------------------------------------------------------------------------------------------
-- PAINEL:FREEZEPLAYER
-----------------------------------------------------------------------------------------------------------------------------------------
function painel.freezePlayer(targetId, freeze)
    local source = source
    local Passport = vRP.Passport(source)
    
    if not vRP.HasPermission(Passport, "Admin") then
        return
    end
    
    local targetSource = vRP.Source(targetId)
    if targetSource then
        TriggerClientEvent("painel:Freeze", targetSource, freeze)
        local action = freeze and "congelado" or "descongelado"
        TriggerClientEvent("Notify", source, "sucesso", "Jogador "..action..".", 5000)
        TriggerClientEvent("Notify", targetSource, "aviso", "Você foi "..action.." por um administrador.", 5000)
        
        -- Log
        TriggerEvent("Discord", "Painel", "**Admin:** "..vRP.Identity(Passport).name.." "..vRP.Identity(Passport).name2.."\n**Ação:** "..(freeze and "Freeze" or "Unfreeze").."\n**Jogador:** "..vRP.Identity(targetId).name.." "..vRP.Identity(targetId).name2, 3447003)
    else
        TriggerClientEvent("Notify", source, "negado", "Jogador não encontrado.", 5000)
    end
end
-----------------------------------------------------------------------------------------------------------------------------------------
-- PAINEL:GETVEHICLES
-----------------------------------------------------------------------------------------------------------------------------------------
function painel.getVehicles()
    local source = source
    local Passport = vRP.Passport(source)
    
    if not vRP.HasPermission(Passport, "Admin") then
        return {}
    end
    
    local vehicles = {}
    for k, v in pairs(VehicleList) do
        table.insert(vehicles, {
            spawn = k,
            name = v.name or k
        })
    end
    
    return vehicles
end
-----------------------------------------------------------------------------------------------------------------------------------------
-- PAINEL:SPAWNVEHICLE
-----------------------------------------------------------------------------------------------------------------------------------------
function painel.spawnVehicle(vehicle)
    local source = source
    local Passport = vRP.Passport(source)
    
    if not vRP.HasPermission(Passport, "Admin") then
        return
    end
    
    local coords = GetEntityCoords(GetPlayerPed(source))
    local heading = GetEntityHeading(GetPlayerPed(source))
    
    local vehicleHash = GetHashKey(vehicle)
    local spawnedVehicle = CreateVehicle(vehicleHash, coords.x, coords.y, coords.z, heading, true, true)
    
    if DoesEntityExist(spawnedVehicle) then
        TriggerClientEvent("Notify", source, "sucesso", "Veículo spawnado.", 5000)
        
        -- Log
        TriggerEvent("Discord", "Painel", "**Admin:** "..vRP.Identity(Passport).name.." "..vRP.Identity(Passport).name2.."\n**Ação:** Spawn Vehicle\n**Veículo:** "..vehicle, 3447003)
    else
        TriggerClientEvent("Notify", source, "negado", "Erro ao spawnar veículo.", 5000)
    end
end
-----------------------------------------------------------------------------------------------------------------------------------------
-- PAINEL:DELETEVEHICLE
-----------------------------------------------------------------------------------------------------------------------------------------
function painel.deleteVehicle()
    local source = source
    local Passport = vRP.Passport(source)
    
    if not vRP.HasPermission(Passport, "Admin") then
        return
    end
    
    local ped = GetPlayerPed(source)
    local vehicle = GetVehiclePedIsIn(ped, false)
    
    if vehicle and vehicle ~= 0 then
        DeleteEntity(vehicle)
        TriggerClientEvent("Notify", source, "sucesso", "Veículo deletado.", 5000)
        
        -- Log
        TriggerEvent("Discord", "Painel", "**Admin:** "..vRP.Identity(Passport).name.." "..vRP.Identity(Passport).name2.."\n**Ação:** Delete Vehicle", 15105570)
    else
        TriggerClientEvent("Notify", source, "negado", "Você não está em um veículo.", 5000)
    end
end
-----------------------------------------------------------------------------------------------------------------------------------------
-- PAINEL:REPAIRVEHICLE
-----------------------------------------------------------------------------------------------------------------------------------------
function painel.repairVehicle()
    local source = source
    local Passport = vRP.Passport(source)
    
    if not vRP.HasPermission(Passport, "Admin") then
        return
    end
    
    local ped = GetPlayerPed(source)
    local vehicle = GetVehiclePedIsIn(ped, false)
    
    if vehicle and vehicle ~= 0 then
        SetVehicleFixed(vehicle)
        SetVehicleDirtLevel(vehicle, 0.0)
        SetVehicleFuelLevel(vehicle, 100.0)
        TriggerClientEvent("Notify", source, "sucesso", "Veículo reparado.", 5000)
        
        -- Log
        TriggerEvent("Discord", "Painel", "**Admin:** "..vRP.Identity(Passport).name.." "..vRP.Identity(Passport).name2.."\n**Ação:** Repair Vehicle", 3447003)
    else
        TriggerClientEvent("Notify", source, "negado", "Você não está em um veículo.", 5000)
    end
end
-----------------------------------------------------------------------------------------------------------------------------------------
-- PAINEL:GETCHESTS
-----------------------------------------------------------------------------------------------------------------------------------------
function painel.getChests()
    local source = source
    local Passport = vRP.Passport(source)
    
    if not vRP.HasPermission(Passport, "Admin") then
        return {}
    end
    
    local chests = vRP.Query("chests/GetChests", {})
    return chests
end
-----------------------------------------------------------------------------------------------------------------------------------------
-- PAINEL:OPENCHEST
-----------------------------------------------------------------------------------------------------------------------------------------
function painel.openChest(name)
    local source = source
    local Passport = vRP.Passport(source)
    
    if not vRP.HasPermission(Passport, "Admin") then
        return
    end
    
    TriggerEvent("chest:Open", source, name, "Admin")
    TriggerClientEvent("Notify", source, "sucesso", "Baú aberto.", 5000)
    
    -- Log
    TriggerEvent("Discord", "Painel", "**Admin:** "..vRP.Identity(Passport).name.." "..vRP.Identity(Passport).name2.."\n**Ação:** Open Chest\n**Baú:** "..name, 3447003)
end
-----------------------------------------------------------------------------------------------------------------------------------------
-- PAINEL:GETWARNINGS
-----------------------------------------------------------------------------------------------------------------------------------------
function painel.getWarnings()
    local source = source
    local Passport = vRP.Passport(source)
    
    if not vRP.HasPermission(Passport, "Admin") then
        return {}
    end
    
    return warnings
end
-----------------------------------------------------------------------------------------------------------------------------------------
-- PAINEL:DELETEWARNING
-----------------------------------------------------------------------------------------------------------------------------------------
function painel.deleteWarning(id)
    local source = source
    local Passport = vRP.Passport(source)
    
    if not vRP.HasPermission(Passport, "Admin") then
        return
    end
    
    if warnings[id] then
        warnings[id] = nil
        TriggerClientEvent("Notify", source, "sucesso", "Advertência removida.", 5000)
        
        -- Log
        TriggerEvent("Discord", "Painel", "**Admin:** "..vRP.Identity(Passport).name.." "..vRP.Identity(Passport).name2.."\n**Ação:** Delete Warning\n**ID:** "..id, 15105570)
    else
        TriggerClientEvent("Notify", source, "negado", "Advertência não encontrada.", 5000)
    end
end
-----------------------------------------------------------------------------------------------------------------------------------------
-- DISCONNECT EVENT
-----------------------------------------------------------------------------------------------------------------------------------------
AddEventHandler("Disconnect", function(Passport, source)
    if spectating[source] then
        spectating[source] = nil
    end
end)
