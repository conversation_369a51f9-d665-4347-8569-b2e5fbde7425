-----------------------------------------------------------------------------------------------------------------------------------------
-- VRP
-----------------------------------------------------------------------------------------------------------------------------------------
local Tunnel = module("vrp","lib/Tunnel")
-----------------------------------------------------------------------------------------------------------------------------------------
-- CONNECTION
-----------------------------------------------------------------------------------------------------------------------------------------
vSERVER = Tunnel.getInterface("crafting")
-----------------------------------------------------------------------------------------------------------------------------------------
-- CLOSE
-----------------------------------------------------------------------------------------------------------------------------------------
RegisterNUICallback("invClose",function(Data,Callback)
	SetNuiFocus(false,false)
	SendNUIMessage({ action = "hideNUI" })

	Callback("Ok")
end)
-----------------------------------------------------------------------------------------------------------------------------------------
-- REQUESTCRAFTING
-----------------------------------------------------------------------------------------------------------------------------------------
RegisterNUICallback("requestCrafting",function(Data,Callback)
	local inventoryCraft,inventoryUser,invPeso,invMaxpeso = vSERVER.requestCrafting(Data["craft"])
	if inventoryCraft then
		Callback({ inventoryCraft = inventoryCraft, inventario = inventoryUser, invPeso = invPeso, invMaxpeso = invMaxpeso })
	end
end)
-----------------------------------------------------------------------------------------------------------------------------------------
-- FUNCTIONCRAFT
-----------------------------------------------------------------------------------------------------------------------------------------
RegisterNUICallback("functionCraft",function(Data,Callback)
	vSERVER.functionCrafting(Data["index"],Data["craft"],Data["amount"],Data["slot"])

	Callback("Ok")
end)
-----------------------------------------------------------------------------------------------------------------------------------------
-- FUNCTIONDESTROY
-----------------------------------------------------------------------------------------------------------------------------------------
RegisterNUICallback("functionDestroy",function(Data,Callback)
	vSERVER.functionDestroy(Data["index"],Data["craft"],Data["amount"],Data["slot"])

	Callback("Ok")
end)
-----------------------------------------------------------------------------------------------------------------------------------------
-- POPULATESLOT
-----------------------------------------------------------------------------------------------------------------------------------------
RegisterNUICallback("populateSlot",function(Data,Callback)
	TriggerServerEvent("crafting:populateSlot",Data["item"],Data["slot"],Data["target"],Data["amount"])

	Callback("Ok")
end)
-----------------------------------------------------------------------------------------------------------------------------------------
-- UPDATESLOT
-----------------------------------------------------------------------------------------------------------------------------------------
RegisterNUICallback("updateSlot",function(Data,Callback)
	TriggerServerEvent("crafting:updateSlot",Data["item"],Data["slot"],Data["target"],Data["amount"])

	Callback("Ok")
end)
-----------------------------------------------------------------------------------------------------------------------------------------
-- CRAFTING:UPDATE
-----------------------------------------------------------------------------------------------------------------------------------------
RegisterNetEvent("crafting:Update")
AddEventHandler("crafting:Update",function(Action)
	SendNUIMessage({ action = Action })
end)
-----------------------------------------------------------------------------------------------------------------------------------------
-- LIST
-----------------------------------------------------------------------------------------------------------------------------------------
local List = {
	["1"] = { 82.45,-1553.26,29.59,"Lixeiro" },
	["2"] = { 287.36,2843.6,44.7,"Lixeiro" },
	["3"] = { -413.68,6171.99,31.48,"Lixeiro" },
	["4"] = { 1272.26,-1712.57,54.76,"Lester" },
	["5"] = { 46.21,-1749.45,29.64,"Mercado" },
	["6"] = { 2747.81,3472.91,55.67,"Mercado" },
	["7"] = { 807.67,-757.51,26.77,"PizzaThis" },
	["8"] = { -1198.04,-899.07,13.99,"BurgerShot" },
	["9"] = { -590.37,-1059.77,22.34,"UwuCoffee" },
	["10"] = { 122.69,-1041.57,29.56,"BeanMachine" },
	["11"] = { 95.58,-1985.56,20.44,"Ballas" },
	["12"] = { -31.47,-1434.84,31.49,"Families" },
	["13"] = { 347.45,-2069.06,20.89,"Vagos" },
	["14"] = { 512.29,-1803.52,28.51,"Aztecas" },
	["15"] = { 230.55,-1753.35,28.98,"Bloods" },
	["16"] = { -818.26,-717.89,23.78,"Triads" },
	["17"] = { 501.38,-66.92,58.15,"Razors" }
}
-----------------------------------------------------------------------------------------------------------------------------------------
-- THREADSTART
-----------------------------------------------------------------------------------------------------------------------------------------
CreateThread(function()
	for Number,v in pairs(List) do
		exports["target"]:AddCircleZone("Crafting:"..Number,vec3(v[1],v[2],v[3]),0.5,{
			name = "Crafting:"..Number,
			heading = 3374176
		},{
			shop = Number,
			Distance = 1.0,
			options = {
				{
					event = "crafting:openSystem",
					label = "Abrir",
					tunnel = "shop"
				}
			}
		})
	end
end)
-----------------------------------------------------------------------------------------------------------------------------------------
-- CRAFTING:OPENSYSTEM
-----------------------------------------------------------------------------------------------------------------------------------------
AddEventHandler("crafting:openSystem",function(Number)
	if List[Number] then
		if vSERVER.requestPerm(Number,List[Number][4]) then
			SetNuiFocus(true,true)
			SendNUIMessage({ action = "showNUI", name = List[Number][4] })
		end
	end
end)
-----------------------------------------------------------------------------------------------------------------------------------------
-- CRAFTING:OPENSOURCE
-----------------------------------------------------------------------------------------------------------------------------------------
RegisterNetEvent("crafting:openSource")
AddEventHandler("crafting:openSource",function()
	SetNuiFocus(true,true)
	SendNUIMessage({ action = "showNUI", name = "Inventory" })
end)