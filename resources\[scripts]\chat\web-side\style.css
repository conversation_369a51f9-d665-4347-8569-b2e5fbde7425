@import url("https://fonts.googleapis.com/css2?family=Roboto&display=swap");

html,body {
	margin: 0;
	padding: 0;
	color: #fff;
	cursor: default;
	font-size: 11px;
	overflow: hidden;
	font-family: "Roboto";
	box-sizing: border-box;
	background: transparent;
}

:focus { outline: 0; }
u { text-decoration: none; }
::-webkit-scrollbar { width: 0; }
::-webkit-scrollbar-track { background: transparent; }
::-webkit-scrollbar-thumb { background: transparent; }
::-webkit-scrollbar-thumb:hover { background: transparent; }
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
	-webkit-appearance: none;
	margin: 0;
}

#Chat {
	top: 50px;
	left: 50px;
	z-index: 997;
	max-width: 500px;
	position: absolute;
}

#ChatMessage {
	float: left;
	margin: 20px;
	width: 460px;
	height: 260px;
	display: none;
	font-size: 13px;
	overflow: hidden;
	overflow-y: scroll;
	letter-spacing: 2px;
	text-shadow: 1px 1px #000;
}

#ChatMessage div {
	margin-bottom: 15px;
}

#ChatMessage div:last-child {
	margin-bottom: 0;
}

#ChatMessage red {
	color: #ff0000;
	font-size: 16px;
	text-shadow: none;
}

#ChatMessage black {
	color: #666;
	font-size: 16px;
	text-shadow: none;
}

#ChatSubmit {
	border: 0;
	float: left;
	color: #fff;
	width: 500px;
	padding: 20px;
	display: none;
	cursor: default;
	font-size: 13px;
	margin-top: 15px;
	letter-spacing: 2px;
	background: #141414;
}

#ChatBackground {
	float: left;
	width: 500px;
	height: 300px;
	background: transparent;
}