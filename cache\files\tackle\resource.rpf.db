[{"n": "G:/[BACKUP B13]/TESTES/resources//[scripts]/tackle/@vrp/config/Native.lua", "mt": 0, "s": 18446744073709551615, "i": "AAAAAAAAAAAAAAAAAAAAAA=="}, {"n": "G:/[BACKUP B13]/TESTES/resources//[scripts]/tackle/@vrp/lib/Utils.lua", "mt": 0, "s": 18446744073709551615, "i": "AAAAAAAAAAAAAAAAAAAAAA=="}, {"n": "G:/[BACKUP B13]/TESTES/resources//[scripts]/tackle/client-side/core.lua", "mt": 1686344099, "s": 2191, "i": "shUCAAAAAgAAAAAAAAAAAA=="}, {"n": "G:/[BACKUP B13]/TESTES/resources//[scripts]/tackle/fxmanifest.lua", "mt": 1686344099, "s": 193, "i": "sBUCAAAAAgAAAAAAAAAAAA=="}]