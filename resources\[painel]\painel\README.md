# Painel Administrativo - Creative Network

## 📋 Descrição
Painel administrativo completo e moderno para o framework VRP do Creative Network, desenvolvido com interface responsiva e funcionalidades avançadas de administração.

## 🚀 Funcionalidades

### 📊 Dashboard
- Estatísticas em tempo real
- Contadores de jogadores online
- Informações sobre veículos spawned
- Advertências ativas
- Baús disponíveis
- Ações rápidas

### 👥 Gerenciamento de Jogadores
- Lista de jogadores online
- Busca por nome, ID ou telefone
- Teleporte (ir até jogador / trazer jogador)
- Sistema de punições (kick, ban, advertência)
- Gerenciamento de itens e dinheiro
- Sistema de grupos e permissões
- Reviver jogadores
- Espectatar jogadores
- Congelar/descongelar jogadores
- Limpar inventário

### 🚗 Gerenciamento de Veículos
- Spawnar veículos por nome
- Lista de veículos disponíveis
- Busca de veículos
- Reparar veículo atual
- Deletar veículo atual

### 📢 Sistema de Anúncios
- Enviar anúncios para todos os jogadores
- Configurar tempo de exibição
- Interface intuitiva

### ⚠️ Sistema de Advertências
- Visualizar advertências ativas
- Deletar advertências
- Histórico completo
- Informações detalhadas (admin, jogador, data, motivo)

### 📦 Gerenciamento de Baús
- Lista de todos os baús
- Informações de peso e permissões
- Abrir baús remotamente
- Interface visual moderna

## 🎨 Interface
- Design moderno e responsivo
- Tema escuro profissional
- Ícones Font Awesome
- Animações suaves
- Layout adaptável para diferentes resoluções
- Modal interativo para ações de jogadores

## 🔧 Instalação

1. A pasta `[painel]` já está na pasta `resources` do seu servidor
2. O `start [painel]` já está adicionado no seu `server.cfg`
3. Reinicie o servidor

## 🎮 Como Usar

### Abrir o Painel
- **Tecla:** F10
- **Comando:** `/painel`
- **Permissão:** Grupo Admin

### Navegação
- Use o menu lateral para navegar entre as seções
- Clique nos cards de jogadores para abrir o modal de ações
- Use as barras de busca para filtrar conteúdo
- Pressione ESC para fechar o painel

### Ações de Jogadores
1. Clique em um jogador na lista
2. Use as abas do modal para diferentes ações:
   - **Teleporte:** Ir até o jogador ou trazê-lo
   - **Itens:** Dar itens ou limpar inventário
   - **Dinheiro:** Adicionar dinheiro ao banco
   - **Grupos:** Definir ou remover grupos
   - **Punições:** Kick, ban ou advertir
   - **Outros:** Reviver, espectatar, congelar

## 🔐 Permissões
- Apenas jogadores com permissão `Admin` podem usar o painel
- Todas as ações são logadas no Discord (se configurado)
- Sistema de hierarquia respeitado

## 📝 Logs
Todas as ações administrativas são registradas com:
- Nome do administrador
- Ação realizada
- Jogador alvo (quando aplicável)
- Data e hora
- Motivo (quando aplicável)

## 🎯 Recursos Técnicos
- **Frontend:** HTML5, CSS3, JavaScript ES6+
- **Backend:** Lua (FiveM)
- **Framework:** VRP
- **Banco de Dados:** MySQL (OxMySQL)
- **Interface:** NUI (CEF)

## 🔄 Atualizações Automáticas
- Dados atualizados em tempo real
- Botões de refresh em cada seção
- Sincronização automática entre administradores

## 📱 Responsividade
- Funciona em diferentes resoluções
- Layout adaptável para telas menores
- Interface otimizada para uso em jogo

## 🛠️ Personalização
O painel pode ser facilmente personalizado:
- Cores e temas no arquivo `style.css`
- Funcionalidades no arquivo `script.js`
- Ações server-side nos arquivos Lua

## 🐛 Suporte
Para suporte ou dúvidas:
- Verifique os logs do servidor
- Confirme as permissões do usuário
- Teste as conexões de banco de dados

## 📄 Licença
Este painel foi desenvolvido especificamente para o Creative Network e está integrado ao framework VRP existente.

---

**Desenvolvido para Creative Network**  
*Painel Administrativo Profissional v1.0*
