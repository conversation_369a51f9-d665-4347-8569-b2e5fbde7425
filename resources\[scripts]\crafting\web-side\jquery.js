var selectCraft = "selectCraft";

window.addEventListener("message",function(event){
	switch (event.data.action){
		case "showNUI":
			selectCraft = event.data.name;
			$(".inventory").css("display","flex");
			requestCrafting();
		break;

		case "hideNUI":
			$(".inventory").css("display","none");
			$(".ui-tooltip").hide();
		break;

		case "requestCrafting":
			requestCrafting();
		break;
	}
});

$(document).ready(function(){
	document.onkeyup = (data) => {
		if (data["key"] === "Escape"){
			$.post("http://crafting/invClose");
		}
	};
});

const updateDrag = () => {
	$(".populated").draggable({
		helper: "clone"
	});

	$(".empty").droppable({
		hoverClass: "hoverControl",
		drop: function (event,ui){
			if(ui.draggable.parent()[0] == undefined) return;

			const shiftPressed = event.shiftKey;
			const origin = ui.draggable.parent()[0].className;
			if (origin === undefined) return;
			const tInv = $(this).parent()[0].className;

			itemData = { key: ui.draggable.data("item-key"), slot: ui.draggable.data("slot") };
			const target = $(this).data("slot");

			if (itemData.key === undefined || target === undefined) return;

			if (tInv === "invLeft"){
				if (origin === "invLeft"){
					itemData = { key: ui.draggable.data("item-key"), slot: ui.draggable.data("slot") };
					const target = $(this).data("slot");

					if (itemData.key === undefined || target === undefined) return;
					let amount = $(".amount").val();
					if (shiftPressed) amount = ui.draggable.data("amount");

					$.post("http://crafting/populateSlot",JSON.stringify({
						item: itemData.key,
						slot: itemData.slot,
						target: target,
						amount: parseInt(amount)
					}));

					$(".amount").val("");
				} else if (origin === "invRight"){
					itemData = { key: ui.draggable.data("item-key") };
					const target = $(this).data("slot");

					if (itemData.key === undefined || target === undefined) return;

					$.post("http://crafting/functionCraft",JSON.stringify({
						craft: selectCraft,
						index: itemData.key,
						slot: target,
						amount: parseInt($(".amount").val())
					}));

					$(".amount").val("");
				}
			} else if (tInv === "invRight"){
				if (origin === "invLeft"){
					itemData = { key: ui.draggable.data("item-key"), slot: ui.draggable.data("slot") };

					if (itemData.key === undefined) return;
					let amount = $(".amount").val();
					if (shiftPressed) amount = ui.draggable.data("amount");

					$.post("http://crafting/functionDestroy",JSON.stringify({
						craft: selectCraft,
						index: itemData.key,
						slot: itemData.slot,
						amount: parseInt(amount)
					}));

					$(".amount").val("");
				}
			}
		}
	});

	$(".populated").droppable({
		hoverClass: "hoverControl",
		drop: function (event,ui){
			if(ui.draggable.parent()[0] == undefined) return;

			const shiftPressed = event.shiftKey;
			const origin = ui.draggable.parent()[0].className;
			if (origin === undefined) return;
			const tInv = $(this).parent()[0].className;

			itemData = { key: ui.draggable.data("item-key"), slot: ui.draggable.data("slot") };
			const target = $(this).data("slot");

			if (itemData.key === undefined || target === undefined) return;

			if (tInv === "invLeft"){
				if (origin === "invLeft"){
					itemData = { key: ui.draggable.data("item-key"), slot: ui.draggable.data("slot") };
					const target = $(this).data("slot");

					if (itemData.key === undefined || target === undefined) return;
					let amount = $(".amount").val();
					if (shiftPressed) amount = ui.draggable.data("amount");

					$.post("http://crafting/updateSlot",JSON.stringify({
						item: itemData.key,
						slot: itemData.slot,
						target: target,
						amount: parseInt(amount)
					}));

					$(".amount").val("");
				} else if (origin === "invRight"){
					itemData = { key: ui.draggable.data("item-key") };
					const target = $(this).data("slot");

					if (itemData.key === undefined || target === undefined || itemData.key !== $(this).data("item-key")) return;

					$.post("http://crafting/functionCraft",JSON.stringify({
						craft: selectCraft,
						index: itemData.key,
						slot: target,
						amount: parseInt($(".amount").val())
					}));

					$(".amount").val("");
				}
			} else if (tInv === "invRight"){
				if (origin === "invLeft"){
					itemData = { key: ui.draggable.data("item-key"), slot: ui.draggable.data("slot") };

					if (itemData.key === undefined) return;
					let amount = $(".amount").val();
					if (shiftPressed) amount = ui.draggable.data("amount");

					$.post("http://crafting/functionDestroy",JSON.stringify({
						craft: selectCraft,
						index: itemData.key,
						slot: itemData.slot,
						amount: parseInt(amount)
					}));

					$(".amount").val("");
				}
			}
		}
	});

	$(".populated").tooltip({
		create: function(event,ui){
			var max = $(this).attr("data-max");
			var economy = $(this).attr("data-economy");
			var description = $(this).attr("data-description");
			var name = $(this).attr("data-name-key");
			var recipe = $(this).attr("data-list");

			$(this).tooltip({
				content: `<item>${name}</item>${description !== "false" ? "<br><description>"+description+"</description>":""}<br><legenda>Economia: <r>$${economy}</r> <s>|</s> Máximo: <r>${max !== "false" ? max:"S/L"}</r></legenda><br><legenda>${recipe}</legenda>`,
				position: { my: "center top+10", at: "center bottom", collision: "flipfit" },
				show: { duration: 10 },
				hide: { duration: 10 }
			})
		}
	});
};

const colorPicker = (percent) => {
	var colorPercent = "#2e6e4c";

	if (percent >= 100)
		colorPercent = "rgba(255,255,255,0)";

	if (percent >= 51 && percent <= 75)
		colorPercent = "#fcc458";

	if (percent >= 26 && percent <= 50)
		colorPercent = "#fc8a58";

	if (percent <= 25)
		colorPercent = "#fc5858";

	return colorPercent;
}

const requestCrafting = () => {
	$.post("http://crafting/requestCrafting",JSON.stringify({ craft: selectCraft }),(data) => {
		$("#weightTextLeft").html(`${(data["invPeso"]).toFixed(2)}   /   ${(data["invMaxpeso"]).toFixed(2)}`);

		$("#weightBarLeft").html(`<div id="weightContent" style="width: ${data["invPeso"] / data["invMaxpeso"] * 100}%"></div>`);

		$(".invLeft").html("");
		$(".invRight").html("");

		if (data["invMaxpeso"] > 100)
			data["invMaxpeso"] = 100;

		const nameList2 = data.inventoryCraft.sort((a,b) => a.name > b.name ? 1 : -1);

		for (let x = 1; x <= data["invMaxpeso"]; x++){
			const slot = x.toString();

			if (data.inventario[slot] !== undefined){
				const v = data.inventario[slot];
				var maxDurability = 86400 * v["days"];
				var newDurability = (maxDurability - v["durability"]) / maxDurability;
				var actualPercent = newDurability * 100;

				if (v["charges"] !== undefined)
					actualPercent = v["charges"];

				if (actualPercent <= 1)
					actualPercent = 1;

				const item = `<div class="item populated" style="background-image: url('nui://inventory/web-side/images/${v.index}.png'); background-position: center; background-repeat: no-repeat;" data-item-key="${v.key}" data-name-key="${v.name}" data-amount="${v.amount}" data-slot="${slot}" data-max="${v["max"]}" data-economy="${v["economy"]}">
					<div class="top">
						<div class="itemWeight">${(v.peso * v.amount).toFixed(2)}</div>
						<div class="itemAmount">${formatarNumero(v.amount)}x</div>
					</div>

					<div class="durability" style="width: ${actualPercent == 1 ? "100":actualPercent}%; background: ${actualPercent == 1 ? "#fc5858":colorPicker(actualPercent)};"></div>
					<div class="nameItem">${v.name}</div>
				</div>`;

				$(".invLeft").append(item);
			} else {
				const item = `<div class="item empty" data-slot="${slot}"></div>`;

				$(".invLeft").append(item);
			}
		}

		for (let x = 1; x <= 50; x++){
			const slot = x.toString();

			if (nameList2[x - 1] !== undefined){
				const v = nameList2[x - 1];
				let list = "";

				for (let i in v.list){
					list = `${list}${v.list[i].amount}x ${v.list[i].name}<br>`;
				}

				list = list.substring(0,list.length - 2);
				const item = `<div class="item populated" title="" style="background-image: url('nui://inventory/web-side/images/${v.index}.png'); background-position: center; background-repeat: no-repeat;" data-item-key="${v.key}" data-name-key="${v.name}" data-list="${list}" data-slot="${slot}" data-description="${v["desc"]}" data-max="${v["max"]}" data-economy="${v["economy"]}">
					<div class="top">
						<div class="itemWeight">${v.peso.toFixed(2)}</div>
						<div class="itemAmount">${formatarNumero(v.amount)}x</div>
					</div>

					<div class="durability"></div>
					<div class="nameItem">${v.name}</div>
				</div>`;

				$(".invRight").append(item);
			} else {
				const item = `<div class="item empty" data-slot="${slot}"></div>`;

				$(".invRight").append(item);
			}
		}
		updateDrag();
	});
};

const formatarNumero = (n) => {
	var n = n.toString();
	var r = "";
	var x = 0;

	for (var i = n.length; i > 0; i--){
		r += n.substr(i - 1, 1) + (x == 2 && i != 1 ? "." : "");
		x = x == 2 ? 0 : x + 1;
	}

	return r.split("").reverse().join("");
};