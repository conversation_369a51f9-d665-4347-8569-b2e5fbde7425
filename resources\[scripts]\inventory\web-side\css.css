::-webkit-scrollbar { width: 6px; }
::-webkit-scrollbar-track { background: rgba(15,15,15,.75); }
::-webkit-scrollbar-thumb { background: rgba(150,150,150,.75); }
::-webkit-scrollbar-thumb:hover { background: rgba(150,150,150,.75); }
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button { -webkit-appearance: none; }

body {
	margin: 0;
	padding: 0;
	color: #fff;
	font-family: "Roboto";
}

* {
	overflow: hidden;
	user-select: none;
	box-sizing: border-box;
}

.inventory {
	width: 100vw;
	height: 100vh;
	display: none;
	position: fixed;
	align-items: center;
	flex-direction: column;
	justify-content: center;
	background: rgba(0,0,0,0.55);
}

.innerInventory {
	display: flex;
	flex-direction: column;
}

.sections {
	height: 589px;
	display: flex;
}

.invLeft, .invRight {
	row-gap: 3px;
	display: grid;
	column-gap: 3px;
	overflow-y: scroll;
	padding-right: 3px;
	grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
}

.item {
	width: 115px;
	height: 145px;
	display: flex;
	flex-direction: column;
	text-shadow: 1px 1px #000;
	background: rgba(15,15,15,.5);
}

.populated {
	background: rgba(15,15,15,.75);
	background-size: 86% !important;
}

.populated:hover, .hoverControl {
	background: rgba(15,15,15,.90);
}

.item .top {
	color: #999;
	display: flex;
	font-size: .70rem;
	justify-content: space-between;
}

.invLeft > .item:nth-child(1){
	background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' version='1.1' height='145px' width='115px'><text x='38' y='100' font-size='75' fill='gray' opacity='.5' font-family='Roboto, Arial, Helvetica, sans-serif'>1</text></svg>");
}

.invLeft > .item:nth-child(2){
	background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' version='1.1' height='145px' width='115px'><text x='37' y='100' font-size='75' fill='gray' opacity='.5' font-family='Roboto, Arial, Helvetica, sans-serif'>2</text></svg>");
}

.invLeft > .item:nth-child(3){
	background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' version='1.1' height='145px' width='115px'><text x='36' y='100' font-size='75' fill='gray' opacity='.5' font-family='Roboto, Arial, Helvetica, sans-serif'>3</text></svg>");
}

.invLeft > .item:nth-child(4){
	background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' version='1.1' height='145px' width='115px'><text x='38' y='100' font-size='75' fill='gray' opacity='.5' font-family='Roboto, Arial, Helvetica, sans-serif'>4</text></svg>");
}

.invLeft > .item:nth-child(5){
	background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' version='1.1' height='145px' width='115px'><text x='36' y='100' font-size='75' fill='gray' opacity='.5' font-family='Roboto, Arial, Helvetica, sans-serif'>5</text></svg>");
}

.itemAmount {
	margin: 15px 15px 0 0;
}

.itemWeight {
	margin: 15px 0 0 15px;
}

.item .nameItem {
	align-self: center;

	width: 100%;
	color: #ccc;

	padding: 8px 10px;
	font-size: .68rem;
	text-align: center;
	white-space: nowrap;
	letter-spacing: 1px;
	text-overflow: ellipsis;
	text-transform: uppercase;

	background: #111;
}

.item .durability {
	width: 100%;
	height: 3px;
	margin-top: auto;
	background: #2e6e4c;
}

.invMiddle {
	display: flex;
	align-items: center;
	flex-direction: column;
}

input {
	text-align: center;
}

input::placeholder {
	color: #ccc;
}

.invMiddle div, .invMiddle input {
	border: 0;
	width: 70%;
	outline: none;
	color: #ccc;
	padding: 16px 0;
	font-size: 12px;
	text-align: center;
	margin-bottom: 12px;
	letter-spacing: 1px;
	background: rgba(15,15,15,.75);
}

#weight {
	max-width: 596px;
}

#weightTextLeft {
	float: left;
	color: #ccc;
	font-size: 12px;
	min-width: 580px;
	max-width: 580px;
	text-align: right;
	margin-right: 15px;
	margin-bottom: 15px;
	letter-spacing: 1px;
}

#weightBarLeft {
	float: left;
	min-height: 10px;
	max-height: 10px;
	min-width: 596px;
	max-width: 596px;
	margin-bottom: 5px;
	background: rgba(15,15,15,.75);
}

#weightTextRight {
	float: right;
	color: #9c9c9c;
	font-size: 12px;
	min-width: 580px;
	max-width: 580px;
	text-align: left;
	margin-left: 15px;
	margin-bottom: 15px;
	letter-spacing: 1px;
}

#weightBarRight {
	float: right;
	min-height: 10px;
	max-height: 10px;
	min-width: 596px;
	max-width: 596px;
	margin-bottom: 5px;
	background: rgba(15,15,15,.75);
}

#weightContent {
	float: left;
	min-height: 10px;
	max-height: 10px;
	max-width: 596px;
	background: #fec026;
	transition: all 0.9s;
}

.ui-helper-hidden-accessible {
	display: none;
}

.ui-tooltip {
	color: #ccc;
	z-index: 9999;
	font-size: 11px;
	max-width: 400px;
	position: absolute;
	letter-spacing: 1px;
	padding: 20px 20px 15px;
	background: rgba(15,15,15,.75);
}

.ui-tooltip:hover {
	display: none;
}

.ui-tooltip item {
	float: left;
	font-size: 18px;
	min-width: 225px;
	letter-spacing: 2px;
}

.ui-tooltip legenda {
	float: left;
	max-width: 250px;
	min-width: 250px;
	margin-top: 10px;
	padding-top: 10px;
	line-height: 20px;
	letter-spacing: 2px;
	border-top: 1px solid #222;
}

.ui-tooltip b {
	font-weight: 300;
}

.ui-tooltip description {
	float: left;
	color: #999;
	font-size: 10px;
	margin-top: 5px;
	max-width: 250px;
	min-width: 250px;
}

.ui-tooltip description green {
	color: #b8f5d4;
}

.ui-tooltip s {
	color: #666;
	padding: 0 5px;
	text-decoration: none;
}

.ui-tooltip r {
	color: #666;
}