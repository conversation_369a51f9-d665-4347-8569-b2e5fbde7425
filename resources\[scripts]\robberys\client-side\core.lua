-----------------------------------------------------------------------------------------------------------------------------------------
-- ROBBERYS:INIT
-----------------------------------------------------------------------------------------------------------------------------------------
RegisterNetEvent("robberys:Init")
AddEventHandler("robberys:Init",function(<PERSON><PERSON><PERSON>)
	for Number,v in pairs(<PERSON>bery<PERSON>) do
		exports["target"]:AddCircleZone("Robberys:"..Number,v["Coords"],0.5,{
			name = "Robberys:"..Number,
			heading = 3374176
		},{
			shop = Number,
			Distance = 1.0,
			options = {
				{
					event = "robberys:Init",
					tunnel = "server",
					label = "Roubar"
				}
			}
		})
	end
end)