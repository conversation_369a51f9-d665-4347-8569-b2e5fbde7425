-----------------------------------------------------------------------------------------------------------------------------------------
-- VRP
-----------------------------------------------------------------------------------------------------------------------------------------
local Proxy = module("vrp","lib/Proxy")
vRP = Proxy.getInterface("vRP")
-----------------------------------------------------------------------------------------------------------------------------------------
-- REDUCES
-----------------------------------------------------------------------------------------------------------------------------------------
local reduceList = {
	["1"] = { 1698.86,2472.69,45.56 },
	["2"] = { 1698.48,2472.36,45.56 },
	["3"] = { 1635.66,2490.23,45.56 },
	["4"] = { 1634.63,2490.1,45.56 },
	["5"] = { 1618.41,2521.55,45.56 },
	["6"] = { 1607.39,2541.39,45.56 },
	["7"] = { 1606.3,2542.63,45.56 },
	["8"] = { 1624.83,2567.86,45.56 },
	["9"] = { 1624.78,2567.12,45.56 },
	["10"] = { 1643.77,2565.0,45.56 },
	["11"] = { 1665.05,2567.68,45.56 },
	["12"] = { 1715.97,2567.16,45.56 },
	["13"] = { 1715.97,2567.95,45.56 },
	["14"] = { 1716.02,2568.78,45.56 },
	["15"] = { 1768.79,2565.76,45.56 },
	["16"] = { 1769.77,2565.7,45.56 },
	["17"] = { 1772.72,2536.83,45.56 },
	["18"] = { 1758.19,2508.99,45.56 },
	["19"] = { 1757.87,2507.77,45.56 },
	["20"] = { 1719.9,2502.66,45.56 },
	["21"] = { 1695.28,2506.62,45.56 },
	["22"] = { 1663.84,2515.34,45.56 },
	["23"] = { 1664.44,2516.1,45.56 },
	["24"] = { 1628.63,2543.64,45.56 },
	["25"] = { 1636.16,2553.61,45.56 },
	["26"] = { 1648.41,2536.3,45.56 },
	["27"] = { 1657.61,2549.28,45.56 },
	["28"] = { 1649.78,2538.35,45.56 },
	["29"] = { 1699.18,2535.8,45.56 },
	["30"] = { 1699.57,2534.67,45.56 },
	["31"] = { 1699.39,2532.1,45.56 },
	["32"] = { 1777.42,2560.84,45.66 },
	["33"] = { 1784.21,2561.16,45.66 }
}
-----------------------------------------------------------------------------------------------------------------------------------------
-- THREADSTART
-----------------------------------------------------------------------------------------------------------------------------------------
CreateThread(function()
	for Number,v in pairs(reduceList) do
		exports["target"]:AddCircleZone("Prison:"..Number,vec3(v[1],v[2],v[3]),0.75,{
			name = "Prison:"..Number,
			heading = 3374176
		},{
			shop = Number,
			Distance = 1.0,
			options = {
				{
					event = "police:Reduces",
					tunnel = "server",
					label = "Vasculhar"
				}
			}
		})
	end
end)
-----------------------------------------------------------------------------------------------------------------------------------------
-- POLICE:MDT
-----------------------------------------------------------------------------------------------------------------------------------------
RegisterNetEvent("police:Mdt")
AddEventHandler("police:Mdt",function()
	
end)