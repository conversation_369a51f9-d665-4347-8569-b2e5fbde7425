-----------------------------------------------------------------------------------------------------------------------------------------
-- VRP
-----------------------------------------------------------------------------------------------------------------------------------------
local Tunnel = module("vrp","lib/Tunnel")
-----------------------------------------------------------------------------------------------------------------------------------------
-- CONNECTION
-----------------------------------------------------------------------------------------------------------------------------------------
Creative = {}
Tunnel.bindInterface("deliver",Creative)
-----------------------------------------------------------------------------------------------------------------------------------------
-- VARIABLES
-----------------------------------------------------------------------------------------------------------------------------------------
local Blip = nil
local Locate = ""
local Selected = 1
local Starting = false
-----------------------------------------------------------------------------------------------------------------------------------------
-- INITLIST
-----------------------------------------------------------------------------------------------------------------------------------------
local List = {
	["BurgerShot"] = { -1191.32,-900.39,13.99,1.0,1.0,"Trabalhar",false },
	["PizzaThis"] = { 806.89,-745.59,26.77,0.5,1.0,"Trabalhar",false },
	["UwuCoffee"] = { -594.0,-1052.47,22.34,0.5,1.0,"Trabalhar",false },
	["BeanMachine"] = { 126.68,-1035.54,29.27,0.5,1.0,"Trabalhar",false },
	["Lumberman"] = { 2433.45,5013.46,46.99,0.5,1.0,"Trabalhar",false },
	["Transporter"] = { 229.16,231.91,97.04,0.25,1.0,"Trabalhar",true }
}
-----------------------------------------------------------------------------------------------------------------------------------------
-- THREADSTART
-----------------------------------------------------------------------------------------------------------------------------------------
CreateThread(function()
	for Number,v in pairs(List) do
		exports["target"]:AddCircleZone("Deliver:"..Number,vec3(v[1],v[2],v[3]),v[4],{
			name = "Deliver:"..Number,
			heading = 3374176
		},{
			shop = Number,
			Distance = v[5],
			options = {
				{
					event = "deliver:Starting",
					tunnel = "shop",
					label = v[6]
				}
			}
		})
	end
end)
-----------------------------------------------------------------------------------------------------------------------------------------
-- CDS
-----------------------------------------------------------------------------------------------------------------------------------------
local Cds = {
	["BurgerShot"] = {
		{ -30.67,-346.94,46.52 },
		{ -197.36,-831.38,30.75 },
		{ -284.41,-600.6,33.55 },
		{ -287.26,-1060.19,27.2 },
		{ -170.71,-1449.71,31.64 },
		{ 87.57,-1670.53,29.18 },
		{ 218.1,-1838.83,27.33 },
		{ 379.52,-1781.49,29.47 },
		{ 561.39,-1751.82,29.28 },
		{ 431.69,-2035.13,23.32 },
		{ -421.75,-2171.55,11.34 },
		{ -621.03,-1639.95,26.35 },
		{ -697.59,-1182.27,10.72 },
		{ -1031.3,-902.98,3.69 },
		{ -1499.69,-933.09,10.18 },
		{ -1918.69,-542.63,11.83 },
		{ -1681.23,-290.87,51.88 },
		{ -1649.77,151.1,62.16 },
		{ -1094.99,427.28,75.87 },
		{ -667.14,471.47,114.14 }
	},
	["PizzaThis"] = {
		{ -30.67,-346.94,46.52 },
		{ -197.36,-831.38,30.75 },
		{ -284.41,-600.6,33.55 },
		{ -287.26,-1060.19,27.2 },
		{ -170.71,-1449.71,31.64 },
		{ 87.57,-1670.53,29.18 },
		{ 218.1,-1838.83,27.33 },
		{ 379.52,-1781.49,29.47 },
		{ 561.39,-1751.82,29.28 },
		{ 431.69,-2035.13,23.32 },
		{ -421.75,-2171.55,11.34 },
		{ -621.03,-1639.95,26.35 },
		{ -697.59,-1182.27,10.72 },
		{ -1031.3,-902.98,3.69 },
		{ -1499.69,-933.09,10.18 },
		{ -1918.69,-542.63,11.83 },
		{ -1681.23,-290.87,51.88 },
		{ -1649.77,151.1,62.16 },
		{ -1094.99,427.28,75.87 },
		{ -667.14,471.47,114.14 }
	},
	["UwuCoffee"] = {
		{ -30.67,-346.94,46.52 },
		{ -197.36,-831.38,30.75 },
		{ -284.41,-600.6,33.55 },
		{ -287.26,-1060.19,27.2 },
		{ -170.71,-1449.71,31.64 },
		{ 87.57,-1670.53,29.18 },
		{ 218.1,-1838.83,27.33 },
		{ 379.52,-1781.49,29.47 },
		{ 561.39,-1751.82,29.28 },
		{ 431.69,-2035.13,23.32 },
		{ -421.75,-2171.55,11.34 },
		{ -621.03,-1639.95,26.35 },
		{ -697.59,-1182.27,10.72 },
		{ -1031.3,-902.98,3.69 },
		{ -1499.69,-933.09,10.18 },
		{ -1918.69,-542.63,11.83 },
		{ -1681.23,-290.87,51.88 },
		{ -1649.77,151.1,62.16 },
		{ -1094.99,427.28,75.87 },
		{ -667.14,471.47,114.14 }
	},
	["BeanMachine"] = {
		{ -30.67,-346.94,46.52 },
		{ -197.36,-831.38,30.75 },
		{ -284.41,-600.6,33.55 },
		{ -287.26,-1060.19,27.2 },
		{ -170.71,-1449.71,31.64 },
		{ 87.57,-1670.53,29.18 },
		{ 218.1,-1838.83,27.33 },
		{ 379.52,-1781.49,29.47 },
		{ 561.39,-1751.82,29.28 },
		{ 431.69,-2035.13,23.32 },
		{ -421.75,-2171.55,11.34 },
		{ -621.03,-1639.95,26.35 },
		{ -697.59,-1182.27,10.72 },
		{ -1031.3,-902.98,3.69 },
		{ -1499.69,-933.09,10.18 },
		{ -1918.69,-542.63,11.83 },
		{ -1681.23,-290.87,51.88 },
		{ -1649.77,151.1,62.16 },
		{ -1094.99,427.28,75.87 },
		{ -667.14,471.47,114.14 }
	},
	["Lumberman"] = {
		{ -513.92,-1019.31,23.47 },
		{ -1604.18,-832.26,10.08 },
		{ -536.48,-45.61,42.57 },
		{ -53.01,79.35,71.62 },
		{ 581.16,139.13,99.48 },
		{ 814.39,-93.48,80.6 },
		{ 1106.93,-355.03,67.01 },
		{ 1070.71,-780.46,58.36 },
		{ 1142.82,-986.58,45.91 },
		{ 1200.55,-1276.6,35.23 },
		{ 967.81,-1829.29,31.24 },
		{ 809.16,-2222.61,29.65 },
		{ 684.61,-2741.62,6.02 },
		{ 263.47,-2506.62,6.45 },
		{ 94.66,-2676.38,6.01 },
		{ -43.87,-2519.91,7.4 },
		{ 182.93,-2027.68,18.28 },
		{ -306.86,-2191.84,10.84 },
		{ -570.95,-1775.95,23.19 },
		{ -350.03,-1569.9,25.23 },
		{ -128.36,-1394.12,29.57 },
		{ 67.84,-1399.02,29.37 },
		{ 343.13,-1297.91,32.51 },
		{ 485.92,-1477.41,29.29 },
		{ 139.81,-1337.41,29.21 },
		{ 263.82,-1346.16,31.93 },
		{ -723.33,-1112.41,10.66 },
		{ -842.54,-1128.21,7.02 },
		{ 488.46,-898.56,25.94 }
	},
	["Transporter"] = {
		{ 285.47,143.37,104.17 },
		{ 527.36,-160.7,57.09 },
		{ 1153.64,-326.75,69.2 },
		{ 1167.01,-456.07,66.79 },
		{ 1138.25,-468.9,66.73 },
		{ 1077.71,-776.5,58.23 },
		{ 315.09,-593.65,43.29 },
		{ 296.46,-894.25,29.23 },
		{ 295.76,-896.14,29.22 },
		{ 147.58,-1035.79,29.34 },
		{ 145.93,-1035.19,29.34 },
		{ 289.1,-1256.87,29.44 },
		{ 288.82,-1282.36,29.64 },
		{ 126.85,-1296.59,29.27 },
		{ 127.84,-1296.03,29.27 },
		{ 33.55,-1345.01,29.49 },
		{ 24.48,-945.95,29.35 },
		{ 5.24,-919.83,29.55 },
		{ 112.58,-819.4,31.34 },
		{ 114.44,-776.41,31.41 },
		{ 111.25,-775.25,31.44 },
		{ -27.99,-724.54,44.23 },
		{ -30.19,-723.71,44.23 },
		{ -203.8,-861.37,30.26 },
		{ -301.7,-830.01,32.42 },
		{ -303.24,-829.74,32.42 },
		{ -258.87,-723.38,33.48 },
		{ -256.2,-715.99,33.53 },
		{ -254.41,-692.49,33.6 },
		{ -537.85,-854.49,29.28 },
		{ -660.73,-854.07,24.48 },
		{ -710.01,-818.9,23.72 },
		{ -712.89,-818.92,23.72 },
		{ -717.7,-915.65,19.21 },
		{ -821.63,-1081.88,11.12 },
		{ -1315.71,-834.75,16.95 },
		{ -1314.75,-836.03,16.95 },
		{ -1305.41,-706.37,25.33 },
		{ -1570.14,-546.72,34.95 },
		{ -1571.06,-547.39,34.95 },
		{ -1415.94,-212.04,46.51 },
		{ -1430.18,-211.06,46.51 },
		{ -1409.76,-100.47,52.39 },
		{ -1410.32,-98.75,52.42 },
		{ -1282.52,-210.92,42.44 },
		{ -1286.28,-213.44,42.44 },
		{ -1285.54,-224.32,42.44 },
		{ -1289.31,-226.78,42.44 },
		{ -1205.02,-326.3,37.83 },
		{ -1205.78,-324.8,37.86 },
		{ -866.69,-187.74,37.84 },
		{ -867.63,-186.07,37.84 },
		{ -846.31,-341.26,38.67 },
		{ -846.81,-340.2,38.67 },
		{ -721.06,-415.58,34.98 },
		{ -556.18,-205.18,38.22 },
		{ -57.66,-92.65,57.78 },
		{ 89.73,2.46,68.29 },
		{ -165.17,232.77,94.91 },
		{ -165.16,234.8,94.91 },
		{ 158.6,234.23,106.63 },
		{ 228.18,338.38,105.56 },
		{ 381.86,326.44,103.56 },
		{ 357.01,173.54,103.07 }
	}
}
-----------------------------------------------------------------------------------------------------------------------------------------
-- DELIVER:STARTING
-----------------------------------------------------------------------------------------------------------------------------------------
RegisterNetEvent("deliver:Starting")
AddEventHandler("deliver:Starting",function(Init)
	if Cds[Init] then
		if Starting then
			Starting = false
			exports["target"]:LabelText("Deliver:"..Init,"Trabalhar")
			TriggerEvent("Notify","verde","Trabalho finalizado.",3000)

			if DoesBlipExist(Blip) then
				RemoveBlip(Blip)
				Blip = nil
			end

			if Locate ~= Init then
				Selected = 1
			end
		else
			if List[Init][7] then
				if Locate ~= Init then
					Selected = 1
				end
			else
				if Locate ~= Init then
					Selected = math.random(#Cds[Init])
				end
			end

			Locate = Init
			Starting = true
			TriggerEvent("Notify","verde","Trabalho iniciado.",3000)
			exports["target"]:LabelText("Deliver:"..Init,"Finalizar")
			Marker(Cds[Locate][Selected][1],Cds[Locate][Selected][2],Cds[Locate][Selected][3])

			while Starting do
				local TimeDistance = 999
				local Ped = PlayerPedId()
				if not IsPedInAnyVehicle(Ped) then
					local Coords = GetEntityCoords(Ped)
					local Vector = vec3(Cds[Locate][Selected][1],Cds[Locate][Selected][2],Cds[Locate][Selected][3])
					local Distance = #(Coords - Vector)

					if Distance <= 15.0 then
						TimeDistance = 1

						if Distance <= 1.0 then
							DrawText(Vector["x"],Vector["y"],Vector["z"],"E N T R E G A R",true)
						else
							DrawText(Vector["x"],Vector["y"],Vector["z"],"E N T R E G A R",false)
						end
					end
				end

				Wait(TimeDistance)
			end
		end
	end
end)
-----------------------------------------------------------------------------------------------------------------------------------------
-- DELIVER
-----------------------------------------------------------------------------------------------------------------------------------------
function Creative.Deliver(Service)
	if Starting and Service == Locate then
		local Ped = PlayerPedId()
		local Coords = GetEntityCoords(Ped)
		local Vector = vec3(Cds[Locate][Selected][1],Cds[Locate][Selected][2],Cds[Locate][Selected][3])
		local Distance = #(Coords - Vector)

		if Distance <= 1.0 then
			return true
		end
	end

	return false
end
-----------------------------------------------------------------------------------------------------------------------------------------
-- UPDATE
-----------------------------------------------------------------------------------------------------------------------------------------
function Creative.Update()
	if List[Locate][7] then
		if Selected >= #Cds[Locate] then
			Selected = 1
		else
			Selected = Selected + 1
		end
	else
		Selected = math.random(#Cds[Locate])
	end

	Marker(Cds[Locate][Selected][1],Cds[Locate][Selected][2],Cds[Locate][Selected][3])
end
-----------------------------------------------------------------------------------------------------------------------------------------
-- DRAWTEXT
-----------------------------------------------------------------------------------------------------------------------------------------
function DrawText(x,y,z,text,color)
	local onScreen,_x,_y = GetScreenCoordFromWorldCoord(x,y,z)

	if onScreen then
		BeginTextCommandDisplayText("STRING")
		AddTextComponentSubstringKeyboardDisplay(text)

		if color then
			SetTextColour(150,196,172,255)
		else
			SetTextColour(204,204,204,175)
		end

		SetTextScale(0.35,0.35)
		SetTextFont(4)
		SetTextCentre(1)
		EndTextCommandDisplayText(_x,_y)

		local width = string.len(text) / 300

		if color then
			DrawRect(_x,_y + 0.0125,width,0.03,162,124,219,200)
		else
			DrawRect(_x,_y + 0.0125,width,0.03,15,15,15,200)
		end
	end
end
-----------------------------------------------------------------------------------------------------------------------------------------
-- MARKER
-----------------------------------------------------------------------------------------------------------------------------------------
function Marker(x,y,z)
	if DoesBlipExist(Blip) then
		RemoveBlip(Blip)
		Blip = nil
	end

	Blip = AddBlipForCoord(x,y,z)
	SetBlipSprite(Blip,1)
	SetBlipColour(Blip,2)
	SetBlipScale(Blip,0.5)
	SetBlipRoute(Blip,true)
	SetBlipAsShortRange(Blip,true)
	BeginTextCommandSetBlipName("STRING")
	AddTextComponentString("Entrega do "..Locate)
	EndTextCommandSetBlipName(Blip)
end