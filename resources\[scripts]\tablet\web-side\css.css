body,td,th { font-size: 12px; font-family: Arial, Helvetica, sans-serif; color: #666; cursor: default; vertical-align: baseline; outline: 0; line-height: 1; margin: 0; padding: 0; word-wrap: break-word; }
a,a:link,a:visited,a:active,a:hover { font-family: Arial, Helvetica, sans-serif; font-size: 12px; color: #666; text-decoration: none; }
div,span,applet,object,iframe,p,blockquote,pre,a,abbr,acronym,address,big,cite,code,del,dfn,em,font,img,ins,kbd,q,s,samp,small,strike,strong,sub,sup,tt,var,b,u,i,center,dl,dt,dd,ol,ul,li,fieldset,form,label,legend,table,caption,tbody,tfoot,thead,tr { border:0; outline:0; font-size:100%; vertical-align:baseline; background:transparent; margin:0; padding:0; }
:focus { outline:0; }
img { border:none; }
::-webkit-scrollbar { width: 0; }
::selection { background: transparent; color: #666; }
::-moz-selection { background: transparent; color: #666; }
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button { -webkit-appearance: none; }
/* ---------------------------------------------------------------------------------------------------------------- */
#mainPage {
	top: 50%;
	left: 50%;
	width: 1200px;
	height: 700px;
	display: none;
	overflow: auto;
	position: absolute;
	border: 7px solid #111;
	transform: translate(-50%,-50%);
	background: #ccc url("images/background.png") no-repeat;
}
/* ---------------------------------------------------------------------------------------------------------------- */
#header {
	float: left;
	width: 1200px;
	height: 126px;
}
/* ---------------------------------------------------------------------------------------------------------------- */
#mainMenu {
	float: left;
	width: 1140px;
	padding: 0 30px;
	background: #1a1a1a;
}

#mainMenu li {
	float: left;
	color: #999;
	font-weight: 700;
	margin-right: 3px;
	padding: 21px 20px;
	list-style-type: none;
	letter-spacing: 0.7px;
	text-transform: uppercase;
	text-shadow: 0 1px rgba(0,0,0,0.5);
}

#mainMenu li:hover {
	color: #555;
	padding: 21px 20px;
	background: #cfcfcf;
	text-shadow: 0 0 rgba(0,0,0,0.5);
}

#mainMenu li.active {
	color: #555;
	margin-top: -5px;
	background: #cfcfcf;
	padding: 24px 20px 23px;
	text-shadow: 0 0 rgba(0,0,0,0.5);
}

#mainMenu li::selection {
	color: #999;
}

#mainMenu li.active::selection, #mainMenu li:hover::selection {
	color: #555;
}
/* ---------------------------------------------------------------------------------------------------------------- */
#content {
	float: left;
	width: 1140px;
	height: 460px;
	margin: 30px;
	overflow: auto;
}
/* ---------------------------------------------------------------------------------------------------------------- */
#titleContent {
	float: left;
	width: 1125px;
	font-size: 20px;
	font-weight: 700;
	letter-spacing: 1px;
	margin-bottom: 15px;
	padding: 5px 0 10px 15px;
	text-transform: uppercase;
	border-bottom: 1px dotted #999;
}

#pageContent {
	float: left;
	width: 1110px;
	margin: 0 15px;
	font-size: 13px;
	line-height: 25px;
}

#divContent {
	float: left;
	height: 30px;
	width: 1140px;
}

s {
	color: #a94987;
	text-decoration: none;
}
/* ---------------------------------------------------------------------------------------------------------------- */
#pageDiv {
	float: left;
	width: 365px;
	line-height: 25px;
	padding: 0 0 0 15px;
}
/* ---------------------------------------------------------------------------------------------------------------- */
#pageDiv2 {
	float: left;
	width: 262px;
	font-size: 13px;
	line-height: 25px;
	padding: 0 0 0 15px;
}
/* ---------------------------------------------------------------------------------------------------------------- */
#benefactorBar {
	width: 1140px;
	padding: 0 30px;
	background: #222;
	margin-top: -30px;
	margin-left: -30px;
	position: absolute;
}

#benefactorBar li {
	float: left;
	color: #999;
	font-weight: 700;
	padding: 15px 20px;
	letter-spacing: 0.7px;
	list-style-type: none;
	text-shadow: 0 0 rgba(0,0,0,0.5);
}

#benefactorBar li:hover {
	color: #ccc;
	background: #444;
}

#benefactorBar li.active {
	color: #ccc;
	background: #666;
}
/* ---------------------------------------------------------------------------------------------------------------- */
#titleVehicles {
	float: left;
	width: 1125px;
	font-size: 20px;
	font-weight: 700;
	margin-top: -46px;
	letter-spacing: 1px;
	margin-bottom: 15px;
	padding: 5px 0 10px 15px;
	text-transform: uppercase;
	position: absolute !important;
	border-bottom: 1px dotted #999;
}

#contentVehicles {
	float: left;
	width: 1140px;
	margin-top: 88px;
	max-height: 372px;
	min-height: 372px;
	overflow-y: scroll;
}

#typeSearch {
	top: 263px;
	right: 35px;
	color: #999999;
	font-size: 13px;
	position: absolute;
}

#typeSearch span {
	text-transform: uppercase;
	letter-spacing: 2px;
	padding: 5px 10px;
	color: #666;
}

#pageVehicles {
	row-gap: 6px;
	display: grid;
	column-gap: 6px;
	min-width: 1140px;
	max-width: 1140px;
	grid-template-columns: 1fr 1fr 1fr 1fr;
}

#pageVehicles span {
	width: 250px;
	padding: 15px;
	background: #fff;
	line-height: 21px;
	border-bottom: 1px solid rgba(0,0,0,0.35);
}

#pageVehicles left {
	float: left;
	width: 161px;
	padding-top: 2px;
}

#pageVehicles right {
	float: left;
	width: 89px;
}

#benefactorBuy, #benefactorRental {
	float: left;
	width: 89px;
	color: #95d1b1;
	padding: 6px 0 4px;
	text-align: center;
	margin-bottom: 3px;
	letter-spacing: 1px;
	background: #2e6e4c;
}

#benefactorBuy:hover, #benefactorRental:hover {
	background: #255a3e;
	transition: all 0.9s;
}

i {
	float: left;
	width: 146px;
	display: block;
	font-size: 14px;
	font-weight: 700;
	font-style: normal;
	margin-bottom: 5px;
	margin-right: 15px;
	padding-bottom: 5px;
	white-space: nowrap;
	text-overflow: ellipsis;
	border-bottom: 1px dotted #ccc;
}

#benefactorDrive {
	float: left;
	width: 89px;
	color: #f1b2bd;
	margin-top: 3px;
	padding: 6px 0 4px;
	text-align: center;
	letter-spacing: 1px;
	background: #a93b50;
}

#benefactorDrive:hover {
	background: #852e3f;
	transition: all 0.9s;
}