::-webkit-scrollbar { background: transparent; width: 0; }
::selection { background: transparent; }
::-moz-selection { background: transparent; }

* {
	color: #ccc;
	font-size: 13px;
	font-family: "Roboto";
	-webkit-user-select: none;
}

.body {
	top: 15%;
	right: 100px;
	height: 50%;
	width: 300px;
	overflow: hidden;
	position: absolute;
}

.notification {
	float: left;
	width: 270px;
	position: relative;
	margin-bottom: 10px;
	padding: 15px 10px 15px 15px;
	background: rgba(15,15,15,.75);
}

.notification:last-child {
	margin-bottom: 0;
}

.content {
	float: left;
	width: 230px;
}

.buttons {
	float: left;
	width: 30px;
}

.content-line {
	float: left;
	margin-top: 5px;
	min-width: 220px;
	max-width: 220px;
	padding: 4px 0 0 5px;
}

.chamados {
	float: left;
	width: 30px;
	height: 30px;
	margin-top: 3px;
	text-align: center;
}

.chamados svg {
	font-size: 22px;
}

.code {
	float: left;
	color: #715611;
	font-size: 11px;
	padding: 5px 10px;
	margin-right: 10px;
	background: #fec026;
}

.titulo {
	float: left;
	min-width: 167px;
	max-width: 167px;
	overflow: hidden;
	padding: 4px 0 3px;
	white-space: nowrap;
	text-overflow: ellipsis;
	text-shadow: 1px 1px #2c303c;
}

.texto {
	float: left;
	color: #3f3f3f;
	margin-top: 13px;
	min-width: 240px;
	max-width: 240px;
	line-height: 16px;
	padding: 8px 10px;
	background: #c1bebe;
}