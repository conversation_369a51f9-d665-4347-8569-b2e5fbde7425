-----------------------------------------------------------------------------------------------------------------------------------------
-- VRP
-----------------------------------------------------------------------------------------------------------------------------------------
local Tunnel = module("vrp","lib/Tunnel")
local Proxy = module("vrp","lib/Proxy")
vRP = Proxy.getInterface("vRP")
-----------------------------------------------------------------------------------------------------------------------------------------
-- CONNECTION
-----------------------------------------------------------------------------------------------------------------------------------------
vSERVER = Tunnel.getInterface("bank")
-----------------------------------------------------------------------------------------------------------------------------------------
-- ONTHREADSTART
-----------------------------------------------------------------------------------------------------------------------------------------
CreateThread(function()
	SetNuiFocus(false,false)
end)
-----------------------------------------------------------------------------------------------------------------------------------------
-- BANK
-----------------------------------------------------------------------------------------------------------------------------------------
AddEventHandler("Bank",function()
	if LocalPlayer["state"]["Route"] < 900000 then
		if vSERVER.requestWanted() then
			SetNuiFocus(true,true)
			SendNUIMessage({ action = "show" })
			vRP.playAnim(false,{"amb@prop_human_atm@male@idle_a","idle_a"},false)
		end
	end
end)
-----------------------------------------------------------------------------------------------------------------------------------------
-- CLOSE
-----------------------------------------------------------------------------------------------------------------------------------------
RegisterNUICallback("close",function(Data,Callback)
	vRP.removeObjects()
	SetNuiFocus(false,false)
	SendNUIMessage({ action = "hide" })

	Callback("Ok")
end)
-----------------------------------------------------------------------------------------------------------------------------------------
-- DEPOSIT
-----------------------------------------------------------------------------------------------------------------------------------------
RegisterNUICallback("deposit",function(Data,Callback)
	if parseInt(Data["value"]) > 0 then
		vSERVER.bankDeposit(Data["value"])
	end

	Callback("Ok")
end)
-----------------------------------------------------------------------------------------------------------------------------------------
-- WITHDRAW
-----------------------------------------------------------------------------------------------------------------------------------------
RegisterNUICallback("withdraw",function(Data,Callback)
	if parseInt(Data["value"]) > 0 then
		vSERVER.bankWithdraw(Data["value"])
	end

	Callback("Ok")
end)