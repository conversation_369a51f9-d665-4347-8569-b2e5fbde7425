@import url("https://fonts.googleapis.com/css2?family=Roboto&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Jura:wght@600&display=swap");

@font-face {
	font-family: "Gotham";
	src: url("fonts/GothamMedium.ttf") format("truetype"); 
}

html,body {
	margin: 0;
	padding: 0;
	color: #fff;
	cursor: default;
	font-size: 11px;
	overflow: hidden;
	font-family: "Roboto";
	box-sizing: border-box;
	background: transparent;
}

:focus { outline: 0; }
u { text-decoration: none; }
::-webkit-scrollbar { width: 0; }
::-webkit-scrollbar-track { background: transparent; }
::-webkit-scrollbar-thumb { background: transparent; }
::-webkit-scrollbar-thumb:hover { background: transparent; }
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
	-webkit-appearance: none;
	margin: 0;
}

#Body, .Reposed, .Wanted, .Progress {
	display: none;
}

#NaviWeapons {
	top: 125px;
	right: 50px;
	color: #ccc;
	display: none;
	text-align: right;
	position: absolute;
	font-family: "Gotham";
	text-transform: uppercase;
}

.NameWeapon {
	opacity: .5;
	font-size: 14px;
}

.NameAmmos {
	font-size: 20px;
}

#NaviTop {
	top: 50px;
	right: 50px;
	color: #ccc;
	position: absolute;
}

.NameServer {
	float: left;
	margin-left: 30px;
}

.InfoUser {
	float: left;
	font-size: 13px;
	margin-top: 3px;
	text-align: right;
	line-height: 18px;
	letter-spacing: 2px;
	text-shadow: 1px 1px #000;
}

space {
	opacity: .35;
	border-left: 1px;
	padding-left: 5px;
	margin-right: 5px;
	text-shadow: none;
}

#NaviBottom {
	right: 50px;
	bottom: 50px;
	font-size: 16px;
	text-align: right;
	position: absolute;
	text-shadow: 1px 1px #000;
}

.NaviText {
	float: left;
	width: 300px;
	font-size: 14px;
	margin-top: 20px;
	letter-spacing: 1px;
}

.NaviText > span {
	opacity: .5;
	display: block;
	font-size: 11px;
	letter-spacing: 1px;
}

.NaviIcon {
	width: 40px;
	float: left;
	font-size: 22px;
	margin-top: 20px;
	padding-top: 4px;
}

#NaviMiddle {
	width: 100vw;
	height: 100vh;
	display: flex;
	position: absolute;
	align-items: center;
	justify-content: flex-end;
}

#NaviMiddle > .NaviBlock {
	display: block;
	margin-right: 50px;
}

#NaviMiddle > div > div {
	display: block;
	margin-bottom: 20px;
}

#NaviMiddle > div > div:last-child {
	margin-bottom: 0;
}

#NaviMiddle > .NaviBlock > .NaviButton {
	position: relative;
}

.stats > .NaviButton {
	position: relative;
}

#NaviMiddle > .NaviBlock > .NaviButton > svg, .stats > .NaviButton > svg {
	width: 40px;
	height: 40px;
	stroke-dashoffset: 0;
	stroke-dasharray: 100;
	transform: rotate(-90deg);
	-ms-transform: rotate(-90deg);
	-webkit-transform: rotate(-90deg);
}

#NaviMiddle > .NaviBlock > .NaviButton > svg > .Circle, .stats > .NaviButton > svg > .Circle {
	fill: none;
	transition: all 1s;
	stroke-width: 14px;
	stroke-dashoffset: 0;
	stroke-dasharray: 100;
}

.stats > .NaviButton > svg > .Nitro {
	stroke-width: 8px;
	stroke-dashoffset: 100;
}

#NaviMiddle > .NaviBlock > .NaviButton > img, .stats > .NaviButton > img {
	top: 3px;
	left: 3px;
	z-index: 1;
	position: absolute;
}

#NaviMiddle > .NaviBlock > .NaviButton > svg > .Fill, .stats > .NaviButton > svg > .Fill {
	stroke-width: 4px;
	stroke: transparent;
	stroke-dashoffset: 0;
	stroke-dasharray: 100;
	fill: rgba(15,15,15,.75);
}

#Vehicle * {
	padding: 0;
	margin: 0;
	box-sizing: border-box;
	font-family: "Gotham";
	color: white;
}

#Vehicle {
	display: none;
}

.SpeedoBox, .SpeedoBox * {
	font-size: 0.5vw;
}

.SpeedoBox {
	height: 22em;
	width: 20em;
	display: flex;
	justify-content: center;
	position: absolute;
	bottom: 10em;
	right: 5em;
}

.circles {
	height: 19em;
	width: 19em;
	display: flex;
	align-items: center;
	justify-content: center;
}

.VisorSpeed {
	text-align: center;
	font-weight: 600;
}

.VisorSpeed p {
	font-size: 5em;
}

.VisorSpeed > div {
	font-size: 1em;
	font-weight: 300;
	letter-spacing: 5px;
	color: #ccc !important;
	margin-top: 5px !important;
	margin-left: 5px !important;
	font-family: "Roboto" !important;
}

.NumMarch {
	position: absolute;
	top: 1.6em;
	font-size: 2em;
	color: #ccc;
}

.VisorSpeed p:nth-child(2) {
	font-weight: 500;
	font-size: 2.5em;
}

.stats {
	right: 0.5em;
	bottom: 2em;
	display: flex;
	height: 6.8em;
	position: absolute;
	align-items: flex-end;
	flex-direction: column;
	justify-content: space-between;
}

.FuelStats {
	height: 2em;
	width: 2em;
}

.car-stats {
	top: -1em;
	right: 1em;
	background: rgba(15,15,15,.75);
	height: 3em;
	width: 3em;
	border-radius: 50%;
	display: flex;
	justify-content: center;
	align-items: center;
	position: relative;
}

.car-stats .progress {
	height: 100%;
	width: 100%;
	border-radius: 50%;
}

.car-stats svg {
	position: absolute;
	padding: 0.2em;
	height: 2.75em;
	width: 2.75em;
	border-radius: 50%;
}

.stats-progress {
	background: #ff0000;
}

.stats-car {
	position: absolute;
	bottom: -1.2em;
	text-align: center;
}

.SpeedoIcons {
	height: 4em;
	width: 50%;
	position: absolute;
	display: flex;
	flex-flow: row wrap;
	justify-content: center;
	bottom: 3em;
	right: -0.2em;
	transform: translateX(-50%);
	gap: 6%;
}

.SpeedoIcons svg {
	width: 20%;
}

.speed, .march, .fuel {
	display: flex;
	justify-content: center;
	align-items: center;
	position: absolute;
	overflow: visible;
	transform-origin: center center;
}

.speed {
	height: 19em;
	width: 19em;
	right: 2em;
	top: -.5em;
	z-index: 20;
}

.speed circle {
	transform-origin: center;
	-webkit-transform: rotate(120deg);
	-moz-transform: rotate(120deg);
	-ms-transform: rotate(120deg);
	-o-transform: rotate(120deg);
	transform: rotate(120deg);
	transform-box: fill-box;
	stroke-dasharray: 440;
}

.speed circle, .march circle, .fuel circle {
	fill: transparent;
	stroke-width: .5em;
	stroke-dasharray: 440;
	transition: 200ms ease-in-out;
	stroke-dashoffset: calc(440 - (440 * 50) / 100);
}

.speed circle:nth-child(1) {
	stroke: #999;
	stroke-dashoffset: calc(440 - (440 * 46) / 100);
}

.speed circle:nth-child(2) {
	stroke: #fff;
}

.march {
	height: 17em;
	width: 17em;
	right: 2.8em;
	top: 1em;
}

.march circle {
	transform-origin: center;
	-webkit-transform: rotate(232deg);
	-moz-transform: rotate(232deg);
	-ms-transform: rotate(232deg);
	-o-transform: rotate(232deg);
	transform: rotate(232deg);
	transform-box: fill-box;
	stroke-dasharray: 440;
}

.march circle:nth-child(1) {
	stroke: #977217;
	stroke-dashoffset: calc(440 - (440 * 18) / 100);
}

.march circle:nth-child(2) {
	stroke: #fec026;
	stroke-dashoffset: calc(440 - (440 * 0) / 100);
}

.fuel {
	height: 19em;
	width: 19em;
	right: 2.2em;
	top: -0.8em;
}

.fuel circle {
	transform-origin: center;
	-webkit-transform: rotate(-41deg);
	-moz-transform: rotate(-41deg);
	-ms-transform: rotate(-41deg);
	-o-transform: rotate(-41deg);
	transform: rotate(-41deg);
	transform-box: fill-box;
	stroke-dasharray: 440;
}

.fuel circle:nth-child(1) {
	stroke: #db2a3b;
	stroke-dashoffset: calc(440 - (440 * 13) / 100);
}

.fuel circle:nth-child(2) {
	stroke: #999;
}

.Gray {
	fill: #ccc;
	color: #ccc;
}

.Yellow {
	fill: #fec026;
	color: #fec026;
}

.Green {
	fill: #83d52b;
	color: #83d52b;
}

.Red {
	fill: #f43252;
	color: #f43252;
}

.Blue {
	fill: #40a7f7;
	color: #40a7f7;
}

.Textform {
	color: #fff;
	padding: 15px;
	font-size: 13px;
	max-width: 250px;
	line-height: 15px;
	text-align: center;
	position: absolute;
	letter-spacing: 2px;
	background: #141414;
	word-wrap: break-word;
}